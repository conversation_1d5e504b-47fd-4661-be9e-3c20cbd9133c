{"colors": {"primary": "var(--cloud-color-primary)", "success": "var(--cloud-color-success)", "warning": "var(--cloud-color-warning)", "error": "var(--cloud-color-error)", "info": "var(--cloud-color-info)", "textBase": "var(--cloud-color-text-base)", "bgBase": "var(--cloud-color-bg-base)", "text": "var(--cloud-color-text)", "textSecondary": "var(--cloud-color-text-secondary)", "textTertiary": "var(--cloud-color-text-tertiary)", "textQuaternary": "var(--cloud-color-text-quaternary)", "fill": "var(--cloud-color-fill)", "fillSecondary": "var(--cloud-color-fill-secondary)", "fillTertiary": "var(--cloud-color-fill-tertiary)", "fillQuaternary": "var(--cloud-color-fill-quaternary)", "bgLayout": "var(--cloud-color-bg-layout)", "bgContainer": "var(--cloud-color-bg-container)", "bgElevated": "var(--cloud-color-bg-elevated)", "bgSpotlight": "var(--cloud-color-bg-spotlight)", "border": "var(--cloud-color-border)", "borderSecondary": "var(--cloud-color-border-secondary)", "primaryBg": "var(--cloud-color-primary-bg)", "primaryBgHover": "var(--cloud-color-primary-bg-hover)", "primaryBorder": "var(--cloud-color-primary-border)", "primaryBorderHover": "var(--cloud-color-primary-border-hover)", "primaryHover": "var(--cloud-color-primary-hover)", "primaryActive": "var(--cloud-color-primary-active)", "primaryTextHover": "var(--cloud-color-primary-text-hover)", "primaryText": "var(--cloud-color-primary-text)", "primaryTextActive": "var(--cloud-color-primary-text-active)", "successBg": "var(--cloud-color-success-bg)", "successBgHover": "var(--cloud-color-success-bg-hover)", "successBorder": "var(--cloud-color-success-border)", "successBorderHover": "var(--cloud-color-success-border-hover)", "successHover": "var(--cloud-color-success-hover)", "successActive": "var(--cloud-color-success-active)", "successTextHover": "var(--cloud-color-success-text-hover)", "successText": "var(--cloud-color-success-text)", "successTextActive": "var(--cloud-color-success-text-active)", "errorBg": "var(--cloud-color-error-bg)", "errorBgHover": "var(--cloud-color-error-bg-hover)", "errorBorder": "var(--cloud-color-error-border)", "errorBorderHover": "var(--cloud-color-error-border-hover)", "errorHover": "var(--cloud-color-error-hover)", "errorActive": "var(--cloud-color-error-active)", "errorTextHover": "var(--cloud-color-error-text-hover)", "errorText": "var(--cloud-color-error-text)", "errorTextActive": "var(--cloud-color-error-text-active)", "warningBg": "var(--cloud-color-warning-bg)", "warningBgHover": "var(--cloud-color-warning-bg-hover)", "warningBorder": "var(--cloud-color-warning-border)", "warningBorderHover": "var(--cloud-color-warning-border-hover)", "warningHover": "var(--cloud-color-warning-hover)", "warningActive": "var(--cloud-color-warning-active)", "warningTextHover": "var(--cloud-color-warning-text-hover)", "warningText": "var(--cloud-color-warning-text)", "warningTextActive": "var(--cloud-color-warning-text-active)", "infoBg": "var(--cloud-color-info-bg)", "infoBgHover": "var(--cloud-color-info-bg-hover)", "infoBorder": "var(--cloud-color-info-border)", "infoBorderHover": "var(--cloud-color-info-border-hover)", "infoHover": "var(--cloud-color-info-hover)", "infoActive": "var(--cloud-color-info-active)", "infoTextHover": "var(--cloud-color-info-text-hover)", "infoText": "var(--cloud-color-info-text)", "infoTextActive": "var(--cloud-color-info-text-active)", "bgMask": "var(--cloud-color-bg-mask)", "white": "var(--cloud-color-white)"}}