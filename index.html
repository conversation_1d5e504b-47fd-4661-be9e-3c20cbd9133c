<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <link rel="icon" type="image/svg+xml" href="/logo.png"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>云电脑</title>
    <script>
        // 百度统计
        // to delete, 测试环境用test.iqinghu.com
        if (window.location.hostname === 'www.iqinghu.com' ||
            window.location.hostname === 'iqinghu.com'
          ) {
            var _hmt = _hmt || [];
            (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?e1115e909c0d74526e5473e8692f251c";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();

            // 付费投流埋点
            window._agl = window._agl || [];
            (function () {
                _agl.push(
                    ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
                );
                (function () {
                    var agl = document.createElement('script');
                    agl.type = 'text/javascript';
                    agl.async = true;
                    agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(agl, s);
                })();
            })();
        }
    </script>
    <script>
        (function () {
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
            const setting = localStorage.getItem('vueuse-color-scheme') || 'auto'
            if (setting === 'dark' || (prefersDark && setting !== 'light'))
                document.documentElement.classList.toggle('dark', true)
        })()
    </script>
</head>
<body>
<div id="app"></div>
<script src="/loading.js"></script>
<script type="module" src="/src/main.ts"></script>
<script type="text/javascript" src="/lib/guacamole/guacamole-common.min.js"></script>
<script src="https://cdn.dingxiang-inc.com/ctu-group/captcha-ui/v5/index.js" crossorigin="anonymous"
        id="dx-captcha-script"></script>
</body>
</html>
