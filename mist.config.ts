import {defineConfig} from '@mistjs/cli'

export default defineConfig({
    // 关闭nitro服务
    nitro: false,
    vite: {
        resolve: {
            alias: [
                {
                    find: 'dayjs',
                    replacement: 'dayjs/esm',
                },
                {
                    find: /^dayjs\/locale/,
                    replacement: 'dayjs/esm/locale',
                },
                {
                    find: /^dayjs\/plugin/,
                    replacement: 'dayjs/esm/plugin',
                },
                {
                    find: /^ant-design-vue\/es$/,
                    replacement: 'ant-design-vue/es',
                },
                {
                    find: /^ant-design-vue\/dist$/,
                    replacement: 'ant-design-vue/dist',
                },
                {
                    find: /^ant-design-vue\/lib$/,
                    replacement: 'ant-design-vue/es',
                },
                {
                    find: /^ant-design-vue$/,
                    replacement: 'ant-design-vue/es',
                },
                {
                    find: 'lodash',
                    replacement: 'lodash-es',
                },
                {
                    find: /^@$/,
                    replacement: '/src',
                },
                {
                    find: /^~$/,
                    replacement: '/src',
                },
                {
                    find: /^~@$/,
                    replacement: '/src',
                },
                {
                    find: /^~#$/,
                    replacement: '/src/enums',
                }
            ]
        },
    },
})
