const express = require('express')
const connectHistoryApiFallback = require('connect-history-api-fallback')

const compression = require('compression')

var app = express()
var connectHistoryApiFallbackFunction = connectHistoryApiFallback()

function getClientIP (req) {
  var ips = req.headers['x-original-forwarded-for'] || // nginx-ingress 转发将真实IP放于此处 判断是否有反向代理 IP
      req.headers['x-forwarded-for'] || // 判断是否有反向代理 IP
      req.connection.remoteAddress || // 判断 connection 的远程 IP
      req.socket.remoteAddress || // 判断后端的 socket 的 IP
      req.connection.socket.remoteAddress
  if (ips.indexOf(',') > 0) {
    var ipStr = ips.split(',').filter(ip => ip)
    if (ipStr.length) {
      return ipStr[0]
    }
    return
  }
  return ips
};

app.use(compression());

app.use('/workbench', (req, res, next) => {
  var ip = getClientIP(req)
  res.cookie('curIp', ip, {
    httpOnly: false,
    overwrite: false
  })
  return connectHistoryApiFallbackFunction(req, res, next)
})
app.use('/workbench', express.static('./views'))
app.listen(3000, function () {
  // 3000为配置的端口号
  console.log('请访问http://localhost:3000/workbench/')
})
