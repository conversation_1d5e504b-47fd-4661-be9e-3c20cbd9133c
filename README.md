# 云电脑工作台

基于 Vue 3 + TypeScript + Vite 构建的云电脑管理和远程连接工作台。

## 功能特性

- 云电脑列表管理
- 远程桌面连接（基于Apache Guacamole）
- 云电脑开关机控制
- 桌面状态监控
- 多种连接方式（本地/网络）

## 技术栈

- Vue 3.4+（Composition API）
- TypeScript
- Vite 构建工具
- Ant Design Vue 4.x
- Vue Router
- Pinia
- Axios
- Less + UnoCSS
- Apache Guacamole（npm包：guacamole-common-js）

## 开发环境设置

1. 安装依赖

```bash
npm install
```

2. 启动开发服务器

```bash
npm run dev
```

3. 构建生产版本

```bash
npm run build
```

## 云电脑远程连接模块

### 功能说明

云电脑远程连接模块使用 Apache Guacamole 客户端库实现无插件的 HTML5 远程桌面连接功能。支持 RDP、VNC 和 SSH 等协议的远程连接。

### 使用方法

1. 在云电脑列表页中，点击操作栏中的"本地连接"或"网络连接"按钮
2. 系统将自动跳转到远程连接页面，并尝试建立连接
3. 连接成功后，可以进行远程桌面的操作

### Guacamole 集成说明

本项目使用 npm 包 `guacamole-common-js` 集成 Apache Guacamole 客户端功能：

```bash
# 安装 Guacamole 客户端库
pnpm add guacamole-common-js
# 或
npm install guacamole-common-js
```

在组件中使用：

```typescript
// 引入 Guacamole
import {Guacamole} from 'guacamole-common-js'

// 创建 WebSocket 隧道
const tunnel = new Guacamole.WebSocketTunnel(wsUrl)

// 创建 Guacamole 客户端
const client = new Guacamole.Client(tunnel)

// 连接
client.connect()
```

### 键盘快捷键

- F11: 全屏/退出全屏
- Ctrl+Alt+Delete: 发送组合键到远程系统

### API 说明

#### 1. 获取用户实例列表

```
GET /userInstance/list
```

参数:

- page: 当前页码
- pageSize: 每页大小
- status: 状态过滤
- name: 名称过滤

返回:

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": "1",
        "instanceName": "实例名称",
        "ipAddress": "***********",
        "region": "中国大陆-广州",
        "expireTime": "2025-04-12 (剩余30天)",
        "deviceStatus": "running",
        "remoteStatus": "off"
      }
    ],
    "total": 1
  },
  "msg": "成功",
  "success": true
}
```

#### 2. 连接实例会话

```
POST /userInstance/session/connect
```

参数:

```json
{
  "instanceId": "1",
  "connectType": "local"
}
```

返回:

```json
{
  "code": 0,
  "data": {
    "wsUrl": "ws://**************:8080/api/tunnel",
    "sessionToken": "432b940d-68fe-43ff-b3c1-0dde1458406a"
  },
  "msg": "成功",
  "success": true
}
```

## 注意事项

1. 使用远程连接功能需要确保Apache Guacamole服务器正常运行
2. 本地开发时，可使用提供的测试连接地址进行开发调试

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 项目使用

```bash
# 安装依赖
pnpm install
# 或
yarn install
# 或
npm install

# 启动项目
npm run dev

# 构建项目
npm run build

# 预览构建后的项目
npm run preview

# 代码检查
npm run lint

# 类型检查
npm run typecheck
```

## 项目结构

```
├── public/               # 静态资源目录
├── src/                  # 源代码目录
│   ├── api/              # API 接口定义
│   ├── assets/           # 静态资源 (图片、字体等)
│   ├── components/       # 公共组件
│   ├── composables/      # 组合式函数
│   ├── config/           # 全局配置
│   ├── directive/        # 自定义指令
│   ├── enums/            # 枚举定义
│   ├── hooks/            # 自定义钩子
│   ├── Icons/            # 图标组件
│   ├── layouts/          # 布局组件
│   ├── locales/          # 国际化文件
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── utils/            # 工具函数
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── types/                # 全局类型定义
├── plugins/              # 项目插件
├── scripts/              # 脚本文件
├── themes/               # 主题配置
└── ... 其他配置文件
```

## API 封装

项目使用 Axios 进行 HTTP 请求封装，主要特点如下：

### 请求封装

项目在 `src/utils/request.ts` 中对 Axios 进行了封装，提供了以下功能：

1. **统一响应格式**：所有 API 响应遵循 `ResponseBody<T>` 接口格式
   ```typescript
   interface ResponseBody<T = any> {
       code: number      // 状态码
       data?: T          // 响应数据
       msg: string       // 响应消息
       success: boolean  // 是否成功
   }
   ```

2. **请求方法封装**：提供了 `useGet`、`usePost`、`usePut`、`useDelete` 等方法
   ```typescript
   // GET 请求示例
   const data = await useGet<UserInfo>('/api/user/info')

   // POST 请求示例
   const result = await usePost<LoginResultModel, LoginParams>('/auth/signin', loginParams, {
       token: false,
       loading: true
   })
   ```

3. **请求配置扩展**：支持额外的请求配置选项
   ```typescript
   interface RequestConfigExtra {
       token?: boolean      // 是否携带 token
       customDev?: boolean  // 是否使用自定义开发环境
       loading?: boolean    // 是否显示加载状态
       baseServer?: string  // 基础服务器地址
       hasRegion?: boolean  // 是否包含区域信息
       isForeAddBody?: boolean // 是否前置添加请求体
   }
   ```

4. **全局拦截器**：处理请求和响应拦截
    - 请求拦截器：自动添加 token、多语言配置
    - 响应拦截器：统一处理错误，如 401 未授权自动跳转登录页

### API 定义规范

API 接口定义遵循以下规范：

1. **按模块组织**：API 接口按功能模块存放在 `src/api` 目录
2. **类型定义**：为请求参数和响应数据定义 TypeScript 接口
3. **函数命名**：API 函数使用 `xxxApi` 命名方式

示例：

```typescript
// 请求参数接口
export interface LoginParams {
    passwordPayload: {
        username: string
        password: string
    }
    connection: CONNECTION_TYPE.PASSWORD
    options?: {
        autoRegister?: boolean
        captchaCode?: string
    }
}

// 响应接口
export interface LoginResultModel {
    token: string
}

// API 函数
export function loginApi(params: LoginParams) {
    return usePost<LoginResultModel, LoginParams>('/auth/signin', params, {
        token: false,
        loading: true,
    })
}
```

## 代码规范

### 组件开发规范

1. **使用 `<script setup lang="ts">` 语法**
   ```vue
   <script setup lang="ts">
   // 组件逻辑
   </script>
   ```

2. **组件名使用 PascalCase**
   ```
   src/components/UserAvatar.vue
   ```

3. **类型定义**：Props 和 Emits 必须声明类型
   ```typescript
   const props = defineProps<{
     username: string
     avatar?: string
   }>()

   const emit = defineEmits<{
     'update:avatar': [value: string]
     'login': [username: string]
   }>()
   ```

4. **响应式数据**
   ```typescript
   // 原始值使用 ref
   const count = ref(0)
   // 对象使用 reactive
   const user = reactive({ name: '', age: 0 })
   // 大型数据结构使用 shallowRef
   const tableData = shallowRef<Record<string, any>[]>([])
   ```

### 状态管理规范

1. **使用 Pinia 进行状态管理**
2. **按功能模块拆分 store**
3. **使用组合式函数方式定义 store**
   ```typescript
   export const useUserStore = defineStore('user', () => {
     const userInfo = shallowRef<UserInfo>()
     const token = useAuthorization()

     const nickname = computed(() => userInfo.value?.nickname)

     async function getUserInfo() {
       try {
         const {data: user} = await getUserInfoApi<UserInfo>()
         userInfo.value = user || {}
         return user
       } catch (e) {
         console.error(e)
       }
     }

     return {
       userInfo,
       getUserInfo,
       nickname,
     }
   })
   ```

4. **使用 storeToRefs 解构响应式状态**
   ```typescript
   const userStore = useUserStore()
   const { userInfo } = storeToRefs(userStore)
   ```

### 代码风格

1. **命名规范**
    - 组件文件：PascalCase (如 UserAvatar.vue)
    - 页面组件：index.vue (如 dashboard/workbench/index.vue)
    - 变量/函数：camelCase
    - 常量：UPPER_CASE
    - 类型/接口：PascalCase
    - Boolean 类型变量使用 is/has/should 等前缀

2. **注释规范**
    - 组件顶部添加功能说明注释
    - 复杂逻辑添加必要的注释说明
    - API 接口添加参数和返回值注释

## 最佳实践

1. **组件设计**
    - 保持组件的单一职责
    - 组件间通信优先使用 props/emits
    - 复杂组件考虑使用插槽实现可定制化

2. **性能优化**
    - 使用 `shallowRef` 优化大型数据结构
    - 合理使用 `computed` 缓存计算结果
    - 长列表使用虚拟滚动
    - 路由组件使用懒加载

3. **异常处理**
    - API 请求使用 try/catch 捕获异常
    - 使用 message 或 notification 提示用户错误信息

4. **国际化**
    - 使用 `useI18nLocale()` 组合式函数处理国际化
    - 字符串使用 t() 函数包装

5. **表单处理**
    - 使用 a-form 和 a-form-item 处理表单
    - 表单验证使用 async-validator 规则
    - 提交前使用 await formRef.value.validate() 验证
