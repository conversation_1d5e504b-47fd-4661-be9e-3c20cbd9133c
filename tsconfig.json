{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "jsxImportSource": "vue",
    /* Linting */
    "strict": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    /* Importing .vue files */
    // 解决vscode 提示找不到模块的问题
    "types": [
      "vite/client"
    ],
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strictNullChecks": false,
    // paths
    "baseUrl": ".",
    // 修改这里，移除 /cloud-desktop-workbench
    "paths": {
      "~@/*": [
        "src/*"
      ],
      "~/*": [
        "src/*"
      ],
      "@/*": [
        "src/*"
      ],
      "~#/*": [
        "src/enums/*"
      ],
      "@v-c/utils": [
        "node_modules/@v-c/utils"
      ]
    },
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/*.d.ts"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
