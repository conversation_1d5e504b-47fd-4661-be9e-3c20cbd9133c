---
description:
globs:
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

# Cloud Desktop Workbench 项目规则

## 技术栈

### 前端框架与核心库
- Vue 3.4+ (使用 Composition API)
- TypeScript
- Vite 5.x 作为构建工具
- Pinia 用于状态管理
- Vue Router 用于路由管理
- Vue I18n 用于国际化

### UI 框架与组件
- Ant Design Vue 4.x
- Ant Design Icons Vue
- AntV G2Plot 和 L7 用于数据可视化

### 工具库
- Axios 用于 HTTP 请求
- Lodash-es 用于工具函数
- Dayjs 用于日期处理
- Decimal.js 用于精确数值计算
- VueUse 用于组合式函数

### 样式解决方案
- Less 预处理器
- UnoCSS 原子化 CSS 框架
- 支持主题定制

## 代码规范

### 项目结构规范
- 使用 `src` 作为源代码根目录
- 使用 `types` 目录存放类型定义
- 使用 `plugins` 目录存放插件
- 使用 `public` 目录存放静态资源

### 组件开发规范
- 使用 `<script setup>` 语法
- 组件名使用 PascalCase
- 使用 TypeScript 类型注解
- props 和 emits 必须声明类型
- 遵循 Vue 3 组合式 API 最佳实践

### 样式规范
- 使用 Less 预处理器
- 优先使用 UnoCSS 原子化类
- 组件样式使用 scoped
- 遵循 BEM 命名规范

### 状态管理规范
- 使用 Pinia 进行状态管理
- 按功能模块拆分 store
- 使用 TypeScript 定义 store 类型
- 不允许在组件中直接修改 store 状态

### TypeScript 使用规范
- 优先使用接口定义复杂数据结构
- 使用类型别名简化类型定义
- 枚举类型存放在 src/enums 目录
- 使用 TypeScript 工具类型提高代码复用

### 路径别名
- `@/*` 指向 src 目录
- `~/*` 指向 src 目录
- `~@/*` 指向 src 目录
- `~#/*` 指向 src/enums 目录

### API 请求规范
- 使用 Axios 进行请求
- API 接口集中管理
- 使用拦截器处理通用逻辑
- 请求函数返回 Promise 类型

### 代码质量规范
- 遵循 SOLID 原则
- 组件和函数职责单一
- 提取通用逻辑到可复用的组合式函数
- 避免过深的组件嵌套

### Git 工作流规范
- 提交前通过 ESLint 检查
- 遵循 Conventional Commits 规范
- 重要功能使用分支开发

## 文件命名规范

### 组件文件
- 使用 PascalCase 命名，如 `UserProfile.vue`
- 页面组件放在 pages 目录，如 `src/pages/dashboard/user/index.vue`
- 公共组件放在 components 目录

### 其他文件
- 使用 kebab-case 命名，如 `user-api.ts`
- 工具函数文件使用 camelCase 命名，如 `formatDate.ts`
- 类型定义文件使用 PascalCase 命名，如 `UserTypes.ts`

## 最佳实践

### 性能优化
- 组件按需引入
- 大型依赖使用动态导入
- 使用 Vue 内置的性能优化 API

### 代码可维护性
- 模块化设计
- 合理的代码注释
- 复杂逻辑提取成独立函数
- 避免魔法数字和字符串，使用常量

### 国际化
- 所有用户可见文本使用 i18n
- 翻译文件按模块组织
- 支持动态切换语言
