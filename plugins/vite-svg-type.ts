import type {Plugin} from 'vite'
import {writeFileSync, existsSync, mkdirSync} from 'fs'
import {resolve} from 'path'

/**
 * 自动生成SVG类型声明文件的Vite插件
 */
export function viteSvgTypesPlugin(): Plugin {
    const typesDir = resolve(process.cwd(), 'types')
    const svgTypesPath = resolve(typesDir, 'svg.d.ts')

    const svgTypeContent = `declare module '*.svg' {
  import { FunctionalComponent, SVGAttributes } from 'vue'
  const component: FunctionalComponent<SVGAttributes>
  export default component
}

declare module '*.svg?component' {
  import { FunctionalComponent, SVGAttributes } from 'vue'
  const component: FunctionalComponent<SVGAttributes>
  export default component
}

declare module '*.svg?raw' {
  const content: string
  export default content
}

declare module '*.svg?url' {
  const url: string
  export default url
}
`

    return {
        name: 'vite-plugin-svg-types',
        configResolved() {
            // 确保types目录存在
            if (!existsSync(typesDir)) {
                mkdirSync(typesDir, {recursive: true})
            }

            // 写入类型声明文件
            writeFileSync(svgTypesPath, svgTypeContent, 'utf-8')
            console.log('✅ SVG类型声明文件已生成: types/svg.d.ts')
        }
    }
} 