{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "packageManager": "pnpm@8.10.0", "author": "q<PERSON>hu", "engines": {"node": ">=18.20.2"}, "scripts": {"dev": "vite", "build": "vite build", "build:nitro": "mist build nitro", "start:nirto": "node .output/server/index.mjs", "preview": "mist preview", "lint": "eslint src --fix", "typecheck": "vue-tsc --noEmit", "prepare": "husky", "dir-tree": "node --import tsx scripts/dir-tree", "gen:uno": "node --import tsx scripts/gen-unocss", "toJS": "node --import tsx scripts/to-js", "build:minify": "minify public/lib/guacamole/guacamole-common.js > public/lib/guacamole/guacamole-common.min.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.31", "@antv/l7": "^2.20.20", "@ctrl/tinycolor": "^4.0.3", "@v-c/utils": "^0.0.26", "@vueuse/core": "^10.9.0", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "core-js": "^3.42.0", "regenerator-runtime": "^0.13.11", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "express": "^4.18.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-i18n": "^9.10.1", "vue-router": "^4.3.0"}, "devDependencies": {"@antfu/eslint-config": "^2.8.1", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@mistjs/cli": "0.0.1-beta.7", "@mistjs/vite-plugin-preload": "^0.0.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.26", "@types/treeify": "^1.0.3", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/test-utils": "^2.4.4", "antdv-component-resolver": "^1.0.7", "antdv-style": "0.0.1-beta.2", "changelogen": "^0.5.5", "cross-env": "^7.0.3", "directory-tree": "^3.5.1", "esbuild": "^0.20.1", "eslint": "^8.57.0", "eslint-import-resolver-alias": "^1.1.2", "esno": "^0.16.3", "execa": "^8.0.1", "fs-extra": "^11.2.0", "husky": "^9.0.11", "jsdom": "^22.1.0", "less": "^4.2.0", "lint-staged": "^14.0.1", "lodash": "^4.17.21", "minify": "^9.2.0", "nitropack": "^2.9.3", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss-preset-env": "^10.2.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.6.0", "treeify": "^1.1.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.4.2", "uno-prefix-variant": "^0.0.3", "unocss": "^0.57.7", "unocss-preset-chinese": "^0.3.2", "unocss-preset-ease": "^0.0.3", "unplugin-auto-import": "^0.16.7", "unplugin-config": "^0.1.4", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.6", "vite-plugin-babel": "^1.3.1", "vite-svg-loader": "^5.1.0", "vitest": "^0.34.6", "vue-tsc": "^2.0.6"}, "lint-staged": {"**/*.{vue,ts,js,jsx,tsx}": "eslint src --fix"}, "browserslist": ["Chrome >= 79"]}