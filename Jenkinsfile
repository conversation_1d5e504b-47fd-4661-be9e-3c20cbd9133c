pipeline {
    agent any
    environment {
       GIT_URL="https://codeup.aliyun.com/6746e85c894777dc14f966f7/qinghu-cloud/frontend/qinghu-cloud-workbench.git"
    }
    parameters {
            gitParameter (branch: '', branchFilter: 'origin/(.*)', defaultValue: 'test', name: 'BRANCH', type: 'PT_BRANCH', useRepository: "https://codeup.aliyun.com/6746e85c894777dc14f966f7/qinghu-cloud/frontend/qinghu-cloud-workbench.git")
            choice(name: 'ENV', choices: ['test', 'production'], description: '环境')
    }
    stages {
        stage('Git') {
           steps {
              checkout([$class: 'GitSCM',branches: [[name: "$BRANCH"]],
               userRemoteConfigs: [[url: "${env.GIT_URL}", credentialsId: "ca22a3a7-d002-419b-9ff2-6380e1f6accd"]]])
           }
        }
        stage('Build') {
            steps {
                script {
                    sh "docker build -t ccr.ccs.tencentyun.com/menglar/cloud-desktop-front:${params.ENV} ."
                }
            }
        }
        stage('Push') {
            steps {
                script {
                    sh "docker push ccr.ccs.tencentyun.com/menglar/cloud-desktop-front:${params.ENV}"
                }
            }
        }
    }
}
