var Guacamole;(Guacamole=Guacamole||{}).ArrayBufferReader=function(e){var t=this;e.onblob=function(e){for(var n=window.atob(e),a=new ArrayBuffer(n.length),o=new Uint8Array(a),r=0;r<n.length;r++)o[r]=n.charCodeAt(r);t.ondata&&t.ondata(a)},e.onend=function(){t.onend&&t.onend()},this.ondata=null,this.onend=null},(Guacamole=Guacamole||{}).ArrayBufferWriter=function(e){var t=this;function n(t){for(var n="",a=0;a<t.byteLength;a++)n+=String.fromCharCode(t[a]);e.sendBlob(window.btoa(n))}e.onack=function(e){t.onack&&t.onack(e)},this.blobLength=Guacamole.ArrayBufferWriter.DEFAULT_BLOB_LENGTH,this.sendData=function(e){var a=new Uint8Array(e);if(a.length<=t.blobLength)n(a);else for(var o=0;o<a.length;o+=t.blobLength)n(a.subarray(o,o+t.blobLength))},this.sendEnd=function(){e.sendEnd()},this.onack=null},Guacamole.ArrayBufferWriter.DEFAULT_BLOB_LENGTH=6048,(Guacamole=Guacamole||{}).AudioContextFactory={singleton:null,getAudioContext:function(){var e=window.AudioContext||window.webkitAudioContext;if(e)try{return Guacamole.AudioContextFactory.singleton||(Guacamole.AudioContextFactory.singleton=new e),Guacamole.AudioContextFactory.singleton}catch(e){}return null}},(Guacamole=Guacamole||{}).AudioPlayer=function(){this.sync=function(){}},Guacamole.AudioPlayer.isSupportedType=function(e){return Guacamole.RawAudioPlayer.isSupportedType(e)},Guacamole.AudioPlayer.getSupportedTypes=function(){return Guacamole.RawAudioPlayer.getSupportedTypes()},Guacamole.AudioPlayer.getInstance=function(e,t){return Guacamole.RawAudioPlayer.isSupportedType(t)?new Guacamole.RawAudioPlayer(e,t):null},Guacamole.RawAudioPlayer=function(e,t){var n=Guacamole.RawAudioFormat.parse(t),a=Guacamole.AudioContextFactory.getAudioContext(),o=a.currentTime,r=new Guacamole.ArrayBufferReader(e),i=1===n.bytesPerSample?window.Int8Array:window.Int16Array,s=1===n.bytesPerSample?128:32768,u=[],l=function(){var e=function(e){if(e.length<=1)return e[0];var t=0;e.forEach((function(e){t+=e.length}));var n=0,a=new i(t);return e.forEach((function(e){a.set(e,n),n+=e.length})),a}(u);return e?(u=function(e){for(var t=Number.MAX_VALUE,a=e.length,o=Math.floor(e.length/n.channels),r=Math.floor(.02*n.rate),s=Math.max(n.channels*r,n.channels*(o-r));s<e.length;s+=n.channels){for(var u=0,l=0;l<n.channels;l++)u+=Math.abs(e[s+l]);u<=t&&(a=s+n.channels,t=u)}return a===e.length?[e]:[new i(e.buffer.slice(0,a*n.bytesPerSample)),new i(e.buffer.slice(a*n.bytesPerSample))]}(e),e=u.shift()):null};r.ondata=function(e){!function(e){u.push(new i(e))}(new i(e));var t=l();if(t){var r=a.currentTime;o<r&&(o=r);var c=a.createBufferSource();c.connect(a.destination),c.start||(c.start=c.noteOn),c.buffer=function(e){var t=e.length/n.channels,r=a.currentTime;o<r&&(o=r);for(var i=a.createBuffer(n.channels,t,n.rate),u=0;u<n.channels;u++)for(var l=i.getChannelData(u),c=u,d=0;d<t;d++)l[d]=e[c]/s,c+=n.channels;return i}(t),c.start(o),o+=t.length/n.channels/n.rate}},this.sync=function(){var e=a.currentTime;o=Math.min(o,e+.3)}},Guacamole.RawAudioPlayer.prototype=new Guacamole.AudioPlayer,Guacamole.RawAudioPlayer.isSupportedType=function(e){return!!Guacamole.AudioContextFactory.getAudioContext()&&null!==Guacamole.RawAudioFormat.parse(e)},Guacamole.RawAudioPlayer.getSupportedTypes=function(){return Guacamole.AudioContextFactory.getAudioContext()?["audio/L8","audio/L16"]:[]},(Guacamole=Guacamole||{}).AudioRecorder=function(){this.onclose=null,this.onerror=null},Guacamole.AudioRecorder.isSupportedType=function(e){return Guacamole.RawAudioRecorder.isSupportedType(e)},Guacamole.AudioRecorder.getSupportedTypes=function(){return Guacamole.RawAudioRecorder.getSupportedTypes()},Guacamole.AudioRecorder.getInstance=function(e,t){return Guacamole.RawAudioRecorder.isSupportedType(t)?new Guacamole.RawAudioRecorder(e,t):null},Guacamole.RawAudioRecorder=function(e,t){var n=this,a=Guacamole.RawAudioFormat.parse(t),o=Guacamole.AudioContextFactory.getAudioContext();navigator.mediaDevices||(navigator.mediaDevices={}),navigator.mediaDevices.getUserMedia||(navigator.mediaDevices.getUserMedia=(navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia).bind(navigator));var r=new Guacamole.ArrayBufferWriter(e),i=1===a.bytesPerSample?window.Int8Array:window.Int16Array,s=1===a.bytesPerSample?128:32768,u=0,l=0,c=null,d=null,h=null,f=function(e){if(0===e)return 1;var t=Math.PI*e;return Math.sin(t)/t},m=function(e,t){for(var n,a,o=(e.length-1)*t,r=Math.floor(o)-3+1,i=Math.floor(o)+3,s=0,u=r;u<=i;u++)s+=(e[u]||0)*(-(a=3)<(n=o-u)&&n<a?f(n)*f(n/a):0);return s},p=function(e){(h=o.createScriptProcessor(2048,a.channels,a.channels)).connect(o.destination),h.onaudioprocess=function(e){r.sendData(function(e){var t=e.length;u+=t;var n=Math.round(u*a.rate/e.sampleRate)-l;l+=n;for(var o=new i(n*a.channels),r=0;r<a.channels;r++)for(var c=e.getChannelData(r),d=r,h=0;h<n;h++)o[d]=m(c,h/(n-1))*s,d+=a.channels;return o}(e.inputBuffer).buffer)},(d=o.createMediaStreamSource(e)).connect(h),"suspended"===o.state&&o.resume(),c=e},g=function(){r.sendEnd(),n.onerror&&n.onerror()};r.onack=function(e){var t;e.code!==Guacamole.Status.Code.SUCCESS||c?(!function(){if(d&&d.disconnect(),h&&h.disconnect(),c)for(var e=c.getTracks(),t=0;t<e.length;t++)e[t].stop();h=null,d=null,c=null,r.sendEnd()}(),r.onack=null,e.code===Guacamole.Status.Code.RESOURCE_CLOSED?n.onclose&&n.onclose():n.onerror&&n.onerror()):(t=navigator.mediaDevices.getUserMedia({audio:!0},p,g))&&t.then&&t.then(p,g)}},Guacamole.RawAudioRecorder.prototype=new Guacamole.AudioRecorder,Guacamole.RawAudioRecorder.isSupportedType=function(e){return!!Guacamole.AudioContextFactory.getAudioContext()&&null!==Guacamole.RawAudioFormat.parse(e)},Guacamole.RawAudioRecorder.getSupportedTypes=function(){return Guacamole.AudioContextFactory.getAudioContext()?["audio/L8","audio/L16"]:[]},(Guacamole=Guacamole||{}).BlobReader=function(e,t){var n,a=this,o=0;n=window.BlobBuilder?new BlobBuilder:window.WebKitBlobBuilder?new WebKitBlobBuilder:window.MozBlobBuilder?new MozBlobBuilder:new function(){var e=[];this.append=function(n){e.push(new Blob([n],{type:t}))},this.getBlob=function(){return new Blob(e,{type:t})}},e.onblob=function(t){for(var r=window.atob(t),i=new ArrayBuffer(r.length),s=new Uint8Array(i),u=0;u<r.length;u++)s[u]=r.charCodeAt(u);n.append(i),o+=i.byteLength,a.onprogress&&a.onprogress(i.byteLength),e.sendAck("OK",0)},e.onend=function(){a.onend&&a.onend()},this.getLength=function(){return o},this.getBlob=function(){return n.getBlob()},this.onprogress=null,this.onend=null},(Guacamole=Guacamole||{}).BlobWriter=function(e){var t=this,n=new Guacamole.ArrayBufferWriter(e);n.onack=function(e){t.onack&&t.onack(e)};this.sendBlob=function(e){var a=0,o=new FileReader,r=function(){if(a>=e.size)t.oncomplete&&t.oncomplete(e);else{var r=function(e,t,n){var a=(e.slice||e.webkitSlice||e.mozSlice).bind(e),o=n-t;if(o!==n){var r=a(t,o);if(r.size===o)return r}return a(t,n)}(e,a,a+n.blobLength);a+=n.blobLength,o.readAsArrayBuffer(r)}};o.onload=function(){n.sendData(o.result),n.onack=function(o){t.onack&&t.onack(o),o.isError()||(t.onprogress&&t.onprogress(e,a-n.blobLength),r())}},o.onerror=function(){t.onerror&&t.onerror(e,a,o.error)},r()},this.sendEnd=function(){n.sendEnd()},this.onack=null,this.onerror=null,this.onprogress=null,this.oncomplete=null},(Guacamole=Guacamole||{}).Client=function(e){var t=this,n=0,a=0,o=null,r={0:"butt",1:"round",2:"square"},i={0:"bevel",1:"miter",2:"round"},s=new Guacamole.Display,u={},l={},c={},d=[],h=[],f=[],m=new Guacamole.IntegerPool,p=[];function g(e){e!=n&&(n=e,t.onstatechange&&t.onstatechange(n))}function v(){return 3==n||2==n}this.exportState=function(e){var t={currentState:n,currentTimestamp:a,layers:{}},o={};for(var r in u)o[r]=u[r];s.flush((function(){for(var n in o){var a=parseInt(n),r=o[n],i=r.toCanvas(),s={width:r.width,height:r.height};r.width&&r.height&&(s.url=i.toDataURL("image/png")),a>0&&(s.x=r.x,s.y=r.y,s.z=r.z,s.alpha=r.alpha,s.matrix=r.matrix,s.parent=w(r.parent)),t.layers[n]=s}e(t)}))},this.importState=function(e,t){var o,r;for(o in n=e.currentState,a=e.currentTimestamp,u)(r=parseInt(o))>0&&s.dispose(u[o]);for(o in u={},e.layers){r=parseInt(o);var i=e.layers[o],l=y(r);if(s.resize(l,i.width,i.height),i.url&&(s.setChannelMask(l,Guacamole.Layer.SRC),s.draw(l,0,0,i.url)),r>0&&i.parent>=0){var c=y(i.parent);s.move(l,c,i.x,i.y,i.z),s.shade(l,i.alpha);var d=i.matrix;s.distort(l,d[0],d[1],d[2],d[3],d[4],d[5])}}s.flush(t)},this.getDisplay=function(){return s},this.sendSize=function(t,n){v()&&e.sendMessage("size",t,n)},this.sendKeyEvent=function(t,n){v()&&e.sendMessage("key",n,t)},this.sendMouseState=function(t){if(v()){s.moveCursor(Math.floor(t.x),Math.floor(t.y));var n=0;t.left&&(n|=1),t.middle&&(n|=2),t.right&&(n|=4),t.up&&(n|=8),t.down&&(n|=16),e.sendMessage("mouse",Math.floor(t.x),Math.floor(t.y),n)}},this.createOutputStream=function(){var e=m.next();return p[e]=new Guacamole.OutputStream(t,e)},this.createAudioStream=function(n){var a=t.createOutputStream();return e.sendMessage("audio",a.index,n),a},this.createFileStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("file",o.index,n,a),o},this.createPipeStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("pipe",o.index,n,a),o},this.createClipboardStream=function(n){var a=t.createOutputStream();return e.sendMessage("clipboard",a.index,n),a},this.createArgumentValueStream=function(n,a){var o=t.createOutputStream();return e.sendMessage("argv",o.index,n,a),o},this.createObjectOutputStream=function(n,a,o){var r=t.createOutputStream();return e.sendMessage("put",n,r.index,a,o),r},this.requestObjectInputStream=function(t,n){v()&&e.sendMessage("get",t,n)},this.sendAck=function(t,n,a){v()&&e.sendMessage("ack",t,n,a)},this.sendBlob=function(t,n){v()&&e.sendMessage("blob",t,n)},this.endStream=function(t){v()&&(e.sendMessage("end",t),p[t]&&(m.free(t),delete p[t]))},this.onstatechange=null,this.onname=null,this.onerror=null,this.onaudio=null,this.onvideo=null,this.onargv=null,this.onclipboard=null,this.onfile=null,this.onfilesystem=null,this.onpipe=null,this.onrequired=null,this.onsync=null;var y=function(e){var t=u[e];return t||(t=0===e?s.getDefaultLayer():e>0?s.createLayer():s.createBuffer(),u[e]=t),t},w=function(e){if(!e)return null;for(var t in u)if(e===u[t])return parseInt(t);return null};var S={"miter-limit":function(e,t){s.setMiterLimit(e,parseFloat(t))}},T={ack:function(e){var t=parseInt(e[0]),n=e[1],a=parseInt(e[2]),o=p[t];o&&(o.onack&&o.onack(new Guacamole.Status(a,n)),a>=256&&p[t]===o&&(m.free(t),delete p[t]))},arc:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseFloat(e[4]),i=parseFloat(e[5]),u=parseInt(e[6]);s.arc(t,n,a,o,r,i,0!=u)},argv:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onargv){var r=h[n]=new Guacamole.InputStream(t,n);t.onargv(r,a,o)}else t.sendAck(n,"Receiving argument values unsupported",256)},audio:function(e){var n=parseInt(e[0]),a=e[1],o=h[n]=new Guacamole.InputStream(t,n),r=null;t.onaudio&&(r=t.onaudio(o,a)),r||(r=Guacamole.AudioPlayer.getInstance(o,a)),r?(l[n]=r,t.sendAck(n,"OK",0)):t.sendAck(n,"BAD TYPE",783)},blob:function(e){var t=parseInt(e[0]),n=e[1],a=h[t];a&&a.onblob&&a.onblob(n)},body:function(e){var n=parseInt(e[0]),a=f[n],o=parseInt(e[1]),r=e[2],i=e[3];if(a&&a.onbody){var s=h[o]=new Guacamole.InputStream(t,o);a.onbody(s,r,i)}else t.sendAck(o,"Receipt of body unsupported",256)},cfill:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]),i=parseInt(e[5]);s.setChannelMask(n,t),s.fillColor(n,a,o,r,i)},clip:function(e){var t=y(parseInt(e[0]));s.clip(t)},clipboard:function(e){var n=parseInt(e[0]),a=e[1];if(t.onclipboard){var o=h[n]=new Guacamole.InputStream(t,n);t.onclipboard(o,a)}else t.sendAck(n,"Clipboard unsupported",256)},close:function(e){var t=y(parseInt(e[0]));s.close(t)},copy:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]),i=parseInt(e[5]),u=y(parseInt(e[6])),l=parseInt(e[7]),c=parseInt(e[8]);s.setChannelMask(u,i),s.copy(t,n,a,o,r,u,l,c)},cstroke:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=r[parseInt(e[2])],o=i[parseInt(e[3])],u=parseInt(e[4]),l=parseInt(e[5]),c=parseInt(e[6]),d=parseInt(e[7]),h=parseInt(e[8]);s.setChannelMask(n,t),s.strokeColor(n,a,o,u,l,c,d,h)},cursor:function(e){var t=parseInt(e[0]),n=parseInt(e[1]),a=y(parseInt(e[2])),o=parseInt(e[3]),r=parseInt(e[4]),i=parseInt(e[5]),u=parseInt(e[6]);s.setCursor(t,n,a,o,r,i,u)},curve:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]),i=parseInt(e[5]),u=parseInt(e[6]);s.curveTo(t,n,a,o,r,i,u)},disconnect:function(e){t.disconnect()},dispose:function(e){var t=parseInt(e[0]);if(t>0){var n=y(t);s.dispose(n),delete u[t]}else t<0&&delete u[t]},distort:function(e){var t=parseInt(e[0]),n=parseFloat(e[1]),a=parseFloat(e[2]),o=parseFloat(e[3]),r=parseFloat(e[4]),i=parseFloat(e[5]),u=parseFloat(e[6]);if(t>=0){var l=y(t);s.distort(l,n,a,o,r,i,u)}},error:function(e){var n=e[0],a=parseInt(e[1]);t.onerror&&t.onerror(new Guacamole.Status(a,n)),t.disconnect()},end:function(e){var t=parseInt(e[0]),n=h[t];n&&(n.onend&&n.onend(),delete h[t])},file:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onfile){var r=h[n]=new Guacamole.InputStream(t,n);t.onfile(r,a,o)}else t.sendAck(n,"File transfer unsupported",256)},filesystem:function(e){var n=parseInt(e[0]),a=e[1];if(t.onfilesystem){var o=f[n]=new Guacamole.Object(t,n);t.onfilesystem(o,a)}},identity:function(e){var t=y(parseInt(e[0]));s.setTransform(t,1,0,0,1,0,0)},img:function(e){var n=parseInt(e[0]),a=parseInt(e[1]),o=y(parseInt(e[2])),r=e[3],i=parseInt(e[4]),u=parseInt(e[5]),l=h[n]=new Guacamole.InputStream(t,n);s.setChannelMask(o,a),s.drawStream(o,i,u,l,r)},jpeg:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),r=e[4];s.setChannelMask(n,t),s.draw(n,a,o,"data:image/jpeg;base64,"+r)},lfill:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=y(parseInt(e[2]));s.setChannelMask(n,t),s.fillLayer(n,a)},line:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]);s.lineTo(t,n,a)},lstroke:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=y(parseInt(e[2]));s.setChannelMask(n,t),s.strokeLayer(n,a)},mouse:function(e){var t=parseInt(e[0]),n=parseInt(e[1]);s.showCursor(!0),s.moveCursor(t,n)},move:function(e){var t=parseInt(e[0]),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]);if(t>0&&n>=0){var i=y(t),u=y(n);s.move(i,u,a,o,r)}},name:function(e){t.onname&&t.onname(e[0])},nest:function(t){(function(t){var n=d[t];return null==n&&((n=d[t]=new Guacamole.Parser).oninstruction=e.oninstruction),n})(parseInt(t[0])).receive(t[1])},pipe:function(e){var n=parseInt(e[0]),a=e[1],o=e[2];if(t.onpipe){var r=h[n]=new Guacamole.InputStream(t,n);t.onpipe(r,a,o)}else t.sendAck(n,"Named pipes unsupported",256)},png:function(e){var t=parseInt(e[0]),n=y(parseInt(e[1])),a=parseInt(e[2]),o=parseInt(e[3]),r=e[4];s.setChannelMask(n,t),s.draw(n,a,o,"data:image/png;base64,"+r)},pop:function(e){var t=y(parseInt(e[0]));s.pop(t)},push:function(e){var t=y(parseInt(e[0]));s.push(t)},rect:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]);s.rect(t,n,a,o,r)},required:function(e){t.onrequired&&t.onrequired(e)},reset:function(e){var t=y(parseInt(e[0]));s.reset(t)},set:function(e){var t=y(parseInt(e[0])),n=e[1],a=e[2],o=S[n];o&&o(t,a)},shade:function(e){var t=parseInt(e[0]),n=parseInt(e[1]);if(t>=0){var a=y(t);s.shade(a,n)}},size:function(e){var t=parseInt(e[0]),n=y(t),a=parseInt(e[1]),o=parseInt(e[2]);s.resize(n,a,o)},start:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]);s.moveTo(t,n,a)},sync:function(o){var r=parseInt(o[0]);s.flush((function(){for(var t in l){var n=l[t];n&&n.sync()}r!==a&&(e.sendMessage("sync",r),a=r)})),2===n&&g(3),t.onsync&&t.onsync(r)},transfer:function(e){var t=y(parseInt(e[0])),n=parseInt(e[1]),a=parseInt(e[2]),o=parseInt(e[3]),r=parseInt(e[4]),i=parseInt(e[5]),u=y(parseInt(e[6])),l=parseInt(e[7]),c=parseInt(e[8]);3===i?s.put(t,n,a,o,r,u,l,c):5!==i&&s.transfer(t,n,a,o,r,u,l,c,Guacamole.Client.DefaultTransferFunction[i])},transform:function(e){var t=y(parseInt(e[0])),n=parseFloat(e[1]),a=parseFloat(e[2]),o=parseFloat(e[3]),r=parseFloat(e[4]),i=parseFloat(e[5]),u=parseFloat(e[6]);s.transform(t,n,a,o,r,i,u)},undefine:function(e){var t=parseInt(e[0]),n=f[t];n&&n.onundefine&&n.onundefine()},video:function(e){var n=parseInt(e[0]),a=y(parseInt(e[1])),o=e[2],r=h[n]=new Guacamole.InputStream(t,n),i=null;t.onvideo&&(i=t.onvideo(r,a,o)),i||(i=Guacamole.VideoPlayer.getInstance(r,a,o)),i?(c[n]=i,t.sendAck(n,"OK",0)):t.sendAck(n,"BAD TYPE",783)}};e.oninstruction=function(e,t){var n=T[e];n&&n(t)},this.disconnect=function(){5!=n&&4!=n&&(g(4),o&&window.clearInterval(o),e.sendMessage("disconnect"),e.disconnect(),g(5))},this.connect=function(t){g(1);try{e.connect(t)}catch(e){throw g(0),e}o=window.setInterval((function(){e.sendMessage("nop")}),5e3),g(2)}},Guacamole.Client.DefaultTransferFunction={0:function(e,t){t.red=t.green=t.blue=0},15:function(e,t){t.red=t.green=t.blue=255},3:function(e,t){t.red=e.red,t.green=e.green,t.blue=e.blue,t.alpha=e.alpha},5:function(e,t){},12:function(e,t){t.red=255&~e.red,t.green=255&~e.green,t.blue=255&~e.blue,t.alpha=e.alpha},10:function(e,t){t.red=255&~t.red,t.green=255&~t.green,t.blue=255&~t.blue},1:function(e,t){t.red=e.red&t.red,t.green=e.green&t.green,t.blue=e.blue&t.blue},14:function(e,t){t.red=255&~(e.red&t.red),t.green=255&~(e.green&t.green),t.blue=255&~(e.blue&t.blue)},7:function(e,t){t.red=e.red|t.red,t.green=e.green|t.green,t.blue=e.blue|t.blue},8:function(e,t){t.red=255&~(e.red|t.red),t.green=255&~(e.green|t.green),t.blue=255&~(e.blue|t.blue)},6:function(e,t){t.red=e.red^t.red,t.green=e.green^t.green,t.blue=e.blue^t.blue},9:function(e,t){t.red=255&(e.red^~t.red),t.green=255&(e.green^~t.green),t.blue=255&(e.blue^~t.blue)},4:function(e,t){t.red=~e.red&t.red&255,t.green=~e.green&t.green&255,t.blue=~e.blue&t.blue&255},13:function(e,t){t.red=255&(~e.red|t.red),t.green=255&(~e.green|t.green),t.blue=255&(~e.blue|t.blue)},2:function(e,t){t.red=e.red&~t.red&255,t.green=e.green&~t.green&255,t.blue=e.blue&~t.blue&255},11:function(e,t){t.red=255&(e.red|~t.red),t.green=255&(e.green|~t.green),t.blue=255&(e.blue|~t.blue)}},(Guacamole=Guacamole||{}).DataURIReader=function(e,t){var n=this,a="data:"+t+";base64,";e.onblob=function(e){a+=e},e.onend=function(){n.onend&&n.onend()},this.getURI=function(){return a},this.onend=null},(Guacamole=Guacamole||{}).Display=function(){var e=this,t=0,n=0,a=1,o=document.createElement("div");o.style.position="relative",o.style.width=t+"px",o.style.height=n+"px",o.style.transformOrigin=o.style.webkitTransformOrigin=o.style.MozTransformOrigin=o.style.OTransformOrigin=o.style.msTransformOrigin="0 0";var r=new Guacamole.Display.VisibleLayer(t,n),i=new Guacamole.Display.VisibleLayer(0,0);i.setChannelMask(Guacamole.Layer.SRC),o.appendChild(r.getElement()),o.appendChild(i.getElement());var s=document.createElement("div");s.style.position="relative",s.style.width=t*a+"px",s.style.height=n*a+"px",s.appendChild(o),this.cursorHotspotX=0,this.cursorHotspotY=0,this.cursorX=0,this.cursorY=0,this.onresize=null,this.oncursor=null;var u=[],l=[];function c(){for(var e=0;e<l.length;){var t=l[e];if(!t.isReady())break;t.flush(),e++}l.splice(0,e)}function d(e,t){this.isReady=function(){for(var e=0;e<t.length;e++)if(t[e].blocked)return!1;return!0},this.flush=function(){for(var n=0;n<t.length;n++)t[n].execute();e&&e()}}function h(e,t){var n=this;this.blocked=t,this.unblock=function(){n.blocked&&(n.blocked=!1,c())},this.execute=function(){e&&e()}}function f(e,t){var n=new h(e,t);return u.push(n),n}this.getElement=function(){return s},this.getWidth=function(){return t},this.getHeight=function(){return n},this.getDefaultLayer=function(){return r},this.getCursorLayer=function(){return i},this.createLayer=function(){var e=new Guacamole.Display.VisibleLayer(t,n);return e.move(r,0,0,0),e},this.createBuffer=function(){var e=new Guacamole.Layer(0,0);return e.autosize=1,e},this.flush=function(e){l.push(new d(e,u)),u=[],c()},this.setCursor=function(t,n,a,o,r,s,u){f((function(){e.cursorHotspotX=t,e.cursorHotspotY=n,i.resize(s,u),i.copy(a,o,r,s,u,0,0),e.moveCursor(e.cursorX,e.cursorY),e.oncursor&&e.oncursor(i.toCanvas(),t,n)}))},this.showCursor=function(e){var t=i.getElement(),n=t.parentNode;!1===e?n&&n.removeChild(t):n!==o&&o.appendChild(t)},this.moveCursor=function(t,n){i.translate(t-e.cursorHotspotX,n-e.cursorHotspotY),e.cursorX=t,e.cursorY=n},this.resize=function(i,u,l){f((function(){i.resize(u,l),i===r&&(t=u,n=l,o.style.width=t+"px",o.style.height=n+"px",s.style.width=t*a+"px",s.style.height=n*a+"px",e.onresize&&e.onresize(u,l))}))},this.drawImage=function(e,t,n,a){f((function(){e.drawImage(t,n,a)}))},this.drawBlob=function(e,t,n,a){var o;if(window.createImageBitmap){var r;o=f((function(){e.drawImage(t,n,r)}),!0),window.createImageBitmap(a).then((function(e){r=e,o.unblock()}))}else{var i=URL.createObjectURL(a);o=f((function(){s.width&&s.height&&e.drawImage(t,n,s),URL.revokeObjectURL(i)}),!0);var s=new Image;s.onload=o.unblock,s.onerror=o.unblock,s.src=i}},this.drawStream=function(t,n,a,o,r){var i;window.createImageBitmap?(i=new Guacamole.BlobReader(o,r)).onend=function(){e.drawBlob(t,n,a,i.getBlob())}:(i=new Guacamole.DataURIReader(o,r)).onend=function(){e.draw(t,n,a,i.getURI())}},this.draw=function(e,t,n,a){var o=f((function(){r.width&&r.height&&e.drawImage(t,n,r)}),!0),r=new Image;r.onload=o.unblock,r.onerror=o.unblock,r.src=a},this.play=function(e,t,n,a){var o=document.createElement("video");o.type=t,o.src=a,o.addEventListener("play",(function(){!function t(){e.drawImage(0,0,o),o.ended||window.setTimeout(t,20)}()}),!1),f(o.play)},this.transfer=function(e,t,n,a,o,r,i,s,u){f((function(){r.transfer(e,t,n,a,o,i,s,u)}))},this.put=function(e,t,n,a,o,r,i,s){f((function(){r.put(e,t,n,a,o,i,s)}))},this.copy=function(e,t,n,a,o,r,i,s){f((function(){r.copy(e,t,n,a,o,i,s)}))},this.moveTo=function(e,t,n){f((function(){e.moveTo(t,n)}))},this.lineTo=function(e,t,n){f((function(){e.lineTo(t,n)}))},this.arc=function(e,t,n,a,o,r,i){f((function(){e.arc(t,n,a,o,r,i)}))},this.curveTo=function(e,t,n,a,o,r,i){f((function(){e.curveTo(t,n,a,o,r,i)}))},this.close=function(e){f((function(){e.close()}))},this.rect=function(e,t,n,a,o){f((function(){e.rect(t,n,a,o)}))},this.clip=function(e){f((function(){e.clip()}))},this.strokeColor=function(e,t,n,a,o,r,i,s){f((function(){e.strokeColor(t,n,a,o,r,i,s)}))},this.fillColor=function(e,t,n,a,o){f((function(){e.fillColor(t,n,a,o)}))},this.strokeLayer=function(e,t,n,a,o){f((function(){e.strokeLayer(t,n,a,o)}))},this.fillLayer=function(e,t){f((function(){e.fillLayer(t)}))},this.push=function(e){f((function(){e.push()}))},this.pop=function(e){f((function(){e.pop()}))},this.reset=function(e){f((function(){e.reset()}))},this.setTransform=function(e,t,n,a,o,r,i){f((function(){e.setTransform(t,n,a,o,r,i)}))},this.transform=function(e,t,n,a,o,r,i){f((function(){e.transform(t,n,a,o,r,i)}))},this.setChannelMask=function(e,t){f((function(){e.setChannelMask(t)}))},this.setMiterLimit=function(e,t){f((function(){e.setMiterLimit(t)}))},this.dispose=function(e){f((function(){e.dispose()}))},this.distort=function(e,t,n,a,o,r,i){f((function(){e.distort(t,n,a,o,r,i)}))},this.move=function(e,t,n,a,o){f((function(){e.move(t,n,a,o)}))},this.shade=function(e,t){f((function(){e.shade(t)}))},this.scale=function(e){o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform="scale("+e+","+e+")",a=e,s.style.width=t*a+"px",s.style.height=n*a+"px"},this.getScale=function(){return a},this.flatten=function(){var e=document.createElement("canvas");e.width=r.width,e.height=r.height;var t=e.getContext("2d");return function e(n,a,o){if(n.width>0&&n.height>0){var r=t.globalAlpha;t.globalAlpha*=n.alpha/255,t.drawImage(n.getCanvas(),a,o);for(var i=function(e){var t=[];for(var n in e.children)t.push(e.children[n]);return t.sort((function(e,t){var n=e.z-t.z;if(0!==n)return n;var a=e.getElement(),o=t.getElement().compareDocumentPosition(a);return o&Node.DOCUMENT_POSITION_PRECEDING?-1:o&Node.DOCUMENT_POSITION_FOLLOWING?1:0})),t}(n),s=0;s<i.length;s++){var u=i[s];e(u,a+u.x,o+u.y)}t.globalAlpha=r}}(r,0,0),e}},Guacamole.Display.VisibleLayer=function(e,t){Guacamole.Layer.apply(this,[e,t]);var n=this;this.__unique_id=Guacamole.Display.VisibleLayer.__next_id++,this.alpha=255,this.x=0,this.y=0,this.z=0,this.matrix=[1,0,0,1,0,0],this.parent=null,this.children={};var a=n.getCanvas();a.style.position="absolute",a.style.left="0px",a.style.top="0px";var o=document.createElement("div");o.appendChild(a),o.style.width=e+"px",o.style.height=t+"px",o.style.position="absolute",o.style.left="0px",o.style.top="0px",o.style.overflow="hidden";var r=this.resize;this.resize=function(e,t){o.style.width=e+"px",o.style.height=t+"px",r(e,t)},this.getElement=function(){return o};var i="translate(0px, 0px)",s="matrix(1, 0, 0, 1, 0, 0)";this.translate=function(e,t){n.x=e,n.y=t,i="translate("+e+"px,"+t+"px)",o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform=i+" "+s},this.move=function(e,t,a,r){n.parent!==e&&(n.parent&&delete n.parent.children[n.__unique_id],n.parent=e,e.children[n.__unique_id]=n,e.getElement().appendChild(o));n.translate(t,a),n.z=r,o.style.zIndex=r},this.shade=function(e){n.alpha=e,o.style.opacity=e/255},this.dispose=function(){n.parent&&(delete n.parent.children[n.__unique_id],n.parent=null),o.parentNode&&o.parentNode.removeChild(o)},this.distort=function(e,t,a,r,u,l){n.matrix=[e,t,a,r,u,l],s="matrix("+e+","+t+","+a+","+r+","+u+","+l+")",o.style.transform=o.style.WebkitTransform=o.style.MozTransform=o.style.OTransform=o.style.msTransform=i+" "+s}},Guacamole.Display.VisibleLayer.__next_id=0,(Guacamole=Guacamole||{}).InputSink=function(){var e=this,t=document.createElement("textarea");t.style.position="fixed",t.style.outline="none",t.style.border="none",t.style.margin="0",t.style.padding="0",t.style.height="0",t.style.width="0",t.style.left="0",t.style.bottom="0",t.style.resize="none",t.style.background="transparent",t.style.color="transparent",t.addEventListener("keypress",(function(e){t.value=""}),!1),t.addEventListener("compositionend",(function(e){e.data&&(t.value="")}),!1),t.addEventListener("input",(function(e){e.data&&!e.isComposing&&(t.value="")}),!1),t.addEventListener("focus",(function(){window.setTimeout((function(){t.click(),t.select()}),0)}),!0),this.focus=function(){window.setTimeout((function(){t.focus()}),0)},this.getElement=function(){return t},document.addEventListener("keydown",(function(t){var n=document.activeElement;if(n&&n!==document.body){var a=n.getBoundingClientRect();if(a.left+a.width>0&&a.top+a.height>0)return}e.focus()}),!0)},(Guacamole=Guacamole||{}).InputStream=function(e,t){var n=this;this.index=t,this.onblob=null,this.onend=null,this.sendAck=function(t,a){e.sendAck(n.index,t,a)}},(Guacamole=Guacamole||{}).IntegerPool=function(){var e=this,t=[];this.next_int=0,this.next=function(){return t.length>0?t.shift():e.next_int++},this.free=function(e){t.push(e)}},(Guacamole=Guacamole||{}).JSONReader=function(e){var t=this,n=new Guacamole.StringReader(e),a="";this.getLength=function(){return a.length},this.getJSON=function(){return JSON.parse(a)},n.ontext=function(e){a+=e,t.onprogress&&t.onprogress(e.length)},n.onend=function(){t.onend&&t.onend()},this.onprogress=null,this.onend=null},(Guacamole=Guacamole||{}).Keyboard=function(e){var t=this,n="_GUAC_KEYBOARD_HANDLED_BY_"+Guacamole.Keyboard._nextID++;this.onkeydown=null,this.onkeyup=null;var a={keyupUnreliable:!1,altIsTypableOnly:!1,capsLockKeyupUnreliable:!1};navigator&&navigator.platform&&(navigator.platform.match(/ipad|iphone|ipod/i)?a.keyupUnreliable=!0:navigator.platform.match(/^mac/i)&&(a.altIsTypableOnly=!0,a.capsLockKeyupUnreliable=!0));var o=function(){var e=this;this.timestamp=(new Date).getTime(),this.defaultPrevented=!1,this.keysym=null,this.reliable=!1,this.getAge=function(){return(new Date).getTime()-e.timestamp}},r=function(e,n,r,i){o.apply(this),this.keyCode=e,this.keyIdentifier=n,this.key=r,this.location=i,this.keysym=w(r,i)||T(e,i),this.keyupReliable=!a.keyupUnreliable,this.keysym&&!y(this.keysym)&&(this.reliable=!0),!this.keysym&&b(e,n)&&(this.keysym=w(n,i,t.modifiers.shift)),(t.modifiers.meta&&65511!==this.keysym&&65512!==this.keysym||65509===this.keysym&&a.capsLockKeyupUnreliable)&&(this.keyupReliable=!1);var s=!t.modifiers.ctrl&&!a.altIsTypableOnly;(!t.modifiers.alt&&t.modifiers.ctrl||s&&t.modifiers.alt||t.modifiers.meta||t.modifiers.hyper)&&(this.reliable=!0),m[e]=this.keysym};r.prototype=new o;var i=function(e){o.apply(this),this.charCode=e,this.keysym=S(e),this.reliable=!0};i.prototype=new o;var s=function(e,n,a,r){o.apply(this),this.keyCode=e,this.keyIdentifier=n,this.key=a,this.location=r,this.keysym=T(e,r)||w(a,r),t.pressed[this.keysym]||(this.keysym=m[e]||this.keysym),this.reliable=!0};s.prototype=new o;var u=[],l={8:[65288],9:[65289],12:[65291,65291,65291,65461],13:[65293],16:[65505,65505,65506],17:[65507,65507,65508],18:[65513,65513,65027],19:[65299],20:[65509],27:[65307],32:[32],33:[65365,65365,65365,65465],34:[65366,65366,65366,65459],35:[65367,65367,65367,65457],36:[65360,65360,65360,65463],37:[65361,65361,65361,65460],38:[65362,65362,65362,65464],39:[65363,65363,65363,65462],40:[65364,65364,65364,65458],45:[65379,65379,65379,65456],46:[65535,65535,65535,65454],91:[65515],92:[65383],93:null,96:[65456],97:[65457],98:[65458],99:[65459],100:[65460],101:[65461],102:[65462],103:[65463],104:[65464],105:[65465],106:[65450],107:[65451],109:[65453],110:[65454],111:[65455],112:[65470],113:[65471],114:[65472],115:[65473],116:[65474],117:[65475],118:[65476],119:[65477],120:[65478],121:[65479],122:[65480],123:[65481],144:[65407],145:[65300],225:[65027]},c={Again:[65382],AllCandidates:[65341],Alphanumeric:[65328],Alt:[65513,65513,65027],Attn:[64782],AltGraph:[65027],ArrowDown:[65364],ArrowLeft:[65361],ArrowRight:[65363],ArrowUp:[65362],Backspace:[65288],CapsLock:[65509],Cancel:[65385],Clear:[65291],Convert:[65313],Copy:[64789],Crsel:[64796],CrSel:[64796],CodeInput:[65335],Compose:[65312],Control:[65507,65507,65508],ContextMenu:[65383],Delete:[65535],Down:[65364],End:[65367],Enter:[65293],EraseEof:[64774],Escape:[65307],Execute:[65378],Exsel:[64797],ExSel:[64797],F1:[65470],F2:[65471],F3:[65472],F4:[65473],F5:[65474],F6:[65475],F7:[65476],F8:[65477],F9:[65478],F10:[65479],F11:[65480],F12:[65481],F13:[65482],F14:[65483],F15:[65484],F16:[65485],F17:[65486],F18:[65487],F19:[65488],F20:[65489],F21:[65490],F22:[65491],F23:[65492],F24:[65493],Find:[65384],GroupFirst:[65036],GroupLast:[65038],GroupNext:[65032],GroupPrevious:[65034],FullWidth:null,HalfWidth:null,HangulMode:[65329],Hankaku:[65321],HanjaMode:[65332],Help:[65386],Hiragana:[65317],HiraganaKatakana:[65319],Home:[65360],Hyper:[65517,65517,65518],Insert:[65379],JapaneseHiragana:[65317],JapaneseKatakana:[65318],JapaneseRomaji:[65316],JunjaMode:[65336],KanaMode:[65325],KanjiMode:[65313],Katakana:[65318],Left:[65361],Meta:[65511,65511,65512],ModeChange:[65406],NumLock:[65407],PageDown:[65366],PageUp:[65365],Pause:[65299],Play:[64790],PreviousCandidate:[65342],PrintScreen:[65377],Redo:[65382],Right:[65363],RomanCharacters:null,Scroll:[65300],Select:[65376],Separator:[65452],Shift:[65505,65505,65506],SingleCandidate:[65340],Super:[65515,65515,65516],Tab:[65289],UIKeyInputDownArrow:[65364],UIKeyInputEscape:[65307],UIKeyInputLeftArrow:[65361],UIKeyInputRightArrow:[65363],UIKeyInputUpArrow:[65362],Up:[65362],Undo:[65381],Win:[65515],Zenkaku:[65320],ZenkakuHankaku:[65322]},d={65027:!0,65505:!0,65506:!0,65507:!0,65508:!0,65509:!0,65511:!0,65512:!0,65513:!0,65514:!0,65515:!0,65516:!0};this.modifiers=new Guacamole.Keyboard.ModifierState,this.pressed={};var h={},f={},m={},p=null,g=null,v=function(e,t){return e?e[t]||e[0]:null},y=function(e){return e>=0&&e<=255||16777216==(4294901760&e)};function w(e,t,n){if(!e)return null;var a,o=e.indexOf("U+");if(o>=0){var r=e.substring(o+2);a=String.fromCharCode(parseInt(r,16))}else{if(1!==e.length||3===t)return v(c[e],t);a=e}return!0===n?a=a.toUpperCase():!1===n&&(a=a.toLowerCase()),S(a.charCodeAt(0))}function S(e){return function(e){return e<=31||e>=127&&e<=159}(e)?65280|e:e>=0&&e<=255?e:e>=256&&e<=1114111?16777216|e:null}function T(e,t){return v(l[e],t)}var b=function(e,t){if(!t)return!1;var n=t.indexOf("U+");return-1===n||(e!==parseInt(t.substring(n+2),16)||(e>=65&&e<=90||e>=48&&e<=57))};this.press=function(e){if(null!==e){if(!t.pressed[e]&&(t.pressed[e]=!0,t.onkeydown)){var n=t.onkeydown(e);return f[e]=n,window.clearTimeout(p),window.clearInterval(g),d[e]||(p=window.setTimeout((function(){g=window.setInterval((function(){t.onkeyup(e),t.onkeydown(e)}),50)}),500)),n}return f[e]||!1}},this.release=function(e){t.pressed[e]&&(delete t.pressed[e],delete h[e],window.clearTimeout(p),window.clearInterval(g),null!==e&&t.onkeyup&&t.onkeyup(e))},this.type=function(e){for(var n=0;n<e.length;n++){var a=S(e.codePointAt?e.codePointAt(n):e.charCodeAt(n));t.press(a),t.release(a)}},this.reset=function(){for(var e in t.pressed)t.release(parseInt(e));u=[]};var I=function(e,n,a,o){var r;if(-1===a.indexOf(o.keysym))if(e&&!1===n)for(r=0;r<a.length;r++)t.release(a[r]);else if(!e&&n){for(r=0;r<a.length;r++)if(t.pressed[a[r]])return;var i=a[0];o.keysym&&(h[i]=!0),t.press(i)}},G=function(e,n){var a=Guacamole.Keyboard.ModifierState.fromKeyboardEvent(e);I(t.modifiers.alt,a.alt,[65513,65514,65027],n),I(t.modifiers.shift,a.shift,[65505,65506],n),I(t.modifiers.ctrl,a.ctrl,[65507,65508],n),I(t.modifiers.meta,a.meta,[65511,65512],n),I(t.modifiers.hyper,a.hyper,[65515,65516],n),t.modifiers=a};function C(){var e,n=E();if(!n)return!1;do{e=n,n=E()}while(null!==n);return function(){for(var e in t.pressed)if(!h[e])return!1;return!0}()&&t.reset(),e.defaultPrevented}var E=function(){var e=u[0];if(!e)return null;if(!(e instanceof r))return e instanceof s&&!a.keyupUnreliable?(n=e.keysym)?(t.release(n),delete m[e.keyCode],e.defaultPrevented=!0,u.shift()):(t.reset(),e):u.shift();var n=null,o=[];if(e.reliable?(n=e.keysym,o=u.splice(0,1)):u[1]instanceof i?(n=u[1].keysym,o=u.splice(0,2)):u[1]&&(n=e.keysym,o=u.splice(0,1)),o.length>0){if(n){!function(e){t.modifiers.ctrl&&t.modifiers.alt&&(e>=65&&e<=90||e>=97&&e<=122||(e<=255||16777216==(4278190080&e))&&(t.release(65507),t.release(65508),t.release(65513),t.release(65514)))}(n);var l=!t.press(n);m[e.keyCode]=n,e.keyupReliable||t.release(n);for(var c=0;c<o.length;c++)o[c].defaultPrevented=l}return e}return null},k=function(e){return"location"in e?e.location:"keyLocation"in e?e.keyLocation:0},A=function(e){return!e[n]&&(e[n]=!0,!0)};this.listenTo=function(e){e.addEventListener("keydown",(function(e){if(t.onkeydown&&A(e)){var n;window.event?n=window.event.keyCode:e.which&&(n=e.which);var a=new r(n,e.keyIdentifier,e.key,k(e));G(e,a),229!==n&&(u.push(a),C()&&e.preventDefault())}}),!0),e.addEventListener("keypress",(function(e){if((t.onkeydown||t.onkeyup)&&A(e)){var n;window.event?n=window.event.keyCode:e.which&&(n=e.which);var a=new i(n);G(e,a),u.push(a),C()&&e.preventDefault()}}),!0),e.addEventListener("keyup",(function(e){if(t.onkeyup&&A(e)){var n;e.preventDefault(),window.event?n=window.event.keyCode:e.which&&(n=e.which);var a=new s(n,e.keyIdentifier,e.key,k(e));G(e,a),u.push(a),C()}}),!0);var n=function(n){(t.onkeydown||t.onkeyup)&&A(n)&&n.data&&!n.isComposing&&(e.removeEventListener("compositionend",a,!1),t.type(n.data))},a=function(a){(t.onkeydown||t.onkeyup)&&A(a)&&a.data&&(e.removeEventListener("input",n,!1),t.type(a.data))};e.addEventListener("input",n,!1),e.addEventListener("compositionend",a,!1)},e&&t.listenTo(e)},Guacamole.Keyboard._nextID=0,Guacamole.Keyboard.ModifierState=function(){this.shift=!1,this.ctrl=!1,this.alt=!1,this.meta=!1,this.hyper=!1},Guacamole.Keyboard.ModifierState.fromKeyboardEvent=function(e){var t=new Guacamole.Keyboard.ModifierState;return t.shift=e.shiftKey,t.ctrl=e.ctrlKey,t.alt=e.altKey,t.meta=e.metaKey,e.getModifierState&&(t.hyper=e.getModifierState("OS")||e.getModifierState("Super")||e.getModifierState("Hyper")||e.getModifierState("Win")),t},(Guacamole=Guacamole||{}).Layer=function(e,t){var n=this,a=document.createElement("canvas"),o=a.getContext("2d");o.save();var r=!0,i=!0,s=0,u={1:"destination-in",2:"destination-out",4:"source-in",6:"source-atop",8:"source-out",9:"destination-atop",10:"xor",11:"destination-over",12:"copy",14:"source-over",15:"lighter"},l=function(e,t){e=e||0,t=t||0;var i=64*Math.ceil(e/64),u=64*Math.ceil(t/64);if(a.width!==i||a.height!==u){var l=null;if(!r&&0!==a.width&&0!==a.height)(l=document.createElement("canvas")).width=Math.min(n.width,e),l.height=Math.min(n.height,t),l.getContext("2d").drawImage(a,0,0,l.width,l.height,0,0,l.width,l.height);var c=o.globalCompositeOperation;a.width=i,a.height=u,l&&o.drawImage(l,0,0,l.width,l.height,0,0,l.width,l.height),o.globalCompositeOperation=c,s=0,o.save()}else n.reset();n.width=e,n.height=t};function c(e,t,a,o){var r,i,s=a+e,u=o+t;r=s>n.width?s:n.width,i=u>n.height?u:n.height,n.resize(r,i)}this.autosize=!1,this.width=e,this.height=t,this.getCanvas=function(){return a},this.toCanvas=function(){var e=document.createElement("canvas");return e.width=n.width,e.height=n.height,e.getContext("2d").drawImage(n.getCanvas(),0,0),e},this.resize=function(e,t){e===n.width&&t===n.height||l(e,t)},this.drawImage=function(e,t,a){n.autosize&&c(e,t,a.width,a.height),o.drawImage(a,e,t),r=!1},this.transfer=function(e,t,a,i,s,u,l,d){var h=e.getCanvas();if(!(t>=h.width||a>=h.height)&&(t+i>h.width&&(i=h.width-t),a+s>h.height&&(s=h.height-a),0!==i&&0!==s)){n.autosize&&c(u,l,i,s);for(var f=e.getCanvas().getContext("2d").getImageData(t,a,i,s),m=o.getImageData(u,l,i,s),p=0;p<i*s*4;p+=4){var g=new Guacamole.Layer.Pixel(f.data[p],f.data[p+1],f.data[p+2],f.data[p+3]),v=new Guacamole.Layer.Pixel(m.data[p],m.data[p+1],m.data[p+2],m.data[p+3]);d(g,v),m.data[p]=v.red,m.data[p+1]=v.green,m.data[p+2]=v.blue,m.data[p+3]=v.alpha}o.putImageData(m,u,l),r=!1}},this.put=function(e,t,a,i,s,u,l){var d=e.getCanvas();if(!(t>=d.width||a>=d.height)&&(t+i>d.width&&(i=d.width-t),a+s>d.height&&(s=d.height-a),0!==i&&0!==s)){n.autosize&&c(u,l,i,s);var h=e.getCanvas().getContext("2d").getImageData(t,a,i,s);o.putImageData(h,u,l),r=!1}},this.copy=function(e,t,a,i,s,u,l){var d=e.getCanvas();t>=d.width||a>=d.height||(t+i>d.width&&(i=d.width-t),a+s>d.height&&(s=d.height-a),0!==i&&0!==s&&(n.autosize&&c(u,l,i,s),o.drawImage(d,t,a,i,s,u,l,i,s),r=!1))},this.moveTo=function(e,t){i&&(o.beginPath(),i=!1),n.autosize&&c(e,t,0,0),o.moveTo(e,t)},this.lineTo=function(e,t){i&&(o.beginPath(),i=!1),n.autosize&&c(e,t,0,0),o.lineTo(e,t)},this.arc=function(e,t,a,r,s,u){i&&(o.beginPath(),i=!1),n.autosize&&c(e,t,0,0),o.arc(e,t,a,r,s,u)},this.curveTo=function(e,t,a,r,s,u){i&&(o.beginPath(),i=!1),n.autosize&&c(s,u,0,0),o.bezierCurveTo(e,t,a,r,s,u)},this.close=function(){o.closePath(),i=!0},this.rect=function(e,t,a,r){i&&(o.beginPath(),i=!1),n.autosize&&c(e,t,a,r),o.rect(e,t,a,r)},this.clip=function(){o.clip(),i=!0},this.strokeColor=function(e,t,n,a,s,u,l){o.lineCap=e,o.lineJoin=t,o.lineWidth=n,o.strokeStyle="rgba("+a+","+s+","+u+","+l/255+")",o.stroke(),r=!1,i=!0},this.fillColor=function(e,t,n,a){o.fillStyle="rgba("+e+","+t+","+n+","+a/255+")",o.fill(),r=!1,i=!0},this.strokeLayer=function(e,t,n,a){o.lineCap=e,o.lineJoin=t,o.lineWidth=n,o.strokeStyle=o.createPattern(a.getCanvas(),"repeat"),o.stroke(),r=!1,i=!0},this.fillLayer=function(e){o.fillStyle=o.createPattern(e.getCanvas(),"repeat"),o.fill(),r=!1,i=!0},this.push=function(){o.save(),s++},this.pop=function(){s>0&&(o.restore(),s--)},this.reset=function(){for(;s>0;)o.restore(),s--;o.restore(),o.save(),o.beginPath(),i=!1},this.setTransform=function(e,t,n,a,r,i){o.setTransform(e,t,n,a,r,i)},this.transform=function(e,t,n,a,r,i){o.transform(e,t,n,a,r,i)},this.setChannelMask=function(e){o.globalCompositeOperation=u[e]},this.setMiterLimit=function(e){o.miterLimit=e},l(e,t),a.style.zIndex=-1},Guacamole.Layer.ROUT=2,Guacamole.Layer.ATOP=6,Guacamole.Layer.XOR=10,Guacamole.Layer.ROVER=11,Guacamole.Layer.OVER=14,Guacamole.Layer.PLUS=15,Guacamole.Layer.RIN=1,Guacamole.Layer.IN=4,Guacamole.Layer.OUT=8,Guacamole.Layer.RATOP=9,Guacamole.Layer.SRC=12,Guacamole.Layer.Pixel=function(e,t,n,a){this.red=e,this.green=t,this.blue=n,this.alpha=a},(Guacamole=Guacamole||{}).Mouse=function(e){var t=this;this.touchMouseThreshold=3,this.scrollThreshold=53,this.PIXELS_PER_LINE=18,this.PIXELS_PER_PAGE=16*this.PIXELS_PER_LINE,this.currentState=new Guacamole.Mouse.State(0,0,!1,!1,!1,!1,!1),this.onmousedown=null,this.onmouseup=null,this.onmousemove=null,this.onmouseout=null;var n=0,a=0;function o(e){e.stopPropagation(),e.preventDefault&&e.preventDefault(),e.returnValue=!1}function r(){n=t.touchMouseThreshold}function i(e){var n=e.deltaY||-e.wheelDeltaY||-e.wheelDelta;if(n?1===e.deltaMode?n=e.deltaY*t.PIXELS_PER_LINE:2===e.deltaMode&&(n=e.deltaY*t.PIXELS_PER_PAGE):n=e.detail*t.PIXELS_PER_LINE,(a+=n)<=-t.scrollThreshold){do{t.onmousedown&&(t.currentState.up=!0,t.onmousedown(t.currentState)),t.onmouseup&&(t.currentState.up=!1,t.onmouseup(t.currentState)),a+=t.scrollThreshold}while(a<=-t.scrollThreshold);a=0}if(a>=t.scrollThreshold){do{t.onmousedown&&(t.currentState.down=!0,t.onmousedown(t.currentState)),t.onmouseup&&(t.currentState.down=!1,t.onmouseup(t.currentState)),a-=t.scrollThreshold}while(a>=t.scrollThreshold);a=0}o(e)}e.addEventListener("contextmenu",(function(e){o(e)}),!1),e.addEventListener("mousemove",(function(a){o(a),n?n--:(t.currentState.fromClientPosition(e,a.clientX,a.clientY),t.onmousemove&&t.onmousemove(t.currentState))}),!1),e.addEventListener("mousedown",(function(e){if(o(e),!n){switch(e.button){case 0:t.currentState.left=!0;break;case 1:t.currentState.middle=!0;break;case 2:t.currentState.right=!0}t.onmousedown&&t.onmousedown(t.currentState)}}),!1),e.addEventListener("mouseup",(function(e){if(o(e),!n){switch(e.button){case 0:t.currentState.left=!1;break;case 1:t.currentState.middle=!1;break;case 2:t.currentState.right=!1}t.onmouseup&&t.onmouseup(t.currentState)}}),!1),e.addEventListener("mouseout",(function(n){n||(n=window.event);for(var a=n.relatedTarget||n.toElement;a;){if(a===e)return;a=a.parentNode}o(n),(t.currentState.left||t.currentState.middle||t.currentState.right)&&(t.currentState.left=!1,t.currentState.middle=!1,t.currentState.right=!1,t.onmouseup&&t.onmouseup(t.currentState)),t.onmouseout&&t.onmouseout()}),!1),e.addEventListener("selectstart",(function(e){o(e)}),!1),e.addEventListener("touchmove",r,!1),e.addEventListener("touchstart",r,!1),e.addEventListener("touchend",r,!1),e.addEventListener("DOMMouseScroll",i,!1),e.addEventListener("mousewheel",i,!1),e.addEventListener("wheel",i,!1);var s=function(){var e=document.createElement("div");if(!("cursor"in e.style))return!1;try{e.style.cursor="url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEX///+nxBvIAAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==) 0 0, auto"}catch(e){return!1}return/\burl\([^()]*\)\s+0\s+0\b/.test(e.style.cursor||"")}();this.setCursor=function(t,n,a){if(s){var o=t.toDataURL("image/png");return e.style.cursor="url("+o+") "+n+" "+a+", auto",!0}return!1}},Guacamole.Mouse.State=function(e,t,n,a,o,r,i){var s=this;this.x=e,this.y=t,this.left=n,this.middle=a,this.right=o,this.up=r,this.down=i,this.fromClientPosition=function(e,t,n){s.x=t-e.offsetLeft,s.y=n-e.offsetTop;for(var a=e.offsetParent;a&&a!==document.body;)s.x-=a.offsetLeft-a.scrollLeft,s.y-=a.offsetTop-a.scrollTop,a=a.offsetParent;if(a){var o=document.body.scrollLeft||document.documentElement.scrollLeft,r=document.body.scrollTop||document.documentElement.scrollTop;s.x-=a.offsetLeft-o,s.y-=a.offsetTop-r}}},Guacamole.Mouse.Touchpad=function(e){var t=this;this.scrollThreshold=20*(window.devicePixelRatio||1),this.clickTimingThreshold=250,this.clickMoveThreshold=10*(window.devicePixelRatio||1),this.currentState=new Guacamole.Mouse.State(0,0,!1,!1,!1,!1,!1),this.onmousedown=null,this.onmouseup=null,this.onmousemove=null;var n=0,a=0,o=0,r=0,i=0,s={1:"left",2:"right",3:"middle"},u=!1,l=null;e.addEventListener("touchend",(function(e){if(e.preventDefault(),u&&0===e.touches.length){var a=(new Date).getTime(),o=s[n];t.currentState[o]&&(t.currentState[o]=!1,t.onmouseup&&t.onmouseup(t.currentState),l&&(window.clearTimeout(l),l=null)),a-r<=t.clickTimingThreshold&&i<t.clickMoveThreshold&&(t.currentState[o]=!0,t.onmousedown&&t.onmousedown(t.currentState),l=window.setTimeout((function(){t.currentState[o]=!1,t.onmouseup&&t.onmouseup(t.currentState),u=!1}),t.clickTimingThreshold)),l||(u=!1)}}),!1),e.addEventListener("touchstart",(function(e){if(e.preventDefault(),n=Math.min(e.touches.length,3),l&&(window.clearTimeout(l),l=null),!u){u=!0;var t=e.touches[0];a=t.clientX,o=t.clientY,r=(new Date).getTime(),i=0}}),!1),e.addEventListener("touchmove",(function(s){s.preventDefault();var u=s.touches[0],l=u.clientX-a,c=u.clientY-o;if(i+=Math.abs(l)+Math.abs(c),1===n){var d=1+i/((new Date).getTime()-r);t.currentState.x+=l*d,t.currentState.y+=c*d,t.currentState.x<0?t.currentState.x=0:t.currentState.x>=e.offsetWidth&&(t.currentState.x=e.offsetWidth-1),t.currentState.y<0?t.currentState.y=0:t.currentState.y>=e.offsetHeight&&(t.currentState.y=e.offsetHeight-1),t.onmousemove&&t.onmousemove(t.currentState),a=u.clientX,o=u.clientY}else if(2===n){var h;if(Math.abs(c)>=t.scrollThreshold)h=c>0?"down":"up",t.currentState[h]=!0,t.onmousedown&&t.onmousedown(t.currentState),t.currentState[h]=!1,t.onmouseup&&t.onmouseup(t.currentState),a=u.clientX,o=u.clientY}}),!1)},Guacamole.Mouse.Touchscreen=function(e){var t=this,n=!1,a=null,o=null,r=null,i=null;function s(e){t.currentState[e]||(t.currentState[e]=!0,t.onmousedown&&t.onmousedown(t.currentState))}function u(e){t.currentState[e]&&(t.currentState[e]=!1,t.onmouseup&&t.onmouseup(t.currentState))}function l(n,a){t.currentState.fromClientPosition(e,n,a),t.onmousemove&&t.onmousemove(t.currentState)}function c(e){var n=e.touches[0]||e.changedTouches[0],r=n.clientX-a,i=n.clientY-o;return Math.sqrt(r*r+i*i)>=t.clickMoveThreshold}function d(){window.clearTimeout(r),window.clearTimeout(i),n=!1}this.scrollThreshold=20*(window.devicePixelRatio||1),this.clickTimingThreshold=250,this.clickMoveThreshold=16*(window.devicePixelRatio||1),this.longPressThreshold=500,this.currentState=new Guacamole.Mouse.State(0,0,!1,!1,!1,!1,!1),this.onmousedown=null,this.onmouseup=null,this.onmousemove=null,e.addEventListener("touchend",(function(e){if(n)if(0===e.touches.length&&1===e.changedTouches.length){if(window.clearTimeout(i),u("left"),!c(e)&&(e.preventDefault(),!t.currentState.left)){var a=e.changedTouches[0];l(a.clientX,a.clientY),s("left"),r=window.setTimeout((function(){u("left"),d()}),t.clickTimingThreshold)}}else d()}),!1),e.addEventListener("touchstart",(function(e){1===e.touches.length?(e.preventDefault(),function(e){var t=e.touches[0];n=!0,a=t.clientX,o=t.clientY}(e),window.clearTimeout(r),i=window.setTimeout((function(){var t,n=e.touches[0];l(n.clientX,n.clientY),s(t="right"),u(t),d()}),t.longPressThreshold)):d()}),!1),e.addEventListener("touchmove",(function(e){if(n)if(c(e)&&window.clearTimeout(i),1===e.touches.length){if(t.currentState.left){e.preventDefault();var a=e.touches[0];l(a.clientX,a.clientY)}}else d()}),!1)},(Guacamole=(Guacamole=Guacamole||{})||{}).Object=function(e,t){var n=this,a={};this.index=t,this.onbody=function(e,t,n){var o=function(e){var t=a[e];if(!t)return null;var n=t.shift();return 0===t.length&&delete a[e],n}(n);o&&o(e,t)},this.onundefine=null,this.requestInputStream=function(t,o){o&&function(e,t){var n=a[e];n||(n=[],a[e]=n),n.push(t)}(t,o),e.requestObjectInputStream(n.index,t)},this.createOutputStream=function(t,a){return e.createObjectOutputStream(n.index,t,a)}},Guacamole.Object.ROOT_STREAM="/",Guacamole.Object.STREAM_INDEX_MIMETYPE="application/vnd.glyptodon.guacamole.stream-index+json",(Guacamole=Guacamole||{}).OnScreenKeyboard=function(e){var t=this,n={},a={},o=[],r=function(e,t){e.classList?e.classList.add(t):e.className+=" "+t},i=function(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(/([^ ]+)[ ]*/g,(function(e,n){return n===t?"":e}))},s=0,u=function(e,t,n,a){this.width=t,this.height=n,this.scale=function(o){e.style.width=t*o+"px",e.style.height=n*o+"px",a&&(e.style.lineHeight=n*o+"px",e.style.fontSize=o+"px")}},l=function(e){for(var t=0;t<e.length;t++){if(!(e[t]in n))return!1}return!0},c=function(e){var n=t.keys[e];if(!n)return null;for(var a=n.length-1;a>=0;a--){var o=n[a];if(l(o.requires))return o}return null},d=function(e,o){if(!a[e]){r(o,"guac-keyboard-pressed");var s=c(e);if(s.modifier){var u="guac-keyboard-modifier-"+p(s.modifier),l=n[s.modifier];l?(i(f,u),delete n[s.modifier],t.onkeyup&&t.onkeyup(l)):(r(f,u),n[s.modifier]=s.keysym,t.onkeydown&&t.onkeydown(s.keysym))}else t.onkeydown&&t.onkeydown(s.keysym);a[e]=!0}},h=function(e,n){if(a[e]){i(n,"guac-keyboard-pressed");var o=c(e);!o.modifier&&t.onkeyup&&t.onkeyup(o.keysym),a[e]=!1}},f=document.createElement("div");f.className="guac-keyboard",f.onselectstart=f.onmousemove=f.onmouseup=f.onmousedown=function(e){return s&&s--,e.stopPropagation(),!1},this.touchMouseThreshold=3,this.onkeydown=null,this.onkeyup=null,this.layout=new Guacamole.OnScreenKeyboard.Layout(e),this.getElement=function(){return f},this.resize=function(e){for(var n=Math.floor(10*e/t.layout.width)/10,a=0;a<o.length;a++){o[a].scale(n)}};var m=function(e,t){if(t instanceof Array){for(var n=[],a=0;a<t.length;a++)n.push(new Guacamole.OnScreenKeyboard.Key(t[a],e));return n}return"number"==typeof t?[new Guacamole.OnScreenKeyboard.Key({name:e,keysym:t})]:"string"==typeof t?[new Guacamole.OnScreenKeyboard.Key({name:e,title:t})]:[new Guacamole.OnScreenKeyboard.Key(t,e)]};this.keys=function(t){var n={};for(var a in e.keys)n[a]=m(a,t[a]);return n}(e.keys);var p=function(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[^A-Za-z0-9]+/g,"-").toLowerCase()};!function e(n,a,i){var l,c=document.createElement("div");if(i&&r(c,"guac-keyboard-"+p(i)),a instanceof Array)for(r(c,"guac-keyboard-group"),l=0;l<a.length;l++)e(c,a[l]);else if(a instanceof Object){r(c,"guac-keyboard-group");var f=Object.keys(a).sort();for(l=0;l<f.length;l++){i=f[l];e(c,a[i],i)}}else if("number"==typeof a)r(c,"guac-keyboard-gap"),o.push(new u(c,a,a));else if("string"==typeof a){var m=a;1===m.length&&(m="0x"+m.charCodeAt(0).toString(16)),r(c,"guac-keyboard-key-container");var g=document.createElement("div");g.className="guac-keyboard-key guac-keyboard-key-"+p(m);var v=t.keys[a];if(v)for(l=0;l<v.length;l++){var y=v[l],w=document.createElement("div");w.className="guac-keyboard-cap",w.textContent=y.title;for(var S=0;S<y.requires.length;S++){var T=y.requires[S];r(w,"guac-keyboard-requires-"+p(T)),r(g,"guac-keyboard-uses-"+p(T))}g.appendChild(w)}c.appendChild(g),o.push(new u(c,t.layout.keyWidths[a]||1,1,!0));var b=function(e){e.preventDefault(),0===s&&h(a,g)};g.addEventListener("touchstart",(function(e){e.preventDefault(),s=t.touchMouseThreshold,d(a,g)}),!0),g.addEventListener("touchend",(function(e){e.preventDefault(),s=t.touchMouseThreshold,h(a,g)}),!0),g.addEventListener("mousedown",(function(e){e.preventDefault(),0===s&&d(a,g)}),!0),g.addEventListener("mouseup",b,!0),g.addEventListener("mouseout",b,!0)}n.appendChild(c)}(f,e.layout)},Guacamole.OnScreenKeyboard.Layout=function(e){this.language=e.language,this.type=e.type,this.keys=e.keys,this.layout=e.layout,this.width=e.width,this.keyWidths=e.keyWidths||{}},Guacamole.OnScreenKeyboard.Key=function(e,t){this.name=t||e.name,this.title=e.title||this.name,this.keysym=e.keysym||function(e){if(!e||1!==e.length)return null;var t=e.charCodeAt(0);return t>=0&&t<=255?t:t>=256&&t<=1114111?16777216|t:null}(this.title),this.modifier=e.modifier,this.requires=e.requires||[]},(Guacamole=Guacamole||{}).OutputStream=function(e,t){var n=this;this.index=t,this.onack=null,this.sendBlob=function(t){e.sendBlob(n.index,t)},this.sendEnd=function(){e.endStream(n.index)}},(Guacamole=Guacamole||{}).Parser=function(){var e=this,t="",n=[],a=-1,o=0;this.receive=function(r){for(o>4096&&a>=o&&(t=t.substring(o),a-=o,o=0),t+=r;a<t.length;){if(a>=o){var i=t.substring(o,a),s=t.substring(a,a+1);if(n.push(i),";"==s){var u=n.shift();null!=e.oninstruction&&e.oninstruction(u,n),n.length=0}else if(","!=s)throw new Error("Illegal terminator.");o=a+1}var l=t.indexOf(".",o);if(-1==l){o=t.length;break}var c=parseInt(t.substring(a+1,l));if(isNaN(c))throw new Error("Non-numeric character in element length.");a=(o=l+1)+c}},this.oninstruction=null},(Guacamole=Guacamole||{}).RawAudioFormat=function(e){this.bytesPerSample=e.bytesPerSample,this.channels=e.channels,this.rate=e.rate},Guacamole.RawAudioFormat.parse=function(e){var t,n=null,a=1;if("audio/L8;"===e.substring(0,9))e=e.substring(9),t=1;else{if("audio/L16;"!==e.substring(0,10))return null;e=e.substring(10),t=2}for(var o=e.split(","),r=0;r<o.length;r++){var i=o[r],s=i.indexOf("=");if(-1===s)return null;var u=i.substring(0,s),l=i.substring(s+1);switch(u){case"channels":a=parseInt(l);break;case"rate":n=parseInt(l);break;default:return null}}return null===n?null:new Guacamole.RawAudioFormat({bytesPerSample:t,channels:a,rate:n})},(Guacamole=Guacamole||{}).SessionRecording=function(e){var t=this,n=[],a=[],o=0,r=0,i=new Guacamole.SessionRecording._PlaybackTunnel,s=new Guacamole.Client(i),u=-1,l=null,c=null,d=null;s.connect(),s.getDisplay().showCursor(!1),e.oninstruction=function(e,i){var s=new Guacamole.SessionRecording._Frame.Instruction(e,i.slice());if(a.push(s),o+=s.getSize(),"sync"===e){var u=parseInt(i[0]),l=new Guacamole.SessionRecording._Frame(u,a);n.push(l),(1===n.length||o>=16384&&u-r>=5e3)&&(l.keyframe=!0,r=u,o=0),a=[],t.onprogress&&t.onprogress(t.getDuration())}};var h=function(e){return 0===n.length?0:e-n[0].timestamp},f=function e(t,a,o){if(t===a)return t;var r=Math.floor((t+a)/2),i=h(n[r].timestamp);return o<i&&r>t?e(t,r-1,o):o>i&&r<a?e(r+1,a,o):r},m=function(e){for(var t=n[e],a=0;a<t.instructions.length;a++){var o=t.instructions[a];i.receiveInstruction(o.opcode,o.args)}t.keyframe&&!t.clientState&&s.exportState((function(e){t.clientState=e}))},p=function e(a,o,r){g(),d=window.setTimeout((function(){var i;for(i=a;i>=0;i--){var l=n[i];if(i===u)break;if(l.clientState){s.importState(l.clientState);break}}i++;for(var c=(new Date).getTime();i<=a;i++){if((new Date).getTime()-c>=5)break;m(i)}u=i-1,t.onseek&&t.onseek(t.getPosition()),u!==a?e(a,o,Math.max(r-((new Date).getTime()-c),0)):o()}),r||0)},g=function(){window.clearTimeout(d)},v=function e(){if(u+1<n.length){var a=n[u+1].timestamp-l+c,o=Math.max(a-(new Date).getTime(),0);p(u+1,(function(){e()}),o)}else t.pause()};this.onprogress=null,this.onplay=null,this.onpause=null,this.onseek=null,this.connect=function(t){e.connect(t)},this.disconnect=function(){e.disconnect()},this.getDisplay=function(){return s.getDisplay()},this.isPlaying=function(){return!!l},this.getPosition=function(){return-1===u?0:h(n[u].timestamp)},this.getDuration=function(){return 0===n.length?0:h(n[n.length-1].timestamp)},this.play=function(){if(!t.isPlaying()&&u+1<n.length){t.onplay&&t.onplay();var e=n[u+1];l=e.timestamp,c=(new Date).getTime(),v()}},this.seek=function(e,a){if(0!==n.length){var o=t.isPlaying();t.pause(),p(f(0,n.length-1,e),(function(){o&&t.play(),a&&a()}))}},this.pause=function(){g(),t.isPlaying()&&(t.onpause&&t.onpause(),l=null,c=null)}},Guacamole.SessionRecording._Frame=function(e,t){this.keyframe=!1,this.timestamp=e,this.instructions=t,this.clientState=null},Guacamole.SessionRecording._Frame.Instruction=function(e,t){var n=this;this.opcode=e,this.args=t,this.getSize=function(){for(var e=n.opcode.length,t=0;t<n.args.length;t++)e+=n.args[t].length;return e}},Guacamole.SessionRecording._PlaybackTunnel=function(){var e=this;this.connect=function(e){},this.sendMessage=function(e){},this.disconnect=function(){},this.receiveInstruction=function(t,n){e.oninstruction&&e.oninstruction(t,n)}},(Guacamole=Guacamole||{}).Status=function(e,t){var n=this;this.code=e,this.message=t,this.isError=function(){return n.code<0||n.code>255}},Guacamole.Status.Code={SUCCESS:0,UNSUPPORTED:256,SERVER_ERROR:512,SERVER_BUSY:513,UPSTREAM_TIMEOUT:514,UPSTREAM_ERROR:515,RESOURCE_NOT_FOUND:516,RESOURCE_CONFLICT:517,RESOURCE_CLOSED:518,UPSTREAM_NOT_FOUND:519,UPSTREAM_UNAVAILABLE:520,SESSION_CONFLICT:521,SESSION_TIMEOUT:522,SESSION_CLOSED:523,CLIENT_BAD_REQUEST:768,CLIENT_UNAUTHORIZED:769,CLIENT_FORBIDDEN:771,CLIENT_TIMEOUT:776,CLIENT_OVERRUN:781,CLIENT_BAD_TYPE:783,CLIENT_TOO_MANY:797},Guacamole.Status.Code.fromHTTPCode=function(e){switch(e){case 400:return Guacamole.Status.Code.CLIENT_BAD_REQUEST;case 403:return Guacamole.Status.Code.CLIENT_FORBIDDEN;case 404:return Guacamole.Status.Code.RESOURCE_NOT_FOUND;case 429:return Guacamole.Status.Code.CLIENT_TOO_MANY;case 503:return Guacamole.Status.Code.SERVER_BUSY}return Guacamole.Status.Code.SERVER_ERROR},Guacamole.Status.Code.fromWebSocketCode=function(e){switch(e){case 1e3:return Guacamole.Status.Code.SUCCESS;case 1006:case 1015:return Guacamole.Status.Code.UPSTREAM_NOT_FOUND;case 1001:case 1012:case 1013:case 1014:return Guacamole.Status.Code.UPSTREAM_UNAVAILABLE}return Guacamole.Status.Code.SERVER_ERROR},(Guacamole=Guacamole||{}).StringReader=function(e){var t=this,n=new Guacamole.ArrayBufferReader(e),a=0,o=0;n.ondata=function(e){var n=function(e){for(var t="",n=new Uint8Array(e),r=0;r<n.length;r++){var i=n[r];0===a?127==(127|i)?t+=String.fromCharCode(i):223==(31|i)?(o=31&i,a=1):239==(15|i)?(o=15&i,a=2):247==(7|i)?(o=7&i,a=3):t+="�":191==(63|i)?(o=o<<6|63&i,0==--a&&(t+=String.fromCharCode(o))):(a=0,t+="�")}return t}(e);t.ontext&&t.ontext(n)},n.onend=function(){t.onend&&t.onend()},this.ontext=null,this.onend=null},(Guacamole=Guacamole||{}).StringWriter=function(e){var t=this,n=new Guacamole.ArrayBufferWriter(e),a=new Uint8Array(8192),o=0;function r(e){var t,n;if(e<=127)t=0,n=1;else if(e<=2047)t=192,n=2;else if(e<=65535)t=224,n=3;else{if(!(e<=2097151))return void r(65533);t=240,n=4}!function(e){if(o+e>=a.length){var t=new Uint8Array(2*(o+e));t.set(a),a=t}o+=e}(n);for(var i=o-1,s=1;s<n;s++)a[i--]=128|63&e,e>>=6;a[i]=t|e}n.onack=function(e){t.onack&&t.onack(e)},this.sendText=function(e){e.length&&n.sendData(function(e){for(var t=0;t<e.length;t++)r(e.charCodeAt(t));if(o>0){var n=a.subarray(0,o);return o=0,n}}(e))},this.sendEnd=function(){n.sendEnd()},this.onack=null},(Guacamole=Guacamole||{}).Tunnel=function(){this.connect=function(e){},this.disconnect=function(){},this.sendMessage=function(e){},this.setState=function(e){e!==this.state&&(this.state=e,this.onstatechange&&this.onstatechange(e))},this.setUUID=function(e){this.uuid=e,this.onuuid&&this.onuuid(e)},this.isConnected=function(){return this.state===Guacamole.Tunnel.State.OPEN||this.state===Guacamole.Tunnel.State.UNSTABLE},this.state=Guacamole.Tunnel.State.CONNECTING,this.receiveTimeout=15e3,this.unstableThreshold=1500,this.uuid=null,this.onuuid=null,this.onerror=null,this.onstatechange=null,this.oninstruction=null},Guacamole.Tunnel.INTERNAL_DATA_OPCODE="",Guacamole.Tunnel.State={CONNECTING:0,OPEN:1,CLOSED:2,UNSTABLE:3},Guacamole.HTTPTunnel=function(e,t,n){var a=this,o=e+"?connect",r=e+"?read:",i=e+"?write:",s=1,u=!1,l="",c=!!t,d=null,h=null,f=null,m=n||{};function p(e,t){for(var n in t)e.setRequestHeader(n,t[n])}function g(){window.clearTimeout(d),window.clearTimeout(h),a.state===Guacamole.Tunnel.State.UNSTABLE&&a.setState(Guacamole.Tunnel.State.OPEN),d=window.setTimeout((function(){v(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_TIMEOUT,"Server timeout."))}),a.receiveTimeout),h=window.setTimeout((function(){a.setState(Guacamole.Tunnel.State.UNSTABLE)}),a.unstableThreshold)}function v(e){window.clearTimeout(d),window.clearTimeout(h),window.clearInterval(f),a.state!==Guacamole.Tunnel.State.CLOSED&&(e.code!==Guacamole.Status.Code.SUCCESS&&a.onerror&&(a.state!==Guacamole.Tunnel.State.CONNECTING&&e.code===Guacamole.Status.Code.RESOURCE_NOT_FOUND||a.onerror(e)),u=!1,a.setState(Guacamole.Tunnel.State.CLOSED))}function y(){if(a.isConnected())if(l.length>0){u=!0;var e=new XMLHttpRequest;e.open("POST",i+a.uuid),e.withCredentials=c,p(e,m),e.setRequestHeader("Content-type","application/octet-stream"),e.onreadystatechange=function(){4===e.readyState&&(g(),200!==e.status?w(e):y())},e.send(l),l=""}else u=!1}function w(e){var t=parseInt(e.getResponseHeader("Guacamole-Status-Code"));if(t){var n=e.getResponseHeader("Guacamole-Error-Message");v(new Guacamole.Status(t,n))}else e.status?v(new Guacamole.Status(Guacamole.Status.Code.fromHTTPCode(e.status),e.statusText)):v(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_NOT_FOUND))}function S(e){var t=null,n=null,o=0,r=-1,i=0,u=new Array;function l(){if(a.isConnected()){if(!(e.readyState<2)){var o;try{o=e.status}catch(e){o=200}if(n||200!==o||(n=b()),3===e.readyState||4===e.readyState){if(g(),1===s&&(3!==e.readyState||t?4===e.readyState&&t&&clearInterval(t):t=setInterval(l,30)),0===e.status)return void a.disconnect();if(200!==e.status)return void w(e);var c;try{c=e.responseText}catch(e){return}for(;r<c.length;){if(r>=i){var d=c.substring(i,r),h=c.substring(r,r+1);if(u.push(d),";"===h){var f=u.shift();a.oninstruction&&a.oninstruction(f,u),u.length=0}i=r+1}var m=c.indexOf(".",i);if(-1===m){i=c.length;break}var p=parseInt(c.substring(r+1,m));if(0===p){t&&clearInterval(t),e.onreadystatechange=null,e.abort(),n&&S(n);break}r=(i=m+1)+p}}}}else null!==t&&clearInterval(t)}e.onreadystatechange=1===s?function(){3===e.readyState&&++o>=2&&(s=0,e.onreadystatechange=l),l()}:l,l()}this.sendMessage=function(){if(a.isConnected()&&0!==arguments.length){for(var e=n(arguments[0]),t=1;t<arguments.length;t++)e+=","+n(arguments[t]);l+=e+=";",u||y()}function n(e){var t=new String(e);return t.length+"."+t}};var T=0;function b(){var e=new XMLHttpRequest;return e.open("GET",r+a.uuid+":"+T++),e.withCredentials=c,p(e,m),e.send(null),e}this.connect=function(e){g(),a.setState(Guacamole.Tunnel.State.CONNECTING);var t=new XMLHttpRequest;t.onreadystatechange=function(){4===t.readyState&&(200===t.status?(g(),a.setUUID(t.responseText),a.setState(Guacamole.Tunnel.State.OPEN),f=setInterval((function(){a.sendMessage("nop")}),500),S(b())):w(t))},t.open("POST",o,!0),t.withCredentials=c,p(t,m),t.setRequestHeader("Content-type","application/x-www-form-urlencoded; charset=UTF-8"),t.send(e)},this.disconnect=function(){v(new Guacamole.Status(Guacamole.Status.Code.SUCCESS,"Manually closed."))}},Guacamole.HTTPTunnel.prototype=new Guacamole.Tunnel,Guacamole.WebSocketTunnel=function(e){var t=this,n=null,a=null,o=null,r=null;if("ws:"!==e.substring(0,3)&&"wss:"!==e.substring(0,4)){var i={"http:":"ws:","https:":"wss:"}[window.location.protocol];if("/"===e.substring(0,1))e=i+"//"+window.location.host+e;else{var s=window.location.pathname.lastIndexOf("/"),u=window.location.pathname.substring(0,s+1);e=i+"//"+window.location.host+u+e}}function l(){window.clearTimeout(a),window.clearTimeout(o),t.state===Guacamole.Tunnel.State.UNSTABLE&&t.setState(Guacamole.Tunnel.State.OPEN),a=window.setTimeout((function(){c(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_TIMEOUT,"Server timeout."))}),t.receiveTimeout),o=window.setTimeout((function(){t.setState(Guacamole.Tunnel.State.UNSTABLE)}),t.unstableThreshold)}function c(e){window.clearTimeout(a),window.clearTimeout(o),window.clearInterval(r),t.state!==Guacamole.Tunnel.State.CLOSED&&(e.code!==Guacamole.Status.Code.SUCCESS&&t.onerror&&t.onerror(e),t.setState(Guacamole.Tunnel.State.CLOSED),n.close())}this.sendMessage=function(e){if(t.isConnected()&&0!==arguments.length){for(var a=r(arguments[0]),o=1;o<arguments.length;o++)a+=","+r(arguments[o]);a+=";",n.send(a)}function r(e){var t=new String(e);return t.length+"."+t}},this.connect=function(a){l(),t.setState(Guacamole.Tunnel.State.CONNECTING),(n=new WebSocket(e+"?"+a,"guacamole")).onopen=function(e){l(),r=setInterval((function(){t.sendMessage(Guacamole.Tunnel.INTERNAL_DATA_OPCODE,"ping",(new Date).getTime())}),500)},n.onclose=function(e){e.reason?c(new Guacamole.Status(parseInt(e.reason),e.reason)):e.code?c(new Guacamole.Status(Guacamole.Status.Code.fromWebSocketCode(e.code))):c(new Guacamole.Status(Guacamole.Status.Code.UPSTREAM_NOT_FOUND))},n.onmessage=function(e){l();var n,a=e.data,o=0,r=[];do{var i=a.indexOf(".",o);if(-1!==i)n=(o=i+1)+parseInt(a.substring(n+1,i));else c(new Guacamole.Status(Guacamole.Status.Code.SERVER_ERROR,"Incomplete instruction."));var s=a.substring(o,n),u=a.substring(n,n+1);if(r.push(s),";"===u){var d=r.shift();null===t.uuid&&(d===Guacamole.Tunnel.INTERNAL_DATA_OPCODE&&t.setUUID(r[0]),t.setState(Guacamole.Tunnel.State.OPEN)),d!==Guacamole.Tunnel.INTERNAL_DATA_OPCODE&&t.oninstruction&&t.oninstruction(d,r),r.length=0}o=n+1}while(o<a.length)}},this.disconnect=function(){c(new Guacamole.Status(Guacamole.Status.Code.SUCCESS,"Manually closed."))}},Guacamole.WebSocketTunnel.prototype=new Guacamole.Tunnel,Guacamole.ChainedTunnel=function(e){for(var t,n=this,a=[],o=null,r=0;r<arguments.length;r++)a.push(arguments[r]);function i(e){n.disconnect=e.disconnect,n.sendMessage=e.sendMessage;var r=function(t){if(t&&t.code===Guacamole.Status.Code.UPSTREAM_TIMEOUT)return a=[],null;var n=a.shift();return n&&(e.onerror=null,e.oninstruction=null,e.onstatechange=null,i(n)),n};function s(){e.onstatechange=n.onstatechange,e.oninstruction=n.oninstruction,e.onerror=n.onerror,e.onuuid=n.onuuid,e.uuid&&n.setUUID(e.uuid),o=e}e.onstatechange=function(e){switch(e){case Guacamole.Tunnel.State.OPEN:s(),n.onstatechange&&n.onstatechange(e);break;case Guacamole.Tunnel.State.CLOSED:!r()&&n.onstatechange&&n.onstatechange(e)}},e.oninstruction=function(e,t){s(),n.oninstruction&&n.oninstruction(e,t)},e.onerror=function(e){!r(e)&&n.onerror&&n.onerror(e)},e.connect(t)}this.connect=function(e){t=e;var r=o||a.shift();r?i(r):n.onerror&&n.onerror(Guacamole.Status.Code.SERVER_ERROR,"No tunnels to try.")}},Guacamole.ChainedTunnel.prototype=new Guacamole.Tunnel,Guacamole.StaticHTTPTunnel=function(e,t,n){var a=this,o=null,r=n||{};this.sendMessage=function(e){},this.connect=function(n){a.disconnect(),a.setState(Guacamole.Tunnel.State.CONNECTING),(o=new XMLHttpRequest).open("GET",e),o.withCredentials=!!t,function(e,t){for(var n in t)e.setRequestHeader(n,t[n])}(o,r),o.responseType="text",o.send(null);var i=0,s=new Guacamole.Parser;s.oninstruction=function(e,t){a.oninstruction&&a.oninstruction(e,t)},o.onreadystatechange=function(){if(3===o.readyState||4===o.readyState){a.setState(Guacamole.Tunnel.State.OPEN);var e=o.responseText,t=e.length;i<t&&(s.receive(e.substring(i)),i=t)}4===o.readyState&&a.disconnect()},o.onerror=function(){a.onerror&&a.onerror(new Guacamole.Status(Guacamole.Status.Code.fromHTTPCode(o.status),o.statusText)),a.disconnect()}},this.disconnect=function(){o&&(o.abort(),o=null),a.setState(Guacamole.Tunnel.State.CLOSED)}},Guacamole.StaticHTTPTunnel.prototype=new Guacamole.Tunnel,(Guacamole=Guacamole||{}).API_VERSION="1.3.0",(Guacamole=Guacamole||{}).VideoPlayer=function(){this.sync=function(){}},Guacamole.VideoPlayer.isSupportedType=function(e){return!1},Guacamole.VideoPlayer.getSupportedTypes=function(){return[]},Guacamole.VideoPlayer.getInstance=function(e,t,n){return null},module.exports=Guacamole;
