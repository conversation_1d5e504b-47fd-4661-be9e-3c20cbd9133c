<script setup lang="ts">
// 远程连接布局组件
onMounted(() => {
  window.addEventListener('storage', (event) => {
    if (event.key === 'logout-event') {
      // 重定向到登录页
      window.location.href = '/workbench/login'
    }
  })
})
</script>

<template>
    <div class="remote-layout">
        <div class="remote-content">
            <slot/>
        </div>
    </div>
</template>

<style scoped lang="less">
.remote-layout {
  width: 100vw;
  height: 100vh;
  background-color: #121212;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  header {
    width: 100%;
    height: 60px;
    background-color: #1a1a1a;
    padding: 0 20px;
    display: flex;
    align-items: center;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .logo-container {
      display: flex;
      align-items: center;

      img {
        height: 36px;
        width: auto;
      }
    }
  }

  .remote-content {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
}
</style>
