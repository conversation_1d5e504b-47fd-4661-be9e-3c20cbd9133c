<script setup lang="ts">
import {pick} from '@v-c/utils'
import BasicLayout from './basic-layout/index.vue'
import CustomerService from "@/components/customer-service/index.vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue"
defineOptions({
    name: 'ProLayout',
})
const appStore = useAppStore()
const {layoutSetting} = storeToRefs(appStore)
const userStore = useUserStore()
const layoutMenu = useLayoutMenu()
const {t} = useI18nLocale()
const {selectedKeys, openKeys} = storeToRefs(layoutMenu)
const {isMobile, isPad} = useQueryBreakpoints()
watch(isPad, (val) => {
    if (val)
        appStore.toggleCollapsed(true)
    else appStore.toggleCollapsed(false)
})
const layoutProps = computed(() =>
    pick(appStore.layoutSetting, [
        'fixedHeader',
        'fixedSider',
        'splitMenus',
        'menuHeader',
        'header',
        'menu',
        'layout',
        'footer',
        'contentWidth',
        'compactAlgorithm',
    ]),
)
</script>

<template>
    <BasicLayout
            :collapsed="layoutSetting.collapsed"
            :theme="layoutSetting.theme"
            :menu-data="userStore.menuData"
            v-bind="layoutProps"
            :selected-keys="selectedKeys"
            :open-keys="layoutSetting.layout === 'top' ? [] : openKeys"
            :copyright="layoutSetting.copyright"
            :is-mobile="isMobile"
            :logo="layoutSetting.logo"
            :header-logo="layoutSetting.headerLogo"
            :title="layoutSetting.title"
            :desc="layoutSetting.desc"
            :accordion-mode="layoutSetting.accordionMode"
            :left-collapsed="layoutSetting.leftCollapsed"
            :header-height="layoutSetting.headerHeight"
            @update:open-keys="layoutMenu.handleOpenKeys"
            @update:selected-keys="layoutMenu.handleSelectedKeys"
            @update:collapsed="appStore.toggleCollapsed"
    >
        <template #headerActions>
            <router-link to="/product">
                <img src="@/assets/images/common/recharge.webp" alt="" class="w-116px cursor-pointer mr-4px" />
            </router-link>
            <router-link to="/dashboard/my-promotion">
                <img src="@/assets/images/common/promotion.png" alt="" class="w-100px cursor-pointer mr-4px" />
            </router-link>
            <DownloadDialog class="mr-4px"/>
            <a href="https://www.yuque.com/zhaojie/kb/gqgy9bkz2x7dih7f?singleDoc" class="flex items-center cursor-pointer help-icon" target="_blank">
               <QuestionCircleOutlined class="w-14px h-14px"/>
                <!-- <span class="color-[#333333]">帮助</span> -->
            </a>
            <CustomerService />
            <a-divider type="vertical"/>
            <UserAvatar/>
        </template>
        <template #renderFooterLinks/>
        <a-watermark h-full flex flex-col flex-1
                     :content="!layoutSetting.watermark ? '' : layoutSetting.title ?? 'QinghuCloud'">
            <RouterView>
                <template #default="{ Component }">
                    <div :key="$route.path" h-full flex flex-col flex-1>
                        <component :is="Component"/>
                    </div>
                </template>
            </RouterView>
        </a-watermark>
    </BasicLayout>
</template>

<style lang="less" scoped>
.help-icon{
    display: inline-block;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #F2F3F5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4E5969;
}

.buy-vip-btn {
  display: flex;
  width: 108px;
  height: 32px;
  background: linear-gradient(116deg, #4255A1 2%, #2A2A69 100%);
  border-radius: 149px 149px 149px 149px;
  padding: 0 12px;
  align-items: center;
  cursor: pointer;
  user-select: none;

  &-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    margin-right: 4px;
  }

  font-size: 14px;
  color: #EFCFAB;
  line-height: 14px;
}
</style>
