<script setup lang="ts">

</script>

<template>
    <div class="main-login">
        <header>
            <img src="@/assets/images/logo.png" alt="云电脑">
        </header>
        <div class="main-login-box">
            <slot/>
        </div>
    </div>
</template>

<style scoped lang="less">
.main-login {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.05);
  //background-image: url("@/assets/images/login-bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  header {
    display: block;
    width: 100%;
    height: max-content;
    padding: 50px 0 0 50px;
    position: absolute;
    top: 0;
    left: 0;

    & > img {
      width: auto;
      height: 45px;
      object-fit: contain;
    }
  }

  .main-login-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
