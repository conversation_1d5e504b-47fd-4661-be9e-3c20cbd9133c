<script setup lang="ts">
import {useLayoutState} from '../../basic-layout/context'

const {logo, headerLogo, title, layout, isMobile} = useLayoutState()
const cls = computed(() => ({
    'ant-pro-global-header-logo': layout.value === 'mix' || isMobile.value,
    'ant-pro-top-nav-header-logo': layout.value === 'top' && !isMobile.value,
}))

function goIndexHome() {
    window.open('/')
}
</script>

<template>
    <div :class="cls" @click="goIndexHome">
        <a c-primary>
            <img :src="headerLogo">
        </a>
    </div>
</template>
