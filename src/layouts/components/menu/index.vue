<script setup lang="ts">
import {useLayoutState} from '../../basic-layout/context'
import SubMenu from './sub-menu.vue'
import type {VNodeChild} from "vue";
import {isFunction} from "@v-c/utils";

const {
    theme,
    collapsed,
    layout,
    isMobile,
    selectedMenus,
    selectedKeys,
    openKeys,
    handleOpenKeys,
    handleSelectedKeys,
    handleMenuSelect
} = useLayoutState()
const menuTheme = computed(() => {
    if (theme.value === 'inverted')
        return 'dark'
    return theme.value
})

function renderTitle(title: VNodeChild | (() => VNodeChild)) {
    if (isFunction(title))
        return title()

    return title
}
</script>

<template>
    <a-menu
            :selected-keys="selectedKeys"
            :open-keys="collapsed ? [] : openKeys"
            :mode="(layout === 'top' && !isMobile) ? 'horizontal' : 'inline'"
            :theme="menuTheme"
            :collapsed="collapsed"
            class="ant-pro-sider-menu"
            @update:open-keys="handleOpenKeys"
            @update:selected-keys="handleSelectedKeys"
            @select="handleMenuSelect"
    >
        <template v-for="item in selectedMenus">
            <template v-if="!item.onlyShowTitle && !item.hideInMenu">
                <SubMenu :key="item.path" :item="item"/>
                <template v-if="item?.hasDivider">
                    <a-divider w-168px min-w-168px mx-auto mt-10px mb-10px/>
                </template>
            </template>
            <template v-else-if="item.onlyShowTitle && !collapsed">
                <div class="main-onlyShowMenu-title">{{ renderTitle(item.title) }}</div>
            </template>
        </template>
    </a-menu>
</template>
<style lang="less" scoped>
.main-onlyShowMenu-title {
    padding: 16px 16px 16px 39px;
    color: #658D95;
    line-height: 22px;
}
</style>
