<script setup lang="ts">
import {isFunction, isUrl} from '@v-c/utils'
import type {VNodeChild} from 'vue'
import AsyncIcon from './async-icon.vue'
import type {MenuDataItem} from '~/layouts/basic-layout/typing'

withDefaults(defineProps<{ item: MenuDataItem, link?: boolean }>(), {
    link: true,
})

function renderTitle(title: VNodeChild | (() => VNodeChild)) {
    if (isFunction(title))
        return title()

    return title
}

// const tagNameArr = ['free-tag']
// const tagMapName = {'free-tag': '免费'}
</script>

<template>
    <template v-if="item.children && !item.hideChildrenInMenu">
        <a-sub-menu :key="item.path">
            <template v-if="item.icon" #icon>
                <AsyncIcon class="w-14px h-14px text-14px" :icon="item.icon"/>
            </template>
            <template #title>
                <div class="main-menu-title">{{ renderTitle(item.title) }}</div>
            </template>
            <template v-for="menu in item.children">
                <template v-if="!menu.hideInMenu">
                    <template v-if="menu.children">
                        <sub-menu :key="menu.path" :item="menu"/>
                    </template>
                    <template v-else>
                        <a-menu-item :key="menu.path" :title="renderTitle(menu.title)">
                            <template v-if="menu.icon" #icon>
                                <AsyncIcon :icon="menu.icon"/>
                            </template>
                            <template v-if="!isUrl(menu.path)">
                                <RouterLink v-if="link" :to="menu.path">
                                    <div class="main-menu-title">{{ renderTitle(menu.title) }}</div>
                                </RouterLink>
                                <template v-else>
                                    <div class="main-menu-title">{{ renderTitle(menu.title) }}</div>
                                </template>
                            </template>
                            <template v-else>
                                <a :href="menu.path" :target="menu.target ?? '_blank'">
                                    <div class="main-menu-title">{{ renderTitle(menu.title) }}</div>
                                </a>
                            </template>
                        </a-menu-item>
                    </template>
                </template>
            </template>
        </a-sub-menu>
    </template>
    <template v-else>
        <a-menu-item :key="item.path" :title="renderTitle(item.title)">
            <template v-if="item.icon" #icon>
                <AsyncIcon :icon="item.icon"/>
            </template>
            <template v-if="!isUrl(item.path)">
                <RouterLink v-if="link" :to="item.path">
                    <div class="main-menu-title">{{ renderTitle(item.title) }}</div>
                </RouterLink>
                <template v-else>
                    <div class="main-menu-title">{{ renderTitle(item.title) }}</div>
                </template>
            </template>
            <template v-else>
                <a class="main-menu-title" :href="item.path" :target="item.target ?? '_blank'">
                    <div class="main-menu-title">{{ renderTitle(item.title) }}</div>
                </a>
            </template>
            <!--            <span v-if="item.tagClass && tagNameArr.includes(item?.tagClass)"-->
            <!--                  :class="item?.tagClass">{{ tagMapName[item?.tagClass] }}</span>-->
        </a-menu-item>
    </template>
</template>

<style lang="less" scoped>
.main-menu-title {
  color: #333333;
}

.free-tag {
  width: max-content;
  height: 16px;
  background: linear-gradient(135deg, #FF9550 0%, #FF5400 100%);
  border-radius: 8px 8px 8px 1px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 5px;
  color: white;
  margin-left: 8px;
}
</style>
