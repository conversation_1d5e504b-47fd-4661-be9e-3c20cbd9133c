<script setup lang="ts">
import Header from '../components/header/index.vue'
import SiderMenu from '../components/sider-menu/index.vue'
import DrawerMenu from '../components/drawer-menu/index.vue'
import GlobalFooter from '../components/global-footer/index.vue'
import {proLayoutProps} from './typing'
import {useLayoutProvider} from './context'
import {handleOperateElectron} from "~/utils/common-utils.ts";

defineOptions({
    name: 'BasicLayout',
})
const props = defineProps(proLayoutProps)
const emit = defineEmits(['update:collapsed'])

const route = useRoute()
const isNestedElectron = route.query.isNestedElectron
const onlyClose = route.query.onlyClose

/**
 * 处理展开收起的事件参数
 * @param collapsed 展开收起的事件参数
 */
function handleCollapsed(collapsed: boolean) {
    emit('update:collapsed', collapsed)
    props?.onCollapsed?.(collapsed)
}

// 依赖注入所有的配置项，对属性进行控制，减少传值
const {layout, contentWidth} = useLayoutProvider(props, {
    handleCollapsed,
})
const contentCls = computed(() => {
    if (contentWidth.value === 'Fixed')
        return 'ant-pro-basicLayout-content-fixed'

    else
        return ''
})

const isShow = ref<boolean>(false)

</script>

<template>
    <div class="ant-pro-basicLayout" :data-theme="theme">
      <template v-if="isNestedElectron">
        <a-layout>
<!--          嵌套在桌面端的标题栏 保持桌面端样式一致-->
          <div class="w-full h-10 flex justify-end items-center px-5 pos-absolute top-0 left-0 bg-transparent z-99999 app-region-drag">
            <div class="flex items-center gap-2 app-region-no-drag" @mouseleave="isShow = false" @mouseenter="isShow = true">

              <template v-if="!onlyClose">
                <div class="w-14px h-14px flex-center bg-[#F4BE4F] border-1px border-solid border-#C3983F rounded-full cursor-pointer"
                     @click="handleOperateElectron('child:window-minimize')">
                  <img v-if="isShow" src="@/assets/images/min.webp" class="w-8px h-1px" alt="">
                </div>
                <div class="w-14px h-14px flex-center bg-[#64c956] border-1px border-solid border-#48b33b rounded-full cursor-pointer"
                     @click="handleOperateElectron('child:window-maximize')">
                  <img v-if="isShow" src="@/assets/images/max.webp" class="w-7px h-7px" alt="">
                </div>
              </template>
              <template v-else>
                <div class="w-14px h-14px flex-center bg-[#F4BE4F] border-1px border-solid border-#C3983F rounded-full cursor-pointer" />
                <div class="w-14px h-14px flex-center bg-[#64c956] border-1px border-solid border-#48b33b rounded-full cursor-pointer" />
              </template>

              <div
                  class="w-14px h-14px flex-center bg-[#EC6A5E] border-1px border-solid border-#BD564C rounded-full cursor-pointer"
                  @click="handleOperateElectron('windows:closeChildWindow')">
                <img  v-if="isShow" src="@/assets/images/close.webp" class="w-10px h-10px" alt="">
              </div>
            </div>
          </div>

          <a-layout-content class="ant-pro-basicLayout-content" flex flex-col>
            <div h-full flex flex-col flex-1 :class="contentCls">
              <slot/>
            </div>
          </a-layout-content>
        </a-layout>
      </template>
      <template v-else>
        <a-layout>
          <template v-if="menu">
            <SiderMenu v-if="layout !== 'top' && !isMobile"/>
          </template>
          <a-layout>
            <template v-if="header">
              <Header>
                <template v-if="$slots.headerActions" #headerActions>
                  <slot name="headerActions"/>
                </template>
                <template #headerContent>
                  <slot name="headerContent">
                    <!-- 分割菜单的模式 -->
                    <div/>
                  </slot>
                </template>
              </Header>
            </template>
            <slot name="contentPrefix"/>
            <a-layout-content class="ant-pro-basicLayout-content" flex flex-col>
              <div h-full flex flex-col flex-1 :class="contentCls">
                <slot/>
              </div>
            </a-layout-content>
            <a-layout-footer v-if="footer" style="background-color: transparent;">
              <slot name="footerRender">
                <GlobalFooter :copyright="copyright">
                  <template v-if="$slots.renderFooterLinks" #renderFooterLinks/>
                </GlobalFooter>
              </slot>
            </a-layout-footer>
          </a-layout>
        </a-layout>
        <DrawerMenu v-if="menu"/>
      </template>
    </div>
</template>

<style lang="less">
@import 'index.less';
</style>
