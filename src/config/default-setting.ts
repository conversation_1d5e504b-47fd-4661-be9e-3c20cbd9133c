import type {LayoutSetting} from '~/stores/app'
import Logo from '/logo.png'
import Header<PERSON>ogo from '@/assets/images/common/w-logo.png'

export default {
    'title': '云电脑',
    'theme': 'light',
    'logo': Logo,
    'headerLogo': Header<PERSON>ogo,
    'desc': '',
    'collapsed': false,
    'drawerVisible': true,
    'colorPrimary': '#08C18A',
    'layout': 'mix',
    'contentWidth': 'Fluid',
    'fixedHeader': true,
    'fixedSider': true,
    'splitMenus': false,
    'header': true,
    'menu': true,
    'watermark': false,
    'menuHeader': true,
    'footer': false,
    'colorWeak': false,
    'colorGray': false,
    'keepAlive': false,
    'accordionMode': false,
    'leftCollapsed': true,
    'compactAlgorithm': false,
    'headerHeight': 60,
    'copyright': 'QingHuCloud 2025',
    'animationName': 'slide-fadein-right',
} as LayoutSetting

export const animationNameList = [
    {
        label: 'None',
        value: 'none',
    },
    {
        label: 'Fadein Up',
        value: 'slide-fadein-up',
    },
    {
        label: 'Fadein Right',
        value: 'slide-fadein-right',
    },
    {
        label: 'Zoom Fadein',
        value: 'zoom-fadein',
    },
    {
        label: 'Fadein',
        value: 'fadein',
    },
]
export type AnimationNameValueType = 'none' | 'slide-fadein-up' | 'slide-fadein-right' | 'zoom-fadein' | 'fadein'
