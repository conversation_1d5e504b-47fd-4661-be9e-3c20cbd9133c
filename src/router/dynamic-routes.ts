/**
 * 前端静态路由
 */
import type {RouteRecordRaw} from 'vue-router'
import {basicRouteMap} from './router-modules'
import HomeIcon from '@/Icons/home.svg?component'
import BuyIcon from '@/Icons/buy.svg?component'
import DesktopIcon from '@/Icons/desktop.svg';
import LoginLogIcon from '@/Icons/loginLog.svg';
import OrderIcon from '@/Icons/order.svg';
import RechargeIcon from '@/Icons/recharge.svg';
import CloseLogIcon from '@/Icons/closeLog.svg';
import TickerIcon from '@/Icons/ticker.svg';

export default [
    {
        path: '/',
        redirect: '/dashboard',
        name: 'Dashboard',
        meta: {
            title: '首页',
            icon: () => HomeIcon,
            hideChildrenInMenu: true,
        },
        component: basicRouteMap.RouteView,
        children: [
            {
                path: '/dashboard',
                name: 'Dashboard',
                 component: () => import('~/pages/dashboard/workbench/index.vue'),
                meta: {
                    title: '首页',
                    parentKeys: ['/'],
                },
            },
            {
                path: '/dashboard/auth',
                name: 'Auth',
                component: () => import('~/pages/dashboard/workbench/auth.vue'),
                meta: {
                    title: '认证',
                    parentKeys: ['/'],
                },
            },
            {
                path: '/dashboard/user',
                name: 'Person',
                component: () => import('~/pages/dashboard/workbench/personal.vue'),
                meta: {
                    title: '个人中心',
                    parentKeys: ['/'],
                },
            },
            {
                path: '/dashboard/recharge',
                name: 'Recharge',
                component: () => import('~/pages/dashboard/workbench/recharge.vue'),
                meta: {
                    title: '充值',
                    parentKeys: ['/'],
                },
            },
            {
                path: '/dashboard/coupons',
                name: 'Coupons',
                component: () => import('~/pages/dashboard/workbench/coupons.vue'),
                meta: {
                    title: '优惠券管理',
                    parentKeys: ['/'],
                },
            },
            {
                path: '/dashboard/my-promotion',
                name: 'myPromotion',
                component: () => import('~/pages/dashboard/workbench/my-promotion/index.vue'),
                meta: {
                    title: '推广',
                    parentKeys: ['/'],
                },
            },
        ],
    },
    {
        path: '/product',
        name: 'Product',
        meta: {
            title: '产品购买',
            icon: () => BuyIcon,
            // hasDivider: true,
        },
        component: () => import('~/pages/dashboard/product/index.vue'),
    },
    {
        path: '/cloudDesktop',
        name: 'CloudDesktop',
        meta: {
            title: '云电脑管理',
            icon: () => DesktopIcon,
            // hasDivider: true,
        },
        component: () => import('~/pages/cloud-desktop-manage/cloud-desktop-list/index.vue'),
    },
    {
        path: '/product/pay',
        name: 'ProductPay',
        meta: {
            title: '购买云电脑',
            icon: 'UserOutlined',
            hideInMenu: true,
        },
        component: () => import('~/pages/dashboard/product/pay.vue'),
    },
    // {
    //     path: '/ip/management',
    //     name: 'ipManagement',
    //     meta: {
    //         title: 'IP管理',
    //         icon: 'UserOutlined',
    //         // hasDivider: true,
    //     },
    //     component: () => import('~/pages/common/dev.vue'),
    // },
    {
        path: '/safety-manage',
        redirect: '/safety-manage/log',
        name: 'Goods',
        meta: {
            title: '安全管理',
            icon: 'ShoppingOutlined',
            onlyShowTitle: true,
        },
    },
    {
        path: '/safety-manage/log',
        name: 'Log',
        component: () => import('~/pages/safety-manage/log.vue'),
        meta: {
            title: '登录日志',
            icon: () => LoginLogIcon,
            parentKeys: ['/safety-manage/log'],
        },
    },
    {
        path: '/safety-manage/details',
        name: 'Details',
        component: () => import('~/pages/safety-manage/switch-details.vue'),
        meta: {
            title: '开关机明细',
            icon: () => CloseLogIcon,
            parentKeys: ['/safety-manage/details'],
        },
    },
    {
        path: '/cost',
        redirect: '/cost/order',
        name: 'Cost',
        meta: {
            title: '费用管理',
            icon: 'ShoppingOutlined',
            onlyShowTitle: true,
        },
    },
    {
        path: '/cost/order',
        name: 'Order',
        component: () => import('~/pages/dashboard/cost/order.vue'),
        meta: {
            title: '订单管理',
            icon: () => OrderIcon,
            parentKeys: ['/cost/order'],
        },
    },
    {
        path: '/cost/rechargeRecord',
        name: 'RechargeRecord',
        component: () => import('~/pages/dashboard/cost/rechargeRecord.vue'),
        meta: {
            title: '充值记录',
            icon: () => RechargeIcon,
            parentKeys: ['/cost/rechargeRecord'],
        },
    }
] as RouteRecordRaw[]
