import type {RouteRecordRaw} from 'vue-router'

const Layout = () => import('~/layouts/index.vue')

export default [
    {
        path: '/login',
        component: () => import('~/pages/common/login.vue'),
        meta: {
            title: '登录',
        },
    },
    {
        path: '/resetPassword',
        component: () => import('~/pages/common/reset-password.vue'),
        meta: {
            title: '重置密码',
        },
    },
    {
        path: '/401',
        name: 'Error401',
        component: () => import('~/pages/exception/401.vue'),
        meta: {
            title: '授权已过期',
        },
    },
    // 远程连接页面使用独立路由
    {
        path: '/cloud-desktop-connect',
        name: 'CloudDesktopConnect',
        component: () => import('~/pages/cloud-desktop-manage/cloud-desktop-connect/index.vue'),
        meta: {
            title: '远程连接',
            icon: 'LinkOutlined',
        },
    },
    {
        path: '/common',
        name: 'LayoutBasicRedirect',
        component: Layout,
        redirect: '/common/redirect',
        children: [
            {
                path: '/common/redirect',
                component: () => import('~/pages/common/route-view.vue'),
                name: 'CommonRedirect',
                redirect: '/redirect',
                children: [
                    {
                        path: '/redirect/:path(.*)',
                        name: 'RedirectPath',
                        component: () => import('~/pages/common/redirect.vue'),
                    },
                ],
            },

        ],
    },
    {
        path: '/:pathMatch(.*)',
        meta: {
            title: '找不到页面',
        },
        component: () => import('~/pages/exception/error.vue'),
    }
] as RouteRecordRaw[]
