import { AxiosError } from 'axios';
import router from '~/router/index';
import { useMetaTitle } from '~/composables/meta-title';
import { setRouteEmitter } from '~/utils/route-listener';
import { ROOT_ROUTE_REDIRECT_PATH } from '~/router/constant.ts';

const allowList = ['/login', '/resetPassword', '/signup', '/error', '/401', '/404', '/403', '/500'];
const loginPath = '/login';

router.beforeEach(async (to, _, next) => {
    setRouteEmitter(to);

    // 获取用户 store 和 token
    const userStore = useUserStore();
    const token = useAuthorization();

    // 检查 URL 中是否有 token 参数
    const urlToken = to.query.token;
    const onlyClose = to.query.onlyClose;
    const urlCode = to.query.urlCode as string;
    if (urlCode) {
        localStorage.setItem('urlCode', urlCode)
    }
    if (typeof urlToken === 'string') {
        token.value = urlToken;
        // 桌面端登录使用
        if (!urlToken){
            return next({
                path: loginPath,
                query: {
                    isNestedElectron: true,
                    urlCode,
                },
                replace: true,
            });
        }
        // 其他需要将网页嵌入
        delete to.query.token;
        next({
            path: to.path,
            query: {
                ...to.query,
                isNestedElectron: true,
                onlyClose
            },
            replace: true,
        });
        return;
    }

    if (!token.value) {
        // 如果 token 不存在且不在允许列表中，则跳转到登录页面
        console.log('token', token.value, !allowList.includes(to.path) && !to.path.startsWith('/redirect'), to.path,to.path.includes('/product'));

        if (!userStore.routerData && to.path == '/product' && !to.path.startsWith('/redirect')) {
            const currentRoute = await userStore.generateDynamicRoutes();
            router.addRoute(currentRoute);
            next({
                ...to,
                replace: true,
            });
            return
        } else if (userStore.routerData && to.path == '/product' && !to.path.startsWith('/redirect')) {
            next();
            return
        } else if (!allowList.includes(to.path) && !to.path.startsWith('/redirect')) {
            next({
                path: loginPath,
                query: {
                    redirect: encodeURIComponent(to.fullPath),
                },
            });
            return;
        }
    } else {
        if (!userStore.routerData && !allowList.includes(to.path) && !to.path.startsWith('/redirect')) {
            try {
                // 获取路由菜单的信息
                const currentRoute = await userStore.generateDynamicRoutes();
                router.addRoute(currentRoute);
                await userStore?.getUserInfo();
                next({
                    ...to,
                    replace: true,
                });
                return;
            } catch (e) {
                if (e instanceof AxiosError) {
                    switch (e?.response?.status) {
                        case 401: {
                            next({
                                path: '/401',
                            });
                            break;
                        }
                        case 403: {
                            next({
                                path: '/403',
                            });
                            break;
                        }
                        case 500: {
                            next({
                                path: '/500',
                            });
                            break;
                        }
                        default: {
                            next({ path: '/error' });
                            break;
                        }
                    }
                }
            }
        } else {
            // 如果当前是登录页面就跳转到首页
            if (to.path === loginPath) {
                next({
                    path: ROOT_ROUTE_REDIRECT_PATH,
                });
                return;
            }
        }
    }
    next();
});

router.afterEach((to) => {
    useMetaTitle(to);
    useLoadingCheck();
    useScrollToTop();
});
