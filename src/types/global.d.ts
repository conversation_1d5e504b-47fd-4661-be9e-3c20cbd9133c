// 全局Guacamole对象类型声明
interface Window {
    Guacamole: {
        /**
         * Guacamole客户端主类
         */
        Client: new (tunnel: any) => any;

        /**
         * WebSocket隧道实现
         */
        WebSocketTunnel: new (url: string) => any;

        /**
         * HTTP隧道实现
         */
        HTTPTunnel: new (url: string) => any;

        /**
         * 显示模块
         */
        Display: new () => any;

        /**
         * 鼠标输入处理器
         */
        Mouse: {
            new (element: HTMLElement): {
                onmousedown: (state: any) => void;
                onmousemove: (state: any) => void;
                onmouseup: (state: any) => void;
                setClient: (client: any) => void;
            };
            /**
             * 触摸屏处理器
             * 将触摸事件转换为鼠标事件
             */
            Touchscreen: new (element: HTMLElement) => {
                onmousedown: (state: any) => void;
                onmousemove: (state: any) => void;
                onmouseup: (state: any) => void;
                setClient: (client: any) => void;
            };
        };

        /**
         * 键盘输入处理器
         */
        Keyboard: new (element: any) => any;

        /**
         * 链式隧道实现
         */
        ChainedTunnel: new (tunnels: any[]) => any;

        /**
         * 数组缓冲区读取器
         */
        ArrayBufferReader: new (stream: any) => any;

        /**
         * 状态类
         */
        Status: any;

        /**
         * 输入流
         */
        InputStream: new (client: any, index: number) => any;

        /**
         * 输出流
         */
        OutputStream: new (client: any, index: number) => any;

        /**
         * 指令流解析器
         */
        Parser: new () => any;

        /**
         * 视频播放器
         */
        VideoPlayer: {
            getInstance(stream: any, layer: any, mimetype: string): any;
        };

        /**
         * 音频播放器
         */
        AudioPlayer: {
            getInstance(stream: any, mimetype: string): any;
        };

        /**
         * 原始音频播放器实现
         */
        RawAudioPlayer: {
            prototype: any;
            isSupportedType(mimetype: string): boolean;
            getSupportedTypes(): string[];
            new(stream: any, mimetype: string): any;
        };

        /**
         * 音频上下文工厂
         */
        AudioContextFactory: {
            singleton: AudioContext | null;
            getAudioContext(): AudioContext | null;
        };
    };
    electron: {
        ipcRenderer: {
            invoke: (channel: string, ...args: any[]) => any;
        };
    };
    webviewApi: {
        invoke: (channel: string, ...args: any[]) => any;
    };

    // 添加腾讯云验证码类型定义
    TencentCaptcha: new (appId: string, callback: (res: any) => void) => {
        show: () => void;
        destroy: () => void;
    };
}
