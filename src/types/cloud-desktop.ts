/**
 * 云电脑相关类型定义
 */

/**
 * 云电脑记录类型
 */
export interface CloudDesktopRecord {
    id?: number
    logicId?: string
    desktopName?: string
    networkAddress?: string
    networkPort?: number
    region?: string
    zoneId?: string
    zoneName?: string
    imageId?: string
    expreTime?: string
    expreDays?: number
    expreState?: string
    itemId?: number
    instanceId?: string
    deviceState?: string
    remoteState?: string
    instanceState?: string
    workspaceId?: number

    [key: string]: any
}

/**
 * 云电脑操作类型
 */
export type CloudDesktopOperationType = 'start' | 'stop' | 'localRemote' | 'networkRemote' | 'renew' | 'detail' | 'edit'

/**
 * 云电脑详情类型
 */
export interface CloudDesktopDetail {
    desktopName?: string                 // 云电脑名称
    networkAddress?: string              // 网络地址
    networkPort?: number                 // 网络端口
    zoneName?: string                    // 地区/可用区
    volumeName?: string                  // 云盘描述
    vcpu?: string                        // vcpu规格
    memory?: string                      // 内存规格
    stMenu?: string                      // 套餐
    systemDisk?: string                  // 系统盘规格
    systemName?: string                  // 系统名称
    runRecordsList?: CloudDesktopRunRecord[] // 运行记录列表
    eipRecords?: CloudDesktopEffectRecord[]  // EIP记录(生效记录)
}


/**
 * 云电脑过滤条件
 */
export interface CloudDesktopFilterForm {
    expreState?: string  // 到期状态
    deviceState?: string // 设备状态
    remoteState?: string // 远程状态
    pageNum?: number     // 页码
    pageSize?: number    // 每页条数

    [key: string]: any
}

/**
 * 云电脑运行记录类型
 */
export interface CloudDesktopRunRecord {
    /**
     * 关机时间
     */
    offTime?: string;
    /**
     * 开机时间
     */
    onTime?: string;
    /**
     * 耗时
     */
    timeConsume?: string;
    [property: string]: any;
}

/**
 * 云电脑生效记录类型
 */
export interface CloudDesktopEffectRecord {
    renewalTime?: string  // 续费时间
    networkAddress?: string // 网络地址
    zoneName?: string    // 可用区名称
    volumeName?: string  // 云盘描述
    vCpu?: string        // vCpu规格
    memory?: string      // 内存规格
    systemDisk?: string  // 系统盘规格
    systemName?: string  // 系统名称
    type?: string        // 类型
}

/**
 * 云电脑续费记录类型
 */
export interface CloudDesktopRenewRecord {
    id: string
    renewTime: string
    amount: number
    duration: string
    orderId: string
    status: string
    payMethod?: string
}

/**
 * 会话请求参数类型
 */
export interface SessionRequestParams {
    usiId?: string;     // 实例ID
    workspaceId?: number;    // 工作区ID
    terminalType?: string;    // 终端类型
    clientInfo?: string;     // 客户端信息
}

/**
 * 连接详情返回类型
 */
export interface ConnectionDetails {
    sessionId: string;      // 会话ID
    ip: string;      // ip
    tunnelUrl: string;      // 隧道URL
    port: string;      // 端口
    workspaceId: number;    // 工作区ID
    protocol: string;       // 协议类型

}

export interface InstanceRenew {
    desktopName?: string; //云桌面名称
    expreDays?: number; //到期天数
    expreState?: string; //到期状态 DUE("DUE","即将到期"),CAN_BE_RENEWED("CAN_BE_RENEWED","已过期，去续费"),CLEAR("CLEAR","已过期"),
    expreTime?: string; //到期时间
    id?: number; //实例id
    itemId?: number; //实例id
    monthPrice?: number; //每月价格（分）
    networkAddress?: string; //网络地址
    zoneName?: string; //地区
    [property: string]: any;
}


export interface CloudDesktoLabelItem{
    id?: number
    label?: string
    parentId?: number

}