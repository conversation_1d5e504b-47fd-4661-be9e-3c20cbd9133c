/**
 * Guacamole 类型定义文件
 * 基于 guacamole-common-js 库
 */

declare namespace Guacamole {
    /**
     * Guacamole JavaScript API的唯一ID
     * 对应guacamole-common-js Maven项目的版本字符串
     */
    const API_VERSION: string;

    /**
     * 获取指定按键的Keysym编码
     * @param keyCode 键码
     * @returns 对应的keysym编码
     */
    function keysymFromKeyCode(keyCode: number): number;

    /**
     * Guacamole 客户端主类
     * 用于处理远程桌面连接和显示
     */
    class Client {
        /**
         * 创建一个新的 Guacamole 客户端实例
         * @param tunnel 用于通信的隧道对象
         */
        constructor(tunnel: Tunnel);

        /**
         * 客户端的当前状态
         * 0: 空闲 (IDLE)
         * 1: 正在连接 (CONNECTING)
         * 2: 等待握手 (WAITING)
         * 3: 已连接 (CONNECTED)
         * 4: 正在断开连接 (DISCONNECTING)
         * 5: 已断开连接 (DISCONNECTED)
         */
        readonly STATE_IDLE: number;
        readonly STATE_CONNECTING: number;
        readonly STATE_WAITING: number;
        readonly STATE_CONNECTED: number;
        readonly STATE_DISCONNECTING: number;
        readonly STATE_DISCONNECTED: number;

        /**
         * 当前连接状态
         */
        readonly currentState: number;

        /**
         * 获取客户端的显示对象
         * @returns 与此客户端关联的显示对象
         */
        getDisplay(): Display;

        /**
         * 获取客户端当前的时间戳
         * @returns 客户端时间戳
         */
        getTimestamp(): number;

        /**
         * 发送鼠标状态到远程服务器
         * @param state 鼠标状态对象
         */
        sendMouseState(state: Mouse.State): void;

        /**
         * 发送键盘事件到远程服务器
         * @param pressed 是否按下按键(1表示按下，0表示释放)
         * @param keysym 按键符号
         */
        sendKeyEvent(pressed: number, keysym: number): void;

        /**
         * 将剪贴板内容发送到远程服务器
         * @param mimetype 内容的MIME类型
         * @param data 剪贴板内容
         */
        setClipboard(mimetype: string, data: string): void;


        /**
         * 将剪贴板内容发送到远程服务器
         * @param mimetype 内容的MIME类型
         * @param data 剪贴板内容
         */
        createClipboardStream(data: string): OutputStream;

        /**
         * 创建一个发送剪贴板数据到服务器的输出流
         * @returns 一个可以发送剪贴板数据的输出流
         */
        createClipboardOutputStream(): OutputStream;

        /**
         * 连接到 Guacamole 服务器
         * @param data 连接过程中要发送的任意数据
         * @throws 如果连接过程中发生错误则抛出异常
         */
        connect(data?: any): void;

        /**
         * 断开与 Guacamole 服务器的连接
         */
        disconnect(): void;

        /**
         * 向服务器发送确认消息
         * @param streamIndex 流索引
         * @param message 确认消息
         * @param code 状态码
         */
        sendAck(streamIndex: number, message: string, code: number): void;

        /**
         * 发送当前屏幕尺寸到远程服务器
         * @param width 屏幕宽度
         * @param height 屏幕高度
         */
        sendSize(width: number, height: number): void;

        /**
         * 当客户端收到名称指令时触发
         * @param name 名称参数
         */
        onname?: (name: string) => void;

        /**
         * 当客户端收到剪贴板数据时触发
         * @param stream 输入流
         * @param mimetype 数据的MIME类型
         */
        onclipboard?: (stream: InputStream, mimetype: string) => void;

        /**
         * 当客户端收到管道数据时触发
         * @param stream 输入流
         * @param mimetype 数据的MIME类型
         * @param name 管道名称
         */
        onpipe?: (stream: InputStream, mimetype: string, name: string) => void;

        /**
         * 当收到视频流时触发
         * @param stream 输入流
         * @param layer 视频所在的图层
         * @param mimetype 视频的MIME类型
         * @returns 用于处理视频的播放器实例
         */
        onvideo?: (stream: InputStream, layer: Display.Layer, mimetype: string) => VideoPlayer | null;

        /**
         * 当收到音频流时触发
         * @param stream 输入流
         * @param mimetype 音频的MIME类型
         * @returns 用于处理音频的播放器实例
         */
        onaudio?: (stream: InputStream, mimetype: string) => AudioPlayer | null;

        /**
         * 当收到必要功能指令时触发
         * @param parameters 参数列表
         */
        onrequired?: (parameters: string[]) => void;

        /**
         * 当连接状态改变时触发
         * @param state 新的状态
         */
        onstatechange?: (state: number) => void;

        /**
         * 当连接出错时触发
         * @param status 错误状态
         */
        onerror?: (status: Status) => void;
    }

    /**
     * Guacamole 状态类
     * 表示远程桌面连接的状态和错误
     */
    class Status {
        /**
         * 状态代码
         */
        readonly code: number;

        /**
         * 状态消息
         */
        readonly message: string;
    }

    /**
     * 隧道接口
     * 抽象了与 Guacamole 服务器通信的方法
     */
    interface Tunnel {
        /**
         * 连接到隧道
         * @param data 连接数据
         */
        connect(data?: any): void;

        /**
         * 断开隧道连接
         */
        disconnect(): void;

        /**
         * 发送消息到服务器
         * @param elements 消息元素
         */
        sendMessage(...elements: string[]): void;

        /**
         * 当收到指令时触发
         * @param opcode 操作码
         * @param parameters 参数数组
         */
        oninstruction?: (opcode: string, parameters: string[]) => void;

        /**
         * 当隧道状态改变时触发
         * @param state 新的状态
         */
        onstatechange?: (state: number) => void;

        /**
         * 当发生错误时触发
         * @param status 错误状态
         */
        onerror?: (status: Status) => void;
    }

    /**
     * WebSocket 隧道实现
     * 使用 WebSocket 协议与 Guacamole 服务器通信
     */
    class WebSocketTunnel implements Tunnel {
        /**
         * 创建一个新的 WebSocket 隧道
         * @param url WebSocket 服务器的 URL
         */
        constructor(url: string);

        connect(data?: any): void;

        disconnect(): void;

        sendMessage(...elements: string[]): void;

        oninstruction?: (opcode: string, parameters: string[]) => void;
        onstatechange?: (state: number) => void;
        onerror?: (status: Status) => void;
    }

    /**
     * HTTP 隧道实现
     * 使用 HTTP 长轮询与 Guacamole 服务器通信
     */
    class HTTPTunnel implements Tunnel {
        /**
         * 创建一个新的 HTTP 隧道
         * @param url HTTP 服务器的 URL
         */
        constructor(url: string);

        connect(data?: any): void;

        disconnect(): void;

        sendMessage(...elements: string[]): void;

        oninstruction?: (opcode: string, parameters: string[]) => void;
        onstatechange?: (state: number) => void;
        onerror?: (status: Status) => void;
    }

    /**
     * 链式隧道实现
     * 按顺序尝试多个隧道连接方式，直到一个成功
     */
    class ChainedTunnel implements Tunnel {
        /**
         * 创建一个新的链式隧道
         * @param tunnels 要尝试的隧道数组
         */
        constructor(tunnels: Tunnel[]);

        connect(data?: any): void;

        disconnect(): void;

        sendMessage(...elements: string[]): void;

        oninstruction?: (opcode: string, parameters: string[]) => void;
        onstatechange?: (state: number) => void;
        onerror?: (status: Status) => void;
    }

    /**
     * 输入流
     * 表示从服务器接收数据的流
     */
    class InputStream {
        /**
         * 创建一个新的输入流
         * @param client 关联的客户端
         * @param index 流索引
         */
        constructor(client: Client, index: number);

        /**
         * 当收到数据块时触发
         * @param data Base64 编码的数据块
         */
        onblob?: (data: string) => void;

        /**
         * 当流结束时触发
         */
        onend?: () => void;
    }

    /**
     * 输出流
     * 表示向服务器发送数据的流
     */
    class OutputStream {
        /**
         * 创建一个新的输出流
         * @param client 关联的客户端
         * @param index 流索引
         */
        constructor(client: Client, index: number);

        /**
         * 发送数据块
         * @param data Base64 编码的数据
         */
        sendBlob(data: string): void;

        /**
         * 结束流
         */
        sendEnd(): void;
    }

    /**
     * 数组缓冲区读取器
     * 自动处理输入流并返回 ArrayBuffer 格式的数据包
     */
    class ArrayBufferReader {
        /**
         * 创建一个新的数组缓冲区读取器
         * @param stream 要读取数据的流
         */
        constructor(stream: InputStream);

        /**
         * 当收到数据包时触发
         * @param buffer 接收到的数据包
         */
        ondata?: (buffer: ArrayBuffer) => void;

        /**
         * 当流结束时触发
         */
        onend?: () => void;
    }

    /**
     * 显示模块
     * 处理和显示远程桌面的可视部分
     */
    class Display {
        /**
         * 创建一个新的显示实例
         */
        constructor();

        /**
         * 获取显示元素（DOM元素）
         * 该元素包含了远程桌面的图形界面
         * @returns 显示元素的DOM对象
         */
        getElement(): HTMLDivElement;

        /**
         * 获取当前显示元素的宽度
         * @returns 宽度（像素）
         */
        getWidth(): number;

        /**
         * 获取当前显示元素的高度
         * @returns 高度（像素）
         */
        getHeight(): number;

        /**
         * 获取音频上下文
         * @returns 音频上下文
         */
        getAudioContext(): AudioContext;

        /**
         * 强制完成所有挂起的渲染操作
         * @param callback 渲染完成后的回调函数
         */
        flush(callback?: () => void): void;

        /**
         * 获取默认图层
         */
        getDefaultLayer(): Display.Layer;

        /**
         * 创建一个新图层
         * @param width 图层宽度
         * @param height 图层高度
         */
        createLayer(width: number, height: number): Display.Layer;

        /**
         * 创建一个新缓冲区
         * @param width 缓冲区宽度
         * @param height 缓冲区高度
         */
        createBuffer(width: number, height: number): Display.Layer;

        /**
         * 设置显示的缩放比例
         * @param scale 缩放比例
         */
        scale(scale: number): void;

        /**
         * 设置通道掩码
         * @param layer 目标图层
         * @param mask 通道掩码值
         */
        setChannelMask(layer: Display.Layer, mask: number): void;

        /**
         * 绘制图像
         * @param layer 目标图层
         * @param x X坐标
         * @param y Y坐标
         * @param url 图像URL
         */
        draw(layer: Display.Layer, x: number, y: number, url: string): void;

        /**
         * 绘制流内容
         * @param layer 目标图层
         * @param x X坐标
         * @param y Y坐标
         * @param stream 输入流
         * @param mimetype 数据MIME类型
         */
        drawStream(layer: Display.Layer, x: number, y: number, stream: InputStream, mimetype: string): void;

        /**
         * 移动图层
         * @param layer 要移动的图层
         * @param parent 父图层
         * @param x X坐标
         * @param y Y坐标
         * @param z Z索引
         */
        move(layer: Display.Layer, parent: Display.Layer, x: number, y: number, z: number): void;

        /**
         * 设置图层变换矩阵
         * @param layer 目标图层
         * @param a 变换矩阵的a值
         * @param b 变换矩阵的b值
         * @param c 变换矩阵的c值
         * @param d 变换矩阵的d值
         * @param e 变换矩阵的e值
         * @param f 变换矩阵的f值
         */
        setTransform(layer: Display.Layer, a: number, b: number, c: number, d: number, e: number, f: number): void;

        /**
         * 设置游标
         * @param hotspotX 热点X坐标
         * @param hotspotY 热点Y坐标
         * @param layer 游标图层
         * @param x X坐标
         * @param y Y坐标
         * @param width 宽度
         * @param height 高度
         */
        setCursor(hotspotX: number, hotspotY: number, layer: Display.Layer, x: number, y: number, width: number, height: number): void;

        /**
         * 移动游标
         * @param x X坐标
         * @param y Y坐标
         */
        moveCursor(x: number, y: number): void;

        /**
         * 显示或隐藏游标
         * @param visible 是否可见
         */
        showCursor(visible: boolean): void;

        /**
         * 绘制矩形
         * @param layer 目标图层
         * @param x X坐标
         * @param y Y坐标
         * @param width 宽度
         * @param height 高度
         */
        rect(layer: Display.Layer, x: number, y: number, width: number, height: number): void;

        /**
         * 清除图层
         * @param layer 目标图层
         */
        clear(layer: Display.Layer): void;

        /**
         * 关闭图层
         * @param layer 目标图层
         */
        close(layer: Display.Layer): void;

        /**
         * 设置图层可见度
         * @param layer 目标图层
         * @param alpha 透明度值（0-255）
         */
        shade(layer: Display.Layer, alpha: number): void;

        /**
         * 将图层推入堆栈
         * @param layer 目标图层
         */
        push(layer: Display.Layer): void;

        /**
         * 从堆栈中弹出图层
         * @param layer 目标图层
         */
        pop(layer: Display.Layer): void;

        /**
         * 复制图层内容
         * @param srcLayer 源图层
         * @param srcX 源X坐标
         * @param srcY 源Y坐标
         * @param srcWidth 源宽度
         * @param srcHeight 源高度
         * @param dstLayer 目标图层
         * @param dstX 目标X坐标
         * @param dstY 目标Y坐标
         */
        copy(srcLayer: Display.Layer, srcX: number, srcY: number, srcWidth: number, srcHeight: number,
             dstLayer: Display.Layer, dstX: number, dstY: number): void;

        /**
         * 设置裁剪区域
         * @param layer 目标图层
         */
        clip(layer: Display.Layer): void;

        /**
         * 开始一个新的路径
         * @param layer 目标图层
         * @param x X坐标
         * @param y Y坐标
         */
        moveTo(layer: Display.Layer, x: number, y: number): void;

        /**
         * 添加一条直线到路径
         * @param layer 目标图层
         * @param x 目标X坐标
         * @param y 目标Y坐标
         */
        lineTo(layer: Display.Layer, x: number, y: number): void;

        /**
         * 添加一条三次贝塞尔曲线到路径
         * @param layer 目标图层
         * @param cp1x 第一控制点X坐标
         * @param cp1y 第一控制点Y坐标
         * @param cp2x 第二控制点X坐标
         * @param cp2y 第二控制点Y坐标
         * @param x 终点X坐标
         * @param y 终点Y坐标
         */
        curveTo(layer: Display.Layer, cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void;

        /**
         * 使用纯色描边当前路径
         * @param layer 目标图层
         * @param cap 线帽样式
         * @param join 线接样式
         * @param thickness 线宽
         * @param r 红色分量（0-255）
         * @param g 绿色分量（0-255）
         * @param b 蓝色分量（0-255）
         * @param a 透明度（0-255）
         */
        strokeColor(layer: Display.Layer, cap: number, join: number, thickness: number, r: number, g: number, b: number, a: number): void;

        /**
         * 使用图层作为笔刷描边当前路径
         * @param dstLayer 目标图层
         * @param srcLayer 源图层（作为笔刷）
         */
        strokeLayer(dstLayer: Display.Layer, srcLayer: Display.Layer): void;

        /**
         * 使用图层作为填充图案填充当前路径
         * @param dstLayer 目标图层
         * @param srcLayer 源图层（作为填充图案）
         */
        fillLayer(dstLayer: Display.Layer, srcLayer: Display.Layer): void;

        /**
         * 重置图层状态
         * @param layer 目标图层
         */
        reset(layer: Display.Layer): void;

        /**
         * 调整图层大小
         * @param layer 目标图层
         * @param width 新宽度
         * @param height 新高度
         */
        resize(layer: Display.Layer, width: number, height: number): void;

        /**
         * 当显示元素调整大小时触发
         * @param width 新宽度
         * @param height 新高度
         */
        onresize?: (width: number, height: number) => void;
    }

    namespace Display {
        /**
         * 显示图层
         * 表示远程桌面的一个可视图层
         */
        interface Layer {
            /**
             * 图层宽度
             */
            width: number;

            /**
             * 图层高度
             */
            height: number;

            /**
             * 将图层内容导出为Canvas元素
             * @returns 包含图层内容的Canvas元素
             */
            toCanvas(): HTMLCanvasElement;

            /**
             * 获取图层元素
             * @returns 图层的DOM元素
             */
            getElement(): HTMLElement;
        }
    }

    /**
     * 鼠标输入处理器
     */
    class Mouse {
        /**
         * 创建一个新的鼠标输入处理器
         * @param element 接收鼠标事件的DOM元素
         */
        constructor(element: HTMLElement);

        /**
         * 当鼠标状态改变时触发
         * @param state 鼠标状态对象
         */
        onmousedown?: (state: Mouse.State) => void;
        onmouseup?: (state: Mouse.State) => void;
        onmousemove?: (state: Mouse.State) => void;

        /**
         * 设置鼠标位置的客户端
         * @param client Guacamole客户端实例
         */
        setClient(client: Client): void;
    }

    namespace Mouse {
        /**
         * 鼠标状态对象
         */
        interface State {
            /**
             * X坐标
             */
            x: number;

            /**
             * Y坐标
             */
            y: number;

            /**
             * 左键状态 (按下为true)
             */
            left: boolean;

            /**
             * 中键状态 (按下为true)
             */
            middle: boolean;

            /**
             * 右键状态 (按下为true)
             */
            right: boolean;

            /**
             * 上滚状态
             */
            up: boolean;

            /**
             * 下滚状态
             */
            down: boolean;
        }
    }

    /**
     * 键盘输入处理器
     */
    class Keyboard {
        /**
         * 创建一个新的键盘输入处理器
         * @param element 接收键盘事件的DOM元素
         */
        constructor(element: HTMLElement);

        /**
         * 当键被按下时触发
         * @param keysym 按键符号
         */
        onkeydown?: (keysym: number) => void;

        /**
         * 当键被释放时触发
         * @param keysym 按键符号
         */
        onkeyup?: (keysym: number) => void;

        /**
         * 设置键盘事件的客户端
         * @param client Guacamole客户端实例
         */
        setClient(client: Client): void;

        /**
         * 释放所有当前按下的键
         */
        reset(): void;

        /**
         * 按下指定键
         * @param keysym 按键符号
         */
        press(keysym: number): void;

        /**
         * 释放指定键
         * @param keysym 按键符号
         */
        release(keysym: number): void;
    }

    /**
     * 视频播放器接口
     */
    interface VideoPlayer {
        /**
         * 播放视频
         */
        play(): void;

        /**
         * 暂停视频
         */
        pause(): void;

        /**
         * 同步视频播放
         * 通知播放器当前时间点前的所有视频已经通过底层流提供
         */
        sync(): void;
    }

    /**
     * VideoPlayer类的静态方法集合
     */
    namespace VideoPlayer {
        /**
         * 获取视频播放器实例
         * @param stream 视频数据流
         * @param layer 显示视频的图层
         * @param mimetype 视频的MIME类型
         * @returns 视频播放器实例，如果不支持指定的MIME类型则返回null
         */
        function getInstance(stream: InputStream, layer: Display.Layer, mimetype: string): VideoPlayer | null;
    }

    /**
     * 音频播放器接口
     */
    interface AudioPlayer {
        /**
         * 播放音频
         */
        play(): void;

        /**
         * 暂停音频
         */
        pause(): void;
    }

    /**
     * AudioPlayer类的静态方法集合
     */
    namespace AudioPlayer {
        /**
         * 获取音频播放器实例
         * @param stream 音频数据流
         * @param mimetype 音频的MIME类型
         * @returns 音频播放器实例，如果不支持指定的MIME类型则返回null
         */
        function getInstance(stream: InputStream, mimetype: string): AudioPlayer | null;
    }

    /**
     * 指令流解析器
     */
    class Parser {
        /**
         * 接收并解析数据
         * @param packet 数据包
         */
        receive(packet: string): void;

        /**
         * 当收到完整指令时触发
         * @param opcode 操作码
         * @param parameters 参数数组
         */
        oninstruction?: (opcode: string, parameters: string[]) => void;
    }

    /**
     * Guacamole对象
     * 用于处理任意数量的命名输入和输出流
     */
    class Object {
        /**
         * 创建一个新的Guacamole对象
         * @param client 拥有此对象的客户端
         * @param index 对象索引
         */
        constructor(client: Client, index: number);

        /**
         * 此对象的索引
         */
        readonly index: number;

        /**
         * 当对象收到定义指令时触发
         * @param name 对象的名称
         */
        onbody?: (mimetype: string, name: string) => void;

        /**
         * 请求该对象的特定类型的流
         * @param mimetype 请求的MIME类型
         * @param name 流的名称
         * @param callback 当流可用时的回调函数
         */
        requestInputStream(mimetype: string, name: string, callback: (stream: InputStream) => void): void;

        /**
         * 创建到此对象的输出流
         * @param mimetype 流的MIME类型
         * @param name 流的名称
         * @returns 新创建的输出流
         */
        createOutputStream(mimetype: string, name: string): OutputStream;
    }

    /**
     * 触摸屏输入处理器
     * 处理标准触摸事件并生成Guacamole鼠标事件
     */
    class TouchScreen {
        /**
         * 创建一个新的触摸屏处理器
         * @param element 接收触摸事件的DOM元素
         */
        constructor(element: HTMLElement);

        /**
         * 当触摸状态改变时触发
         * @param state 鼠标状态对象
         */
        onmousedown?: (state: Mouse.State) => void;
        onmouseup?: (state: Mouse.State) => void;
        onmousemove?: (state: Mouse.State) => void;

        /**
         * 设置触摸屏输入的客户端
         * @param client Guacamole客户端实例
         */
        setClient(client: Client): void;
    }

    /**
     * 触控板输入处理器
     * 允许通过拖动操作移动鼠标指针
     */
    class Touchpad {
        /**
         * 创建一个新的触控板处理器
         * @param element 接收触摸事件的DOM元素
         */
        constructor(element: HTMLElement);

        /**
         * 当触控板状态改变时触发
         * @param state 鼠标状态对象
         */
        onmousedown?: (state: Mouse.State) => void;
        onmouseup?: (state: Mouse.State) => void;
        onmousemove?: (state: Mouse.State) => void;

        /**
         * 设置触控板输入的客户端
         * @param client Guacamole客户端实例
         */
        setClient(client: Client): void;
    }

    /**
     * 字符串读取器
     * 从Guacamole.InputStream读取UTF-8编码的文本
     */
    class StringReader {
        /**
         * 创建一个新的字符串读取器
         * @param stream 要读取的输入流
         */
        constructor(stream: InputStream);

        /**
         * 当收到文本时触发
         * @param text 接收到的文本
         */
        ontext?: (text: string) => void;

        /**
         * 当流结束时触发
         */
        onend?: () => void;
    }

    /**
     * 字符串写入器
     * 将文本写入Guacamole.OutputStream
     */
    class StringWriter {
        /**
         * 创建一个新的字符串写入器
         * @param stream 要写入的输出流
         */
        constructor(stream: OutputStream);

        /**
         * 发送文本
         * @param text 要发送的文本
         */
        sendText(text: string): void;

        /**
         * 结束流
         */
        sendEnd(): void;
    }
}

/**
 * 扩展 Window 接口以包含 Guacamole 对象
 */
interface Window {
    Guacamole: typeof Guacamole;
}

/**
 * Guacamole使用示例:
 *
 * ```typescript
 * // 创建WebSocket隧道
 * const tunnel = new Guacamole.WebSocketTunnel("wss://example.com/websocket-tunnel");
 *
 * // 创建Guacamole客户端
 * const client = new Guacamole.Client(tunnel);
 *
 * // 获取显示元素
 * const display = client.getDisplay();
 * document.body.appendChild(display.getElement());
 *
 * // 创建鼠标和键盘监听
 * const mouse = new Guacamole.Mouse(display.getElement());
 * const keyboard = new Guacamole.Keyboard(document);
 *
 * // 连接鼠标到客户端
 * mouse.onmousedown = mouse.onmouseup = mouse.onmousemove = function(mouseState) {
 *   client.sendMouseState(mouseState);
 * };
 *
 * // 连接键盘到客户端
 * keyboard.onkeydown = function(keysym) {
 *   client.sendKeyEvent(1, keysym);
 * };
 *
 * keyboard.onkeyup = function(keysym) {
 *   client.sendKeyEvent(0, keysym);
 * };
 *
 * // 连接错误处理
 * client.onerror = function(error) {
 *   console.error("Guacamole连接错误:", error);
 * };
 *
 * // 连接状态改变处理
 * client.onstatechange = function(state) {
 *   if (state === client.STATE_CONNECTED) {
 *     console.log("连接成功");
 *   }
 * };
 *
 * // 连接到Guacamole服务器
 * client.connect();
 *
 * // 断开连接
 * function disconnect() {
 *   client.disconnect();
 * }
 * ```
 */ 