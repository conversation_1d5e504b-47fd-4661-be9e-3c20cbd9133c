export function salesConvert(sales, decimals = 1, errDefaultReturn = '-') {
    if (sales === 0)
        return sales

    if (!sales || !Number(sales))
        return errDefaultReturn

    const moneyUnits = ['', 'w']
    // 转换数字
    let curentIndex = 0
    // 转换单位
    for (let i = 0; i < 2; i++) {
        curentIndex = i
        const stringNum = sales.toString()
        const index = stringNum.indexOf('.')
        let newNum = stringNum
        if (index !== -1)
            newNum = stringNum.substring(0, index)

        if (newNum.length < 5 || i === 1)
            break

        sales = sales / 10000
    }
    if (curentIndex !== 0)
        sales = sales.toFixed(decimals)

    return sales.toString().replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,') + moneyUnits[curentIndex]
}

export function gmvConvert(gmv, decimals = 1, errDefaultReturn = '-', unit = '$') {
    if (gmv === 0)
        return gmv

    if (!gmv || !Number(gmv))
        return errDefaultReturn

    const moneyUnits = ['', 'w']
    // 转换数字
    let curentIndex = 0
    gmv = gmv / 100
    // 转换单位
    for (let i = 0; i < 2; i++) {
        curentIndex = i
        const stringNum = gmv.toString()
        const index = stringNum.indexOf('.')
        let newNum = stringNum
        if (index !== -1)
            newNum = stringNum.substring(0, index)

        if (newNum.length < 5 || i === 1)
            break

        gmv = gmv / 10000
    }
    gmv = gmv.toFixed(decimals)
    return unit + gmv.toString().replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,') + moneyUnits[curentIndex]
}

export function priceConvert(price, decimals = 2, errDefaultReturn = '-', unit = '$') {
    if (price === 0)
        return price

    if (!price || !Number(price))
        return errDefaultReturn

    const moneyUnits = ['', 'w']
    // 转换数字
    let curentIndex = 0
    price = price / 100
    // 转换单位
    for (let i = 0; i < 2; i++) {
        curentIndex = i
        const stringNum = price.toString()
        const index = stringNum.indexOf('.')
        let newNum = stringNum
        if (index !== -1)
            newNum = stringNum.substring(0, index)

        if (newNum.length < 5 || i === 1)
            break

        price = price / 10000
    }
    price = price.toFixed(decimals)
    return unit + price.toString().replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,') + moneyUnits[curentIndex]
}

export function rateConvert(rate, decimals = 2, errDefaultReturn = '-') {
    if (rate === 0)
        return rate

    if (!rate || !Number(rate))
        return errDefaultReturn

    rate = (rate * 100).toFixed(decimals)

    if (rate % 1 == 0)
        rate = Math.floor(rate)

    return rate
}
