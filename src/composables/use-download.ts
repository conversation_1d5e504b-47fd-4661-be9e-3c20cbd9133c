import {isFunction} from '@v-c/utils'
import type {VueNode} from 'vue'
import tDownloadModal from '~/components/t-download-modal/index.vue'



export function useDownload() {
    const confirmConstructor = createApp(tDownloadModal)
    let instance: any = null
    let mountDom: any = null
    if (!instance)
        mountDom = document.createElement('div')
    instance = confirmConstructor.mount(mountDom)
    if (!instance || !instance.$el)
        mountDom?.appendChild?.(instance.$el)
    const open = (params?: (any | undefined)) => {
        instance.showModal(params)
    }
    const close = () => {
        instance.closeModal()
    }
    return {
        open,
        close,
    }
}

