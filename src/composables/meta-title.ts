import type {RouteLocationNormalizedLoaded, RouteRecordRaw} from 'vue-router'
import {i18n} from '~/locales'

export function useMetaTitle(route: RouteRecordRaw | RouteLocationNormalizedLoaded) {
    const {title, locale} = route.meta ?? {}
    let metaTitle = title
    if (title || locale) {
        if (locale)
            metaTitle = (i18n?.global as any).t?.(locale) ?? title

        useTitle(`${metaTitle}-云电脑`)
    }
}
