import {isFunction} from '@v-c/utils'
import type {VueNode} from 'vue'
import tConfirmModal from '~/components/t-confirm-modal/index.vue'

interface confirmModalType {
    title?: string
    width?: number | string
    tips?: string | (() => VueNode) | VueNode | HTMLElement
    content?: string | (() => VueNode) | VueNode | HTMLElement
    getContainer?: string | HTMLElement | (() => HTMLElement)
    onOk?: any
    okText?: string
    cancelText?: string
    isHideCancel?: boolean
    btnRtl?: boolean
}

export function useConfirm(config: confirmModalType) {
    const confirmConstructor = createApp(tConfirmModal, {
        ...config,
    })
    let instance: any = null
    let mountDom: any = null
    if (!instance)
        mountDom = isFunction(config?.getContainer) ? config?.getContainer() : document.createElement('div')
    instance = confirmConstructor.mount(mountDom)
    if (!instance || !instance.$el)
        mountDom?.appendChild?.(instance.$el)
    const open = (params?: (any | undefined)) => {
        instance.showModal(params)
    }
    const close = () => {
        instance.closeModal()
    }
    return {
        open,
        close,
    }
}
