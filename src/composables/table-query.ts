import type {PaginationProps} from 'ant-design-vue'
import type {TableRowSelection} from 'ant-design-vue/es/table/interface'
import {assign} from 'lodash'

/**
 * 表格分页扩展类型
 */
export interface TablePaginationProps extends PaginationProps {
    /**
     * 排序字段
     */
    sortby?: string
    /**
     * 排序方式
     */
    sortType?: string

    frontTotal?: any
}

/**
 * 表格选择框扩展类型
 */
export interface TableRowSelectionsProps extends TableRowSelection {
    /**
     * 选择行
     */
    selectedRows: any[]
    /**
     * 选择行key
     */
    selectedRowKeys: any[]
}

interface TableQueryResult<D = any> {
    records: D[]
    total: number

    [propName: string]: any
}

/**
 * 表格查询配置
 */
export interface TableQueryOptions<D = any> {
    /**
     *查询接口
     */
    queryApi: (params?: any) => Promise<any>
    /**
     * 是否加载中
     */
    loading: boolean

    // 权限判断 0为正常 1. 没会员时的状态 2.有会员但没绑定类目时 3.有会员但非全类目，已全部绑定完类目
    bizCode: number

    // 货币单位符号
    currSymbol: string
    // 货币单位符号中文简称
    currSymbolCn: string
    /**
     * 数据源
     */
    dataSource: D[]
    /**
     * 查询参数
     */
    queryParams: Record<string, any>

    /**
     * 表格排序缓存
     */
    sortedInfo: Record<string, any>

    sortOrderEnum: Record<string, any>
    /**
     * 选择配置
     */
    rowSelections: TableRowSelectionsProps
    /**
     * 挂载时是否进行查询
     */
    queryOnMounted: boolean
    /**
     *  分页配置
     */
    pagination: TablePaginationProps
    /**
     * 是否展开
     */
    expand: boolean
    /**
     * 展开变化
     */
    expandChange: () => void

    /**
     * 列数距离
     */
    columns?: any[]

    columnsShowKeys?: any[]

    columnsDataArr?: () => any[] | Promise<[]>

    diyColumns?: any []

    // 表格变化
    onChange: (pagination: PaginationProps, filters: any, sorter: any) => void | Promise<void>
    /**
     * 查询前回调
     */
    beforeQuery: () => void | Promise<void>
    /**
     * 查询后回调
     */
    afterQuery: <R extends TableQueryResult<D> = any>(data: R) => R | Promise<R>
}

/**
 * 表格查询方法
 */
export function useTableQuery(_options: Partial<TableQueryOptions>) {
    const state = reactive<TableQueryOptions>(assign({
        queryApi: () => Promise.resolve(),
        loading: false,
        bizCode: 0,
        currSymbol: '$',
        currSymbolCn: '美元',
        queryParams: {},
        dataSource: [],
        rowSelections: {
            selectedRowKeys: [],
            selectedRows: [],
            onChange(selectedRowKeys: any[], selectedRows: any[]) {
                state.rowSelections.selectedRowKeys = selectedRowKeys
                state.rowSelections.selectedRows = selectedRows
            },
        },
        queryOnMounted: true,
        expand: false,
        sortOrderEnum: {
            'ascend': 'asc',
            'descend': 'desc',
        },
        sortedInfo: {},
        onChange(pagination: PaginationProps, filters: any, sorter: any) {
            state.pagination!.pageSize = pagination.pageSize
            state.pagination!.current = pagination.current
            state.sortedInfo = sorter
            state.queryParams.sortby = sorter.order ? sorter.field : undefined
            state.queryParams.sortType = state?.sortOrderEnum[sorter.order] || undefined
            query()
        },
        expandChange() {
            state.expand = !state.expand
        },
        beforeQuery() {
        },
        afterQuery(data: TablePaginationProps) {
            return data
        },
        // 列
        columnsShowKeys: [],
        columnsDataArr: () => {
            return []
        },
        diyColumns: [],
        columns: [],
    }, _options, {
        pagination: assign({
            current: 1,
            pageSize: 10,
            pageSizeOptions: ['10', '20', '50', '100'],
            total: 0,
            showSizeChanger: true,
            showQuickJumper: false,
            hideOnSinglePage: true,
            // showTotal: total => `总数据位：${total}`,
        } as TablePaginationProps, _options.pagination),
    }))

    const getColumns = () => {
        let columns: any = []
        const sorted = state?.sortedInfo || {}
        try {
            columns = Array.isArray(state.columnsDataArr) ? state.columnsDataArr : state.columnsDataArr()
        } catch (e) {
        }
        const arr = columns?.filter((item) => {
            let status = !item.diy // 不是diy，默认展示
            if (Array.isArray(state.columnsShowKeys) && state.columnsShowKeys.includes(item.key))
                status = true

            return status
        }).map(item => ({
            ...item,
            sortOrder: sorted?.columnKey === item.key && sorted?.order,
        }))
        return arr
    }

    const getColumnsData = () => {
        let arr: any = []
        try {
            arr = Array.isArray(state.columnsDataArr) ? state.columnsDataArr : state.columnsDataArr()
        } catch (e) {
        }
        return arr
    }

    const updateColumns = () => {
        const columnsArr = getColumnsData()
        // 返回的自定义列数组
        state.diyColumns = columnsArr.filter(item => (item.diy)).sort((a, b) => {
            const aIndex = a.diySort || 0
            const bIndex = b.diySort || 0
            return (bIndex - aIndex)
        }) || []
        state.columns = getColumns()
    }

    const setColumnsShowKeys = (val) => {
        state.columnsShowKeys = val
        updateColumns()
    }

    // 查询方法
    async function query() {
        if (state.loading)
            return
        state.loading = true

        try {
            await state.beforeQuery()
            updateColumns()
            const data = await state.queryApi({
                pageNum: state.pagination.current,
                pageSize: state.pagination.pageSize,
                ...state.queryParams,
            })
            if (data) {
                state.bizCode = data.bizCode || 0
                state.currSymbol = data?.currSymbol?.symbol || '$'
                state.currSymbolCn = data?.currSymbol?.cn || '美元'
                const _data = await state.afterQuery(data)
                state.dataSource = _data.records ?? []
                state.pagination.total = _data.total ?? 0
                const frontTotal = data?.page?.frontTotal || _data.total
                state.pagination.frontTotal = frontTotal ? (frontTotal >= 1000 ? '1000+' : `${frontTotal}`) : state.dataSource.length
            }
        } catch (e) {
            throw new Error(`服务繁忙: ${e}`)
        } finally {
            state.loading = false
        }
    }

    // 重置方法
    function resetQuery() {
        state.pagination.current = 1
        state.queryParams = {}
        state.sortedInfo = {}
        query()
    }

    // 初始化查询
    function initQuery() {
        state.pagination.current = 1
        query()
    }

    onMounted(() => {
        if (!state.queryOnMounted) {
            updateColumns()
            return
        }
        query()
    })

    return {
        query,
        resetQuery,
        initQuery,
        setColumnsShowKeys,
        updateColumns,
        state,
    }
}
