
export const winUrl = "https://qinghu-1304879925.cos.ap-shanghai.myqcloud.com/package/mini/%E9%9D%92%E8%99%8E%E4%BA%91%E7%94%B5%E8%84%91%E5%AE%89%E8%A3%85QH.exe"

export const macUrl = "https://qinghu-1304879925.cos.ap-shanghai.myqcloud.com/package/mac/%E9%9D%92%E8%99%8E%E4%BA%91%E7%94%B5%E8%84%91.dmg"

const downloadMap: Record<string, string> = {
  "Windows": winUrl,
  "MacOS-Apple": macUrl,
  "IOS": "",
  "Android":"",
}

export function useDownloadEvent(platform: string) {
  const url = downloadMap[platform]
  if (!url) {
    // 可根据需要弹窗提示
    return
  }
  const a = document.createElement('a')
  a.href = url
  a.download = ''
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}
