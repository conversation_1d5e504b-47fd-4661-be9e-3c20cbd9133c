import TrendDownIcon from '@/Icons/trend-down.svg?src'
import TrendUpIcon from '@/Icons/trend-up.svg?src'

export function renderRadioCell(radioNum: number) {
    const radio = rateConvert(radioNum)
    return ((!!radio || radio == 0)
        ? h('section', {
            style: {
                color: radio >= 0 ? 'var(--primary-color)' : 'var(--cloud-color-success)',
            }
        }, [(radio >= 0
            ? h('img', {
                src: TrendUpIcon,
            })
            : h('img', {
                src: TrendDownIcon,
            })), h('span', {className: 'ml-4px'}, `${Math.abs(radio)}%`)])
        : '')
}

export function renderPriceCell(text: string, radio: number) {
    return (h('div', [h('span', text), (radio != undefined || radio == 0) ? renderRadioCell(radio) : '']))
}
