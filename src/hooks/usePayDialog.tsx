import {message, Modal} from 'ant-design-vue';
import QrCode from '~/components/qrcode/index.vue'
import walletIcon from '@/assets/images/product/wallet.webp'
import zfbIcon from '@/assets/images/product/zfb.webp'
import wxIcon from '@/assets/images/product/wx.webp'
import {checkOrder, payResult, PayType} from "~/api/common/product.ts";
import {handleOperateElectron, formatAmount} from "~/utils/common-utils.ts";

export const payTypeList = [
  {id: PayType.balance, icon: walletIcon, name: '余额'},
  {id: PayType.zfb, icon: zfbIcon, name: '支付宝'},
  {id: PayType.wx, icon: wxIcon, name: '微信'},
]

/**
 * 支付弹窗hook
 */
export const usePayDialog = () => {
  const isTimeout = ref(false); // 是否超时
  const router = useRouter();
  let modal: any = null; // 支付弹窗实例
  const checkPayStatus = async (orderNo: string,abortController: AbortController, maxAttempts = 50, interval = 3000) => {
    let attempts = 0;

    const check = async () => {
      if (abortController.signal.aborted) {
        return false;
      }
      console.log('轮询支付状态...',maxAttempts);
      if (attempts >= maxAttempts) { // 3分钟 = 180秒 = 60次 * 3秒
        isTimeout.value = true;
        setTimeout(() => {
          modal.destroy();
          isTimeout.value = false;  // 3秒后重置超时状态
        }, 3000);
        abortController.abort();
        message.error('支付超时，请在订单管理中查看支付状态');
        return false;
      }

      try {
        const res = await checkOrder(orderNo);
        if (res.success && res.payload) {
          return true;
        }
        attempts++;
        await new Promise(resolve => setTimeout(resolve, interval));
        return check();
      } catch (error: any) {
        if (error.name === 'AbortError') {
          return false;
        }
        console.error('检查支付状态失败:', error);
        return false;
      }
    };

    return check();
  };

  const showPayModal = (payData: payResult, type: string, cb?: Function) => {
    const current = payTypeList.find(item => item.id === type);
    const abortController = new AbortController();
    modal = Modal.confirm({
      title: '扫码付款',
      icon: null,
      content: () => (
        <div class="flex flex-col justify-center items-center h-70 gap-4 select-none">
          <span class="text-#F53F3F text-4">￥{formatAmount(payData.totalPrice)}</span>
          <div class="bg-#f1f1f1 w-42 h-42 p-2 flex-center rounded-3 position-relative">
            <div>
              {
                type === PayType.zfb ? <iframe class="w-140px h-145px" srcdoc={payData.payQrcode} frameborder="0"/> :
                  <QrCode value={payData.payQrcode} imageSettings={{
                    src: current.icon,
                    width: 25, height: 25, excavate: true,
                  }}/>
              }
            </div>
             {isTimeout.value && <div class="w-full h-full position-absolute color-[#fff] flex items-center justify-center" style={{ background: 'rgba(0, 0, 0, 0.7)' }}>支付超时，请重新支付</div>}
          </div>

          <span>请使用{current.name}App扫码支付</span>
        </div>
      ),
      keyboard: false,
      maskClosable: false,
      footer: null,
      closable: true,
      width: 300,
      class: 'pay-modal',
      onCancel() {
        abortController.abort(); // 取消轮询
        message.warn('取消支付，请在费用管理/订单管理中重新支付').then();
        router.push('/cost/order'); // 添加这行代码跳转到订单管理页面
        //关闭子窗口
        handleOperateElectron('windows:closeChildWindow')
      },
      afterClose() {
        abortController.abort(); // 确保在任何情况下关闭弹窗都会取消轮询
      }
    });
    checkPayStatus(payData.orderNo,abortController).then(paySuccess => {
      if (paySuccess) {
        modal.destroy();
        message.success('支付成功').then(() => {
          cb && cb()
        });
        //关闭子窗口
        handleOperateElectron('windows:closeChildWindow')
      }
    });
  };

  return {
    payTypeList,
    showPayModal
  }
}
