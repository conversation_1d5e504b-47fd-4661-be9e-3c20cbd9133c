import {Modal} from 'ant-design-vue';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {h} from 'vue';
import {useRouter} from 'vue-router';
import {checkDueInstanceApi} from '~/api/common/cloud-desktop'

/**
 * 实名认证弹窗hook
 */
export const useCheckDueInstance = async () => {
    const router = useRouter();
    const today = new Date().toDateString();
    const lastCheck = localStorage.getItem('lastDueCheck');
    if (lastCheck === today) return;
    const res = await checkDueInstanceApi()
    if (res.code == 200 && res.payload?.length) {
        Modal.confirm({
            title: '续费提示',
            icon: h(ExclamationCircleOutlined),
            content: [
                h('div', { style: 'color:333333' }, '存在部分云桌面7天内到期，请及时续费。'),
                h('div', { style: 'color: #999999;' }, '若到期7天后仍未续费，云桌面将被系统自动释放，数据不可找回，请及时续费')
            ],
            cancelText: '取消',
            okText: '立即续费',
            keyboard: false,
            maskClosable: false,
            onOk() {
             router.push('/cloudDesktop?status=running');
            },
            onCancel() {
            
            },
        });
        localStorage.setItem('lastDueCheck', today);
    }
};



