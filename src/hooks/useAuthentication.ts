import {Modal} from 'ant-design-vue';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {h} from 'vue';
import {useRouter} from 'vue-router';
import {useRechargeDialog} from "~/hooks/useRechargeDialog.tsx";

/**
 * 实名认证弹窗hook
 */
export const useAuthentication = () => {
  const router = useRouter();

  const showAuthenticationModal = () => {
    return Modal.confirm({
      title: '实名认证提醒',
      icon: h(ExclamationCircleOutlined),
      content: '建议完成身份认证，否则购买的设备无法开机，是否立即认证？',
      cancelText: '取消',
      okText: '认证',
      keyboard: false,
      maskClosable: false,
      onOk() {
        router.push('/dashboard/auth');
      },
      onCancel() {
        router.push('/cloudDesktop');
      },
    });
  };

  return {
    showAuthenticationModal
  };
};

export const useBalanceEmptyModal = () => {
  const {rechargeModal} = useRechargeDialog()

  const showBalanceEmptyModal = () => {
    return Modal.confirm({
      title: '余额不足提醒',
      icon: h(ExclamationCircleOutlined),
      content: '订单支付失败，余额不足，是否立即充值？',
      cancelText: '取消',
      okText: '确定',
      keyboard: false,
      maskClosable: false,
      onOk() {
        rechargeModal()
      },
      onCancel() {

      },
    });
  };

  return {
    showBalanceEmptyModal
  };
};


