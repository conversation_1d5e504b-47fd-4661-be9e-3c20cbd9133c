import {Modal} from "ant-design-vue";
import {formatAmount} from "~/utils/common-utils.ts";
import CouponsDialog from '@/components/coupons-dialog/index.vue'
// import chooseCouponIcon from '@/assets/images/product/choose_icon.webp'
import {payTypeList} from "./usePayDialog.tsx";
import {
  createOrder,
  createPayOrder,
  getProductPriceDetail,
  payResult,
  PayType,
  ProductPriceDetail
} from "~/api/common/product.ts";
import {usePayDialog} from "./usePayDialog.tsx";
import {UserCoupon} from "~/api/common/user.ts";


export const useRechargeDialog = (options: { showClose?: boolean, mask?: boolean } = {
  showClose: true,
  mask: true
}) => {

  const {showPayModal} = usePayDialog()
  const userStore = useUserStore()
  const {userInfo} = storeToRefs(userStore);

  const rechargeModal = (cb?: Function) => {
    const amountList = [100, 200, 500, 1000]
    const activeAmount = ref()

    const payType = ref(PayType.zfb)

    // 优惠信息
    const coupon: UserCoupon = ref({})

    const chooseCouponRef = ref(null)
    // const chooseCoupon = () => {
    //   chooseCouponRef.value?.open()
    // }

    const priceInfo = ref<ProductPriceDetail>({
      productId: 0,
      actualPrice: 0,
      couponPrice: 0,
      totalPrice: 0
    })

    const changeAmount = async (item: number) => {
      activeAmount.value = item
      priceInfo.value = await getProductPriceDetail({
        productCount: activeAmount.value,
        productType: 'BALANCE',
        couponId: coupon.value.id,
      })
    }

    const createRechargeOrder = async () => {
      const productId = priceInfo.value.productId

      const {success, payload} = await createOrder({
        payType: payType.value,
        productId,
        productCount: activeAmount.value
      })
      if (success) {
        const data = await createPayOrder<payResult>(payload)
        if (data.success) {
          showPayModal(data.payload, payType.value, () => {
            modal.destroy()
            cb && cb()
          })
        }
      }
    }

    changeAmount(amountList[0]).then()

    const modal = Modal.confirm({
      title: '充值',
      icon: null,
      content: () => (
        <div class=" bg-#f1fbfa flex flex-col gap-5  select-none">

          <div class=" flex flex-col gap-3 bg-#FFF rounded-3 shadow p-5">

            <div class="flex gap-10">
              <div class="flex items-center gap-1">
                <div class="w-1 h-3 bg-#08C18A rounded-447px"></div>
                <div class="text-#3D3D3D text-14px">当前余额</div>
              </div>
              <div class="text-#08C18A text-14px">￥{formatAmount(userInfo.value?.balance)}</div>
            </div>

            <div class="flex gap-10">
              <div class="flex items-center gap-1">
                <div class="w-1 h-3 bg-#08C18A rounded-447px"></div>
                <div class="text-#3D3D3D text-14px">充值金额</div>
              </div>
              <div class="flex gap-3">
                {amountList.map(item => (
                  <div key={item} onClick={() => changeAmount(item)}
                       class={[
                         'w-30 h-11 rounded-1 flex-center cursor-pointer select-none border-1px border-solid',
                         activeAmount.value === item ? 'border-#08C18A' : 'border-#D9D9D9'
                       ]}>
                    ￥{item}
                  </div>
                ))}
              </div>
            </div>

          </div>

          {/*<div class=" flex flex-col gap-3 bg-#FFF rounded-3 shadow p-5">*/}
          {/*  <div class="flex gap-10">*/}
          {/*    <div class="flex items-center gap-1">*/}
          {/*      <div class="w-1 h-3 bg-#08C18A rounded-447px"></div>*/}
          {/*      <div class="text-#3D3D3D text-14px">优惠券</div>*/}
          {/*    </div>*/}

          {/*    <div class="flex items-center gap-1">*/}
          {/*      {*/}
          {/*        coupon.id ? (<div>*/}
          {/*            <span>已选</span>*/}
          {/*            <span class="text-#08C18A">{coupon.name}</span>*/}
          {/*          </div>*/}
          {/*        ) : <span>选择优惠券</span>*/}
          {/*      }*/}
          {/*      <img src={chooseCouponIcon} class="w-3 h-3 ml-1 cursor-pointer" onClick={chooseCoupon} alt=""/>*/}
          {/*    </div>*/}
          {/*  </div>*/}
          {/*</div>*/}

          <div class=" flex flex-col gap-3 bg-#FFF rounded-3 shadow p-5">
            <div class="flex gap-10">
              <div class="flex items-center gap-1">
                <div class="w-1 h-3 bg-#08C18A rounded-447px"></div>
                <div class="text-#3D3D3D text-14px">支付方式</div>
              </div>

              <div class="flex items-center gap-5">
                {
                  payTypeList.filter(item => item.id !== 'balance').map(item => (
                    <div key={item.id} onClick={() => payType.value = item.id}
                         class={['p-x-5 w-130px h-48px rounded-1.5 flex-center border border-solid  cursor-pointer select-none gap-2',
                           payType.value === item.id ? 'bg-#F1F6FF border-[#08C18A] text-[#08C18A]' : 'border-[#E0E0E0] bg-#F7F8F9'
                         ]}>
                      <img src={item.icon} class="w-4 h-4" alt=""/>
                      <span>{item.name}</span>
                    </div>
                  ))
                }
              </div>

            </div>
          </div>

          <CouponsDialog ref={chooseCouponRef} onUseCoupon={coupon => coupon.value = coupon}/>
        </div>
      ),
      keyboard: false,
      maskClosable: false,
      footer: () => (
        <div class="w-full h-27 p-x-8 p-y-4 bg-#FFF shadow flex justify-between items-center select-none">

          <div class="flex flex-col gap-1">
            <div class="flex items-center gap-1">
              <span class="text-#3D3D3D fw-500 text-14px mt-4px">实付：</span>
              <div class="text-#08C18A">
                <span class="text-3.5">￥</span>
                <span class="text-6 fw-600">{formatAmount(priceInfo.value.actualPrice)}</span>
              </div>
            </div>
            {/* <div class="flex items-center gap-1">
              <span class="text-#3D3D3D fw-500 text-14px">到账：</span>
              <div class="bg-#f0f2f9 p-y-1 px-2 rounded-1 text-#595654">
                <span>余额</span>
                <span class="text-#08C18A text-4">￥{formatAmount(priceInfo.value.actualPrice)}</span>
              </div>
            </div> */}
          </div>

          <button
            class="w-130px h-46px text-white text-5 button-primary rounded-2 cursor-pointer select-none border-none outline-none"
            onClick={createRechargeOrder}>
            立即支付
          </button>

        </div>
      ),
      mask: options.mask,
      closable: options.showClose,
      width: 850,
      class: 'recharge-modal',
    })
  }

  return {
    rechargeModal
  }
}
