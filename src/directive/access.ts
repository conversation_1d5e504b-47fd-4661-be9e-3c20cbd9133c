import type {App, Directive} from 'vue'

export const accessDirective: Directive = (el, binding) => {
    const {hasAccess} = useAccess()
    if (!hasAccess(binding.value))
        el.parentNode?.removeChild(el)
}

export function setupAccessDirective(app: App) {
    app.directive('access', accessDirective)
}

const isDev: Directive = (el, binding) => {
    const message = useMessage()
    el.onclick = (e: Event) => {
        e.preventDefault()
        message.info('开发中，敬请期待')
    }
}

export function setDevMessage(app: App) {
    app.directive('devMessage', isDev)
}
