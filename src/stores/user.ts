import {getRouteMenusApi} from '@/api/common/menu'
import type {UserInfo} from '@/api/common/user'
import {getUserInfoApi} from '@/api/common/user'
import type {MenuData} from '~/layouts/basic-layout/typing'
import {rootRoute} from '~/router/constant'
import {generateFlatRoutes, generateRoutes, generateTreeRoutes} from '~/router/generate-route'
import { getContactMeStatusApi, type ContactMeInfo } from '@/api/common/user';
import { DYNAMIC_LOAD_WAY, DynamicLoadEnum } from '~/utils/constant'
import avatarUrl from '~/assets/images/dashboard/avatar.webp'

// 用户状态管理
export const useUserStore = defineStore('user', () => {
    const routerData = shallowRef()
    const menuData = shallowRef<MenuData>([])
    // 存储用户信息的key
    const userInfoKey = 'userInfo'
    // 用户信息持久化存储
    const storageUserInfo = useStorage(userInfoKey, {})
    // 内存中的用户信息（响应式）
    const userInfo = shallowRef<UserInfo>()
    // 授权令牌
    const token = useAuthorization()
    // 用户头像，如果没有则使用默认头像
    const avatar = computed(() => userInfo?.value?.avatar ?? avatarUrl)
    // 用户昵称
    const nickname = computed(() => userInfo?.value?.nickname)

    const userId = shallowRef()

    //  TODO 暂时没用到动态路由菜单
    const getMenuRoutes = async () => {
        const response = await getRouteMenusApi()
        return generateTreeRoutes(response.payload ?? [])
    }

    const generateDynamicRoutes = async () => {
        const dynamicLoadWay = DYNAMIC_LOAD_WAY === DynamicLoadEnum.BACKEND ? getMenuRoutes : generateRoutes
        const {menuData: treeMenuData, routeData} = await dynamicLoadWay()

        menuData.value = treeMenuData

        routerData.value = {
            ...rootRoute,
            children: generateFlatRoutes(routeData),
        }
        return routerData.value
    }

    /**
     * 获取用户详细信息
     */
    const getUserInfo = async () => {
        // 获取用户信息
        try {
            const response = await getUserInfoApi<UserInfo>()
            // 将用户详情信息合并到现有用户信息中
            userInfo.value = { ...userInfo.value, ...response.payload }

            userId.value = userInfo.value.userId;

            // 更新持久化存储
            updateStorageUserInfo();
            checkContactMe();
            return response.payload
        } catch (e) {
            // logout();
        }
    }

    /**
     * 获取客服二维码
     */
        // 检查用户是否已添加客服
    const checkContactMe = async () => {
            try {
                // 调用接口获取状态
                const response = await getContactMeStatusApi<ContactMeInfo>();

                // 处理接口返回数据
                if (response && response?.weChatQrUrl) {
                    // 将用户详情信息合并到现有用户信息中
                    userInfo.value = { ...userInfo.value, weChatQrUrl: response?.weChatQrUrl }
                    updateStorageUserInfo();
                }
            } catch (error) {
            }
    }

    /**
     * 保存登录用户信息
     */
    const saveLoginUserInfo = (loginUserInfo: any) => {
        if (!loginUserInfo) return

        const userInfoData = {
            nickname: loginUserInfo.nickname,
            userAccount: loginUserInfo.userAccount,
            inviteCode: loginUserInfo.inviteCode,
            country: loginUserInfo.country,
            isFirstRegister: loginUserInfo.isFirstRegister,
            isInvited: loginUserInfo.isInvited
        }

        // 更新内存中的用户信息
        userInfo.value = { ...userInfo.value, ...userInfoData }
        userId.value = userInfo.value.userId
        // 更新持久化存储
        updateStorageUserInfo()
    }

    /**
     * 更新存储的用户信息
     * 将内存中的用户信息同步到本地存储中，确保数据一致性
     */
    const updateStorageUserInfo = () => {
        if (!userInfo.value) return

        storageUserInfo.value = {
            ...userInfo.value,
            // 存储所有用户信息字段
            nickname: userInfo.value.nickname,           // 用户昵称，显示在界面上的用户名称
            userAccount: userInfo.value.userAccount,     // 账号名称，用户登录系统的唯一标识符
            inviteCode: userInfo.value.inviteCode,       // 邀请码，用于邀请新用户并获得奖励的唯一代码
            country: userInfo.value.country,             // 用户所属国家/地区，用于区域化设置和服务
            isFirstRegister: userInfo.value.isFirstRegister, // 是否首次注册，用于判断是否显示新手引导
            isInvited: userInfo.value.isInvited,         // 是否通过邀请注册，影响用户初始权益和奖励
            phone: userInfo.value.phone,                 // 手机号，用户的联系方式，也可作为登录凭证
            isRealName: userInfo.value.isRealName,       // 是否已实名认证，影响用户可使用的功能和服务范围
            balance: userInfo.value?.balance,             // 账户余额，用户在系统中的可用资金
            avatar: userInfo.value.avatar,
            userId: userInfo.value.userId               // 用户ID
        }
    }

    /**
     * 获取用户令牌策略信息
     */
    const getTokenPoliciesInfo = async () => {
        // 获取用户信息
        try {
            const response = await getUserInfoApi<UserInfo>()
            userInfo.value = {
                ...userInfo.value,
                nickname: response.payload?.nickname,
            }
            updateStorageUserInfo()
        } catch (e) {
            logout()
            console.log(e)
        }
    }

    /**
     * 设置访问令牌
     */
    const setAccessToken = async (tokenStr: string) => {
        token.value = tokenStr
    }

    /**
     * 退出登录
     */
    const logout = async () => {
        try {
            // await logoutApi()
            localStorage.setItem('logout-event', Date.now().toString())
        } finally {
            // 清空令牌
            token.value = null
            // 清空内存中的用户信息
            userInfo.value = undefined
            // 清空路由数据
            routerData.value = undefined
            // 清空菜单数据
            menuData.value = []
            // 清空持久化存储的用户信息
            storageUserInfo.value = {}
        }
    }

    return {
        userInfo,
        getUserInfo,
        logout,
        routerData,
        menuData,
        generateDynamicRoutes,
        avatar,
        nickname,
        getTokenPoliciesInfo,
        setAccessToken,
        saveLoginUserInfo,
        checkContactMe,
        userId
    }
})
