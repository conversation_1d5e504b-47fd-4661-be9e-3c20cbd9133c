


// 用户实例列表
export interface InstanceParams {
    pageNum?: number
    pageSize?: number
    remoteState?: String
    deviceState?: String
    expreState?: String
}
export interface InstanceResult {
}


// 用户开关机明细列表
export interface InstanceParams {
    id?: number
    pageNum?: number
    pageSize?: number
    terminalType?: String
    desktop_name?: String
}

// 获取用户实例列表
export function getUserInstanceListApi(params: InstanceParams) {
    const data = {
        ...params,
    }
    return useGet('/userInstance/list', data, {})
}

// 获取用户开关机明细列表
export function getRunRecordsListApi(params: InstanceParams) {
    const data = {
        ...params,
    }
    return useGet<InstanceResult>('/userInstance/runRecords', data, {})
}

// 获取用户登录日志
export function getLoginRecordsListApi(params: InstanceParams) {
    const data = {
        ...params,
    }
    return useGet<InstanceResult>('/userInstance/loginRecords', data, {})
}
