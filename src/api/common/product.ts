import {useGetNoBody} from "~/utils/request.ts";

export interface SkuItem {
  /**
   * 低至
   */
  asLow?: string;
  /**
   * 原价
   */
  asOrig?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 内存
   */
  memory?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 规格实例
   */
  sepcExample?: string;
  /**
   * 规格备注
   */
  sepcRemark?: string;
  /**
   * sku默认配置
   */
  skuDefJson?: JSONObject;
  /**
   * sku名称
   */
  skuName?: string;
  /**
   * 系统盘
   */
  systemDisk?: string;
  /**
   * vCpu
   */
  vCpu?: string;

  [property: string]: any;
}

export interface JSONObject {
  region?: string
  zoneId?: string
  zoneName?: string
  chargeType?: keyValueMap[]

  [property: string]: any;
}

export interface keyValueMap {
  name?: string
  value?: number
}

export interface OrderListParams {
  /**
   * 搜索关键字
   */
  keyword?: string;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 页码
   */
  pageNum?: number;
  /**
   * 每页条数
   */
  pageSize?: number;
  /**
   * 支付结束时间
   */
  payTimeEnd?: string;
  /**
   * 支付开始时间
   */
  payTimeStart?: string;
  payType?: PayType;
  productType?: ProductType;
  status?: OrderStatus;
  userId?: number;
  isBalance?: number

  [property: string]: any;
}

/**
 * 支付类型
 * wx_pub_qr@yzm :微信扫码支付
 * alipay_pc_direct@yzm :支付宝扫码支付
 * balance :余额支付
 */
export enum PayType {
  zfb = "alipay_pc_direct@yzm",
  balance = "balance",
  wx = "wx_pub_qr@yzm",
}

export const payTypeMap = {
  [PayType.zfb]: '支付宝',
  [PayType.balance]: '余额',
  [PayType.wx]: '微信',
}

/**
 * 产品类型
 * BUY_INSTANCE :购买实例
 * RENEW_INSTANCE :付费实例
 * BALANCE :余额
 */
export enum ProductType {
  balance = "BALANCE",
  buyInstance = "BUY_INSTANCE",
  renewInstance = "RENEW_INSTANCE",
  cloudDiskCapacity = "CLOUD_DISK_CAPACITY"
}

export const productTypeMap = {
  [ProductType.balance]: '余额',
  [ProductType.buyInstance]: '购买实例',
  [ProductType.renewInstance]: '付费实例',
  [ProductType.cloudDiskCapacity]: '扩容云盘',
}

/**
 * 订单状态
 * PENDING :待付款
 * PAID :已付款
 * COMPLETED :已完成
 * CANCEL :已取消
 * OVERDUE :已过期
 * REFUNDED :已退款
 */
export enum OrderStatus {
  CANCEL = "CANCEL",
  COMPLETED = "COMPLETED",
  OVERDUE = "OVERDUE",
  PAID = "PAID",
  PENDING = "PENDING",
  REFUNDED = "REFUNDED",
}

export const orderStatusMap = {
  [OrderStatus.PENDING]: '待付款',
  [OrderStatus.PAID]: '已付款',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCEL]: '已取消',
  [OrderStatus.OVERDUE]: '已过期',
  [OrderStatus.REFUNDED]: '已退款'
}

export interface MonthItem {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 删除标记 0:未删除 1:已删除
   */
  deleteFlag?: boolean;
  id?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 计费类型
   */
  type?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 数值
   */
  value?: number;
  /**
   * 版本号
   */
  version?: number;
  [property: string]: any;
}

export interface OrderItem {
  /**
   * 实际支付金额
   */
  actualPrice?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 订单id
   */
  id?: number;
  /**
   * 产品数量
   */
  number?: number;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 订单总价
   */
  orderPrice?: number;
  /**
   * 订单状态
   */
  orderStatus?: OrderStatus;
  /**
   * 支付时间
   */
  payTime?: string;
  /**
   * 支付方式
   */
  payType?: PayType;
  /**
   * 产品名称
   */
  productName?: string;
  /**
   * 产品类型
   */
  productType?: ProductType;

  [property: string]: any;
}

export interface ImageRes {
  /**
   * 镜像id
   */
  imageId?: string;
  /**
   * 镜像名称
   */
  imageName?: string;
  /**
   * 操作系统类型(linux、windows)
   */
  osType?: string;
  /**
   * 镜像操作系统的发行版本
   */
  platform?: string;
  /**
   * 备注
   */
  remark?: string;

  [property: string]: any;
}

export interface PriceParams {
  /**
   * 优惠券ID
   */
  couponId?: string;
  /**
   * 产品ID
   */
  itemId?: string;
  /**
   * 产品数量
   */
  duration?: number;
  /**
   * 购买数量
   */
  productCount: number;
  /**
   * [ProductType] 产品类型
   * BUY_INSTANCE :购买实例
   * RENEW_INSTANCE :付费实例
   * BALANCE :余额
   */
  productType: string;

  productItemIds?: string;

  [property: string]: any;
}

export interface ProductPriceDetail {
  /**
   * 实际金额
   */
  actualPrice?: number;
  /**
   * 优惠价格
   */
  couponPrice?: number;
  /**
   * 产品id
   */
  productId?: number;
  /**
   * 订单总金额
   */
  totalPrice?: number;

  [property: string]: any;
}

export interface createOrderParams {
  /**
   * 附加信息
   */
  additionalInfo?: AdditionalInfo[];
  /**
   * 优惠券ID
   */
  couponId?: number;
  /**
   * 购买数量
   */
  productCount: number;
  /**
   * 支付方式
   */
  payType: PayType;
  /**
   * 产品ID
   */
  productId: number;

  [property: string]: any;
}

export interface AdditionalInfo {
  key?: string;
  value?: string;

  [property: string]: any;
}

export interface payResult {
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 是否支付成功
   */
  paid?: boolean;
  /**
   * 支付二维码地址
   */
  payQrcode?: string;
  /**
   * 订单金额
   */
  totalPrice?: number;

  [property: string]: any;
}

export interface OrderDetail {
  /**
   * 云桌面名称
   */
  desktopName?: string;
  /**
   * 购买时长（天）
   */
  number?: string;
  /**
   * 设备区域
   */
  zoneName?: string;
  [property: string]: any;
}

//获取订单产品详情
export function getProductPriceDetail(params: PriceParams) {
  return useGetNoBody<ProductPriceDetail, PriceParams>('/product/getOrderProductDetail', params)
}

//订单列表
export function getOrderList(params: OrderListParams) {
  return useGet<Array<OrderItem>, OrderListParams>('/order/queryOrderPage', params)
}

//sku列表
export function getSkuList(params?: any) {
  return useGet<Array<SkuItem>, null>('/sku/list', params)
}

//sku列表
export function getMonthList(params?: any) {
  return useGet<Array<MonthItem>, null>('/sku/chargeTypeList', params)
}

//sku详情
export function getSkuItemById(itemId: string) {
  return useGet<SkuItem, { itemId: string }>('/sku/getSkuDataByItemId', {itemId})
}

//镜像详情
export function getImageById(itemId: string, params) {
  return useGet<ImageRes, { itemId: string }>(`/product/image/${itemId}`,params)
}

//创建订单
export function createOrder(params: createOrderParams) {
  return usePost<string, createOrderParams>('/order/createOrder', params)
}

//创建支付订单
export function createPayOrder<payResult>(orderNo: string) {
  return usePost<payResult, { orderNo: string }>('/order/createPayOrder', {orderNo})
}

//创建支付订单
export function checkOrder(orderNo: string) {
  return useGet<boolean, { orderNo: string }>('/order/checkOrder', {orderNo})
}

//创建支付订单
export function getOrderDetail<OrderDetail>(id: number) {
  return useGet<OrderDetail, { id: number }>('/order/getOrderDetails', {id})
}

// 获取sku关联的区域
export function getInstanceRegionListApi<OrderDetail>(id: number) {
  return useGet<OrderDetail, { id: number }>('/product/getInstanceRegionList', {id})
}
