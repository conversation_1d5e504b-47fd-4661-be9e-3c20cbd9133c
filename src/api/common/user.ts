export interface UserInfo {
    nickname?: string                // 用户昵称
    avatar?: string                  // 用户头像
    // 登录接口返回的用户信息
    userAccount?: string             // 账号名称
    inviteCode?: string              // 邀请码
    country?: string                 // 用户所属国家/地区
    isFirstRegister?: boolean        // 是否首次注册
    isInvited?: boolean              // 是否通过邀请注册
    // 用户详情接口返回的信息
    phone?: string                   // 手机号
    isRealName?: boolean             // 是否已实名认证
    balance?: number                 // 账户余额
    couponCount?: number                 // 优惠券数量
    userId?: number                  // 用户ID
}

export interface userDetails {
    /**
     * 余额
     */
    balance?: number;
    /**
     * 是否实名认证
     */
    isRealName?: boolean;
    /**
     * 昵称
     */
    nickname?: string;
    /**
     * 手机号
     */
    phone?: string;
    /**
     * 优惠券数量
     */
    couponCount?: string;
}

// 添加contactMe接口返回数据类型
export interface ContactMeInfo {
    /**
     * 企业微信二维码URL
     */
    weChatQrUrl: string;
    /**
     * 是否已添加客服
     */
    contactMe: boolean;
}

export function getUserInfoApi<userDetails>() {
    return useGet<userDetails>('/user/getUserDetails')
}

// 添加获取客服联系状态接口
export function getContactMeStatusApi<ContactMeInfo>() {
    return useGet<ContactMeInfo>('/auth/contactMe')
}

export interface UserCoupon {
    /**
     * 优惠券本身的标识符
     */
    couponId?: number;
    /**
     * 优惠券的描述信息，详细说明优惠券的使用条件等
     */
    description?: string;
    /**
     * 优惠券失效的结束日期
     */
    endDate?: string;
    /**
     * 优惠券记录的唯一标识符
     */
    id?: number;
    /**
     * 是否过期
     */
    isExpired?: boolean;
    /**
     * 优惠券的名称
     */
    name?: string;
    /**
     * 优惠券生效的开始日期
     */
    startDate?: string;
    /**
     * 优惠金额（分）
     */
    subtract?: number;
    /**
     * 优惠券的类型，用于区分不同种类的优惠券
     */
    type?: number;
    /**
     * 优惠券的状态，表示优惠券是否可用等 使用状态：1，未使用，2，已使用
     */
    useStatus?: number;
    [property: string]: any;
}

export function getUserCoupon(productId:number = undefined) {
    return useGet<Array<UserCoupon>>('/user/getUserCoupon',{productId})
}
