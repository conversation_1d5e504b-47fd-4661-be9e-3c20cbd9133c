import request, {ResponseBody} from '~/utils/request'
import type {
    CloudDesktopRecord,
    CloudDesktopDetail,
    CloudDesktopRunRecord,
    CloudDesktopEffectRecord,
    SessionRequestParams,
    ConnectionDetails,
    CloudDesktopFilterForm,
    InstanceRenew,
    CloudDesktoLabelItem
} from '~/types/cloud-desktop'

/**
 * 云电脑列表返回类型，包含分页信息
 */
interface PageInfo {
    pageNum: number;
    pageSize: number;
    total: number;
    pages: number;
    pageToken?: string;
}

/**
 * 获取用户实例列表
 * @param params 过滤参数
 * @returns 用户实例列表
 */
export function getUserInstanceList(params: CloudDesktopFilterForm = {}): Promise<ResponseBody<CloudDesktopRecord[]>> {
    return request({
        url: '/userInstance/list',
        method: 'GET',
        params
    })
}

/**
 * 获取用户实例运行记录
 * @param params 过滤参数
 * @returns 用户实例列表
 */
export function getUserInstanceRunRecords(params: CloudDesktopFilterForm = {}): Promise<ResponseBody<CloudDesktopRecord[]>> {
    return request({
        url: '/userInstance/runRecords',
        method: 'GET',
        params
    })
}

/**
 * 获取云电脑详情
 * @param id 实例ID
 * @returns 云电脑详情
 */
export function getUserInstanceDetail(id: number): Promise<ResponseBody<CloudDesktopDetail>> {
    return request({
        url: `/userInstance/details`,
        method: 'GET',
        params: {
            id
        }
    })
}

/**
 * 连接实例会话
 * @param data 会话请求参数
 * @returns 会话连接信息
 */
export function connectInstanceSession(data: SessionRequestParams): Promise<ResponseBody<ConnectionDetails>> {
    return request({
        url: '/userInstance/session/connect',
        method: 'POST',
        data
    })
}

/**
 * 结束会话
 * @param sessionId 会话ID
 * @returns 操作结果
 */
export function disconnectSession(sessionId: string): Promise<ResponseBody<string>> {
    return request({
        url: `/userInstance/session/${sessionId}/end`,
        method: 'POST'
    })
}

/**
 * 启动云电脑
 * @param workspaceId 工作区ID
 * @returns 操作结果
 */
export function startInstance(workspaceIds: number[], terminalType: string): Promise<ResponseBody<any>> {
    return request({
        url: '/userInstance/start',
        method: 'POST',
        data: {
            ids: workspaceIds,
            terminalType: terminalType
        }
    })
}

/**
 * 关闭云电脑
 * @param workspaceId 工作区ID
 * @returns 操作结果
 */
export function stopInstance(workspaceIds: number[], terminalType: string): Promise<ResponseBody<any>> {
    return request({
        url: '/userInstance/stop',
        method: 'POST',
        data: {
            ids: workspaceIds,
            terminalType: terminalType
        }
    })
}

/**
 * 更新云电脑备注信息
 * @param params 包含id、desktopName和remark参数
 * @returns 更新结果
 */
export function updateInstanceRemark(params: {
    id: number;
    desktopName?: string;
    remark?: string;
    labels?: CloudDesktoLabelItem[]; // 标签

}): Promise<ResponseBody<any>> {
    return request({
        url: '/userInstance/updateRemark',
        method: 'POST',
        data: params
    })
}

/**
 * 续费实例
 * @param logicIds 包含logicIds
 * @returns
 */
export function instanceRenew(logicIds: string[]): Promise<ResponseBody<Array<InstanceRenew>>> {
    return request({
        url: '/userInstance/getRenewUserInstanceInfo',
        method: 'Get',
        params: {logicIds}
    })
}


/**
 * 是否剩余7天到期实例
 * @param logicIds 包含logicIds
 * @returns
 */
export function checkDueInstanceApi(): Promise<ResponseBody<Array<InstanceRenew>>> {
    return request({
        url: '/userInstance/checkDueInstance',
        method: 'Get',
    })
}
