// 获取合伙人信息
export function getPartnerInfoApi(params) { 
    return useGet('/proxy/distribution/distribution-partner/infoPartner', params, {});
}

// 获取账户信息 (包含：公司、个人)
export function getUserAccountInfoApi(params) {
    return useGet('/proxy/distribution/distribution-account/getUserAccount', params, {});
}

// 创建账户
export function createUserAccountApi(params) {
    return usePost('/proxy/distribution/distribution-account/createUserAccount', params, {});
}

// 获取佣金详情列表
export function getCommissionDetailListApi(params) {
    return useGet('/proxy/distribution/distribution-orderdetails/orderDetailsList', params, {});
}

// 获取套餐管理列表
export function getSetMealManageListApi(params) {
    return useGet('/proxy/distribution/distribution-discount/discountList', params, {});
}

// 创建链接
export function createLinkApi(params) {
    return usePost('/proxy/distribution/distribution-url/discountUrlCreate', params, {});
}

// 获取链接列表
export function getLinkListApi(params) {
    return useGet('/proxy/distribution/distribution-url/discountUrlList', params, {});
}

// 修改链接名称
export function updateLinkNameApi(params) {
    return useGet('/proxy/distribution/distribution-url/updateUrlName', params, {});
}

// 获取拉新详情列表
export function getPullNewDetailListApi(params) {
    return useGet('/proxy/distribution/distribution-pullnew/pullNewList', params, {});
}

// 获取提现记录列表
export function getWithdrawRecordListApi(params) {
    return useGet('/proxy/distribution/distribution-withdraw/userWithdrawList', params, {});
}

// 创建提现记录
export function createWithdrawaRecordApi(params) {
    return usePost(`/proxy/distribution/distribution-withdraw/createWithdraw?appId=${params.appId}&tenantId=${params.tenantId}&userId=${params.userId}&userAccountId=${params.userAccountId}`, {}, {});
}

// 处理佣金提现
export function commissionWithdrawApi(params) {
    return usePost('/proxy/distribution/distribution-withdraw/process', params, {});
}

/**
 * 生成小程序分销海报
 */
export function createPromotionCode(params) {
    return useGet('/proxy/distribution-discount/createPromotionCode', params, {});
}

/**
 * 生成小程序分销海报
 */
export function uploadUrl(params) {
    return usePost('/proxy/distribution/public/upload', params, {});
}