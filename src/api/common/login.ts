import {CONNECTION_TYPE} from '@/api/common/auth.ts'
import {getCookie} from '~/utils/tools.ts'

export interface LoginParams {
    username: string
    password: string
    signInType: CONNECTION_TYPE.PASSWORD
    options?: {
        autoRegister?: boolean
        captchaCode?: string
        randstr?: string
        regCode?: string
    }
}

export interface LoginMobileParams {
    passCodePayload: {
        phone: string
        passCode: string
    }
    connection: CONNECTION_TYPE.PASSCODE
    options?: {
        autoRegister?: boolean
        captchaCode?: string
        randstr?: string
        regCode?: string
    }
}

// 新增接口参数类型
export interface LoginVerifyCodeParams {
    signInType: 'VERIFY_CODE' // 验证码登录
    username: string // 手机号
    verifyCode: string // 验证码
    urlCode?: string // 渠道号
    inviteCode?: string // 邀请码
}

export interface LoginResultModel {
    accessToken?: string         // 访问令牌
    nickname?: string            // 用户昵称
    expireTime?: number          // 令牌过期时间
    userAccount?: string         // 账号名称
    inviteCode?: string          // 邀请码
    country?: string             // 用户所属国家/地区
    isFirstRegister?: boolean    // 是否首次注册
    isInvited?: boolean          // 是否通过邀请注册
}

export enum VerifyEditType {
    Password = 'password',
    Phone = 'phone',
}

/**
 * 登录API
 * @param params 登录参数
 * @returns API响应
 */
export function loginApi(params: LoginVerifyCodeParams) {
    const data = { ...params };

    // 处理邀请码
    const regCode = getCookie('regCode');
    if (regCode && !data.inviteCode) {
        data.inviteCode = regCode;
    }

    return usePost<LoginResultModel, LoginVerifyCodeParams>('/auth/signIn', data, {
        // 设置为false的时候不会携带token
        token: false,
        // 是否开启全局请求loading
        loading: true,
    });
}

export function signUpApi(params: LoginParams | LoginMobileParams) {
    const data = {
        ...params,
    }
    return usePost<LoginResultModel, LoginParams | LoginMobileParams>('/auth/signup', data, {
        // 设置为false的时候不会携带token
        token: false,
        // 开发模式下使用自定义的接口
        // customDev: true,
        // 是否开启全局请求loading
        loading: true,
    })
}

export function verifyResetPassword(params: any) {
    return usePost<LoginResultModel, any>('/auth/reset-password-verify', params, {
        // 设置为false的时候不会携带token
        token: false,
        // 是否开启全局请求loading
        loading: true,
    })
}

export function doResetPassword(params: any) {
    return usePost<LoginResultModel, any>('/auth/reset-password', params, {
        // 设置为false的时候不会携带token
        token: false,
        // 是否开启全局请求loading
        loading: true,
    })
}


export function logoutApi() {
    return useGet('/logout')
}
