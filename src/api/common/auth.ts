import {ServerAddress} from '@/enums/http-enum'

export enum SMS_CHANNEL {
    CHANNEL_LOGIN = 'CHANNEL_LOGIN', // 用户登录
    CHANNEL_REGISTER = 'CHANNEL_REGISTER', // 用户注册
    CHANNEL_RESET_PASSWORD = 'CHANNEL_RESET_PASSWORD', // 重置密码
    CHANNEL_UPDATE_PHONE = 'CHANNEL_UPDATE_PHONE', // 更新手机号
}

// 短信类型枚举
export enum SMS_TYPE {
    LOGIN_CODE = 'LOGIN_CODE', // 登录验证码
    REGISTER_CODE = 'REGISTER_CODE', // 注册验证码
    RESET_PASSWORD_CODE = 'RESET_PASSWORD_CODE', // 重置密码验证码
}

export enum CONNECTION_TYPE {
    PASSWORD = 'PASSWD', // 密码登录
    PASSCODE = 'VERIFY_CODE', // 验证码登录
}

export enum VERIFY_METHOD {
    PHONE_PASSCODE = 'PHONE_PASSCODE', // 短信验证码验证
    PASSCODE = 'PASSCODE', // 短信验证码验证
}

export interface sendCodeParams {
    phoneNumber: string
    channel: SMS_CHANNEL
    phoneCountryCode?: string
    captchaToken?: string
    constId?: string
}

// 不需要登录态的公共短信接口参数类型
export interface sendPublicSmsParams {
    phone: string
    type: SMS_TYPE
    captcha: string
    randstr: string
}

export interface LoginResultModel {
    token: string
}

export function sendCodeApi(params: sendCodeParams) {
    return usePost<LoginResultModel, sendCodeParams>('/message/send-sms', {
        phoneCountryCode: '86',
        ...params,
    }, {
        // 设置为false的时候不会携带token
        token: false,
        // 开发模式下使用自定义的接口
        // customDev: true,
        // 是否开启全局请求loading
        loading: true,
    })
}

/**
 * 发送短信的公共接口，无需登录态
 * @param params 短信发送参数
 * @returns API响应
 */
export function sendPublicSmsApi(params: sendPublicSmsParams) {
    return usePost<any, sendPublicSmsParams>('/public/sendSms', params, {
        // 设置为false的时候不会携带token
        token: false,
        // 是否开启全局请求loading
        loading: true,
    })
}

export function refreshToken(params: any) {
    return usePost('/user/refreshAccessToken', {
        ...params,
    }, {
        loading: true,
        // baseServer: ServerAddress.RamServer,
    })
}


// 获取用户实名认证状态
export function refreshUserFaceIDStatusApi(params: any) {
    return useGet('/user/refreshUserFaceIDStatus', {
        ...params,
    }, {
        loading: false,
        baseServer: ServerAddress.RamServer,
    })
}

// 获取实名认证code
export function getFaceIdUrlApi(params: any) {
    return useGet('/user/getFaceIdUrl', {
        ...params,
    }, {
        loading: false,
        baseServer: ServerAddress.RamServer,
    })
}

