<script setup lang="ts">
import {useLayoutMenuProvide} from '~/components/page-container/context'
import { legacyLogicalPropertiesTransformer } from 'ant-design-vue';
const {setLocale} = useI18nLocale()
const appStore = useAppStore()
const {theme} = storeToRefs(appStore)
const {antd} = useI18nLocale()
const layoutMenu = useLayoutMenu()
useLayoutMenuProvide(layoutMenu, appStore)

const focused = useWindowFocus()

watch(focused, (isFocused) => {
    if (isFocused) {

    }
})

onMounted(() => {
    // TODO 临时写死
    setLocale('zh-CN')
})
</script>

<template>
  <a-style-provider hash-priority="high" :transformers="[legacyLogicalPropertiesTransformer]">
    <a-config-provider :theme="theme" :locale="antd">
<!--        <template #renderEmpty>-->
<!--            <a-empty image="" class="text-center mt-20px">-->
<!--                <template #description>-->
<!--                    <p>暂无数据</p>-->
<!--                </template>-->
<!--            </a-empty>-->
<!--        </template>-->
        <a-app class="w-full h-full font-chinese antialiased">
            <TokenProvider>
                <RouterView/>
                <div id="captchaDom" fixed z-9999/>
            </TokenProvider>
        </a-app>
    </a-config-provider>
  </a-style-provider>
</template>

<style>
.dx_captcha_basic_overlay, .dx_captcha_loading_overlay {
    z-index: 9998 !important;
}

.dx_captcha_clickword-style-popup, .dx_captcha_basic-style-popup {
    z-index: 9999 !important;
}
</style>
