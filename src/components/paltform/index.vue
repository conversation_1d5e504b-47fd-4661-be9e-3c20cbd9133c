<template>
  <div class="flex download-box">
    <div
      class="w-120px h-122px position-relative platform"
      :style="styleObj"
      v-for="(item, index) in paltformList"
      :key="index"
    >
      <div>
        <img :src="item.icon" class="mt-20px w-46px h-46px" />
        <div class="color-[#687C97] mt-8px">{{ item.name }}</div>
        <div class="text-10px color-[#999999]" v-if="item.sub">
          {{ item.sub }}
        </div>
      </div>
      <div
        v-if="item.online"
        class="position-absolute top-0 left-0 w-full h-full cursor-pointer download-btn"
        @click="handleDownloadClick(item)"
        :style="styleObj"
      >
        <div class="w-full h-full">
          <img
            src="@/assets/images/dashboard/download.webp"
            alt=""
            class="mt-28px"
          />
          <div class="color-[#333333] mt-8px">点击下载</div>
        </div>
      </div>
      <div v-if="!item.online" class="online-tag">待上线</div>
      <img src="@/assets/images/dashboard/tag.png" alt="" class="recommend-tag" v-if="os == item.name"/>
    </div>
  </div>
</template>

<script setup>
import {getCurrentOS} from "~/utils/common-utils.ts";
import windowsIcon from "@/assets/images/dashboard/windows.webp";
import macOSIcon from "@/assets/images/dashboard/macOS.webp";
import androidIcon from "@/assets/images/dashboard/android.webp";
import appleIcon from "@/assets/images/dashboard/apple.webp";

const props = defineProps({
  styleObj: {
    type: Object,
    default: () =>{},
  },
});

const os = getCurrentOS()



const paltformList = [
  { name: "Windows", paltform:'Windows', sub: "", icon: windowsIcon, online: true },
  { name: "MacOS", paltform:'MacOS-Apple', sub: "", icon: macOSIcon, online: true },
  { name: "ios", paltform:'IOS', sub: "", icon: appleIcon, online: false },
  { name: "android", paltform:'Android', sub: "", icon: androidIcon, online: false },
];

if(os == 'MacOS'){
  const idx = paltformList.findIndex(item => item.name === 'MacOS');
  if (idx > 0) {
    const macItem = paltformList.splice(idx, 1)[0];
    paltformList.unshift(macItem);
  }
}

const handleDownloadClick = (item) => {
  useDownloadEvent(item.paltform);
};
</script>

<style scoped lang='less'>
.download-box {

  .platform {
    margin-right: 12px;
    position: relative;
    background: linear-gradient( 136deg, #F6FAFF 0%, #EDF6FE 51%, #F7FAFF 100%);
    border-radius: 6px 6px 6px 6px;
    text-align: center;
    .download-btn {
      background: linear-gradient(
        136deg,
        #f6faff 0%,
        #edf6fe 51%,
        #f7faff 100%
      );
      display: block;
      opacity: 0;
      transform: scale(0.95);
      transition: opacity 0.5s ease, transform 0.5s ease;
      pointer-events: none; /* 防止隐藏状态下的点击事件 */
    }
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      .download-btn {
        opacity: 1;
        transform: scale(1);
        pointer-events: auto; /* 恢复点击事件 */
      }
    }
    .online-tag{
      height: 20px;
      background: linear-gradient( 149deg, #00ABF7 0%, #00D492 100%);
      border-radius: 20px 6px 0px 20px ;
      font-size: 10px;
      color: #FFFFFF;
      position: absolute;
      top: -10px;
      right: 0;
      line-height: 20px;
      padding: 0 6px 0 10px
    }
    .recommend-tag{
      width: 55px;
      height: 25.5px;
      position: absolute;
      top: -10px;
      right: 0;
    }
  }
}
</style>
