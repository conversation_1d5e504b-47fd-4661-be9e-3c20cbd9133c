<script setup lang="ts">
interface MoreInfoType {
    routerName: string;
    routerPath: string;
    filterParams: any;
}

const props = defineProps(["moreInfo", "loading", "title", "hideMore"]);

const headerStyle = {padding: "0 16px", borderBottom: "none", marginBottom: 0};
const bodyStyle = {padding: "0 16px 0"};

function openMore(moreInfo: MoreInfoType) {
    const filterParams = moreInfo.filterParams || null;
    if (filterParams) {
        const localInfo = {
            routerName: moreInfo.routerName,
            routerPath: moreInfo.routerPath,
            params: moreInfo.filterParams,
        };
        localStorage.setItem(`${moreInfo.routerName}`, JSON.stringify(localInfo));
    }
    const url = moreInfo.routerPath;
    routerOpen(url);
}
</script>

<template>
    <a-card :loading="props.loading" :head-style="headerStyle" :body-style="bodyStyle">
        <template v-if="!!title" #title>
            <div class="tCard-title">
                <slot name="title">
                    <span>{{ title }}</span>
                </slot>
            </div>
        </template>
        <template v-if="!hideMore && moreInfo" #extra>
            <slot name="extra">
                <div class="more-btn" @click="openMore(moreInfo)">···更多</div>
            </slot>
        </template>
        <slot/>
    </a-card>
</template>

<style scoped lang="less">
.tCard-title {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 5;

  &:before {
    content: " ";
    width: 4px;
    height: 14px;
    background: #fb7701;
    border-radius: 2px 2px 2px 2px;
    display: inline-block;
    margin-right: 8px;
  }
}

.more-btn {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 14px;
  color: var(--primary-color);
  line-height: 22px;
  text-align: left;
  cursor: pointer;
  position: relative;
  z-index: 5;
}
</style>
