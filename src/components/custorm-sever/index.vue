<script setup lang="ts">
const props = defineProps({
    content: {
        type: String,
        default: '空白的,扫不了',
    },
    width: {
        type: Number,
        default: 200,
    },
    status: {
        type: [String, undefined],
        default: undefined,
    },
})
const userStore = useUserStore()
const {userInfo} = storeToRefs(userStore)
const myServe = unref(userInfo.value?.weChatQrUrl)
</script>

<template>
    <div class="server-box inline-block bg-[#F7F8FA] overflow-hidden"
         :style="{ width: `${width}px`, height: `${width}px` }">
        <!--        <a-qrcode :size="width" :value="content" :bordered="false" :status="status"></a-qrcode> -->
        <img :src="myServe || '@/assets/images/serve.jpg'">
    </div>
</template>

<style scoped lang="less">
.server-box {
  padding: 8px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
