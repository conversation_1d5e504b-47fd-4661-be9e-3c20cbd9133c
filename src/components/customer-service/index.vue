<script setup lang="ts">
const defaultWeChatQrUrl = ref('https://upload.wikimedia.org/wikipedia/commons/thumb/b/bc/QR_code_of_Chinese_Wikipedia_main_page_20131019.svg/660px-QR_code_of_Chinese_Wikipedia_main_page_20131019.svg.png');
const userStore = useUserStore()
const {userInfo} = storeToRefs(userStore);
</script>

<template>
  <a-dropdown placement="bottom">
    <div class="qh-customer-service">
      <span class="qh-customer-service-box" hover="color-primary">
        <img src="@/assets/images/common/service.webp" alt="" />
      </span>
    </div>

    <template #overlay>
      <div class="customer-service-modal">
        <img :src="userInfo?.weChatQrUrl || defaultWeChatQrUrl" alt="" style="width: 218px; height: 218px" />
        <div style="margin-top:12px">专属客服</div>
      </div>
    </template>
  </a-dropdown>
</template>

<style lang="less" scoped>
.qh-customer-service {
  display: flex;
  align-items: center;
  height: 80px;
  .qh-customer-service-box{
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #F2F3F5;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      width: 14px;
      height: 28px;
    }
  }
}
.customer-service-modal {
  width: 226px;
  height: 272px;
  position: relative;
  padding: 4px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.1);
  text-align: center;
  font-size: 16px;
  color: #1D2129;
  line-height: 22px;
}
</style>
