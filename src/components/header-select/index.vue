<script setup lang="ts">
import {QuestionCircleOutlined} from '@ant-design/icons-vue'

const props = defineProps({
    label: {
        type: String,
        default: '类型',
    },
    labelWidth: {
        type: [Number, String],
        default: 80,
    },
    value: {
        type: String,
        default: '',
    },
    valueInKey: { // change是否返回选择对象
        type: Boolean,
        default: false,
    },
    hasAll: {
        type: Boolean,
        default: false,
    },
    dataSource: {
        type: Array,
        default: () => ([
            // {
            //     label: '',
            //     value: '值',
            //     tips: '问题',
            // },
        ]),
    },
    hideTitle: {
        type: <PERSON>olean,
        default: false,
    },
})
const emit = defineEmits(['change'])
const listSource = computed<any []>(() => {
    const arr = Array.isArray(props?.dataSource) ? [...props.dataSource] : []
    if (props.hasAll) {
        arr.unshift({
            label: '全部',
            value: undefined,
        })
    }
    return arr
})
const currentValue = defineModel('value')

function selectValue(item) {
    if (item?.value === currentValue.value)
        return
    currentValue.value = item?.value
    emit('change', item?.value, item)
}
</script>

<template>
    <div class="header-select">
        <span v-if="!hideTitle" class="title" :style="{ width: `${Number(labelWidth)}px` }">{{ label }}：</span>
        <div class="item-list">
            <ul>
                <li v-for="item in listSource" :class="{ active: currentValue === item?.value }"
                    @click="selectValue(item)">
                    <span>{{ item?.label }}</span>
                    <template v-if="item.tips">
                        <a-tooltip arrow-point-at-center placement="bottom" :title="item.tips">
                            <QuestionCircleOutlined ml-5px/>
                        </a-tooltip>
                    </template>
                </li>
            </ul>
        </div>
    </div>
</template>

<style scoped lang="less">
.header-select {
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  .title {
    width: 80px;
    text-align: right;
    line-height: 24px;
    flex-shrink: 0;
    color: var(--text-color-2);
  }

  .item-list {
    width: 100%;
    height: max-content;

    ul {
      width: 100%;
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      li {
        user-select: none;
        min-height: 24px;
        list-style: none;
        padding: 5px 8px;
        line-height: 14px;
        text-align: center;
        font-size: 14px;
        background: transparent;
        border-radius: 24px;
        cursor: pointer;
        transition: all .1s ease-in-out;

        & + li {
          margin-left: 16px;
        }

        &.active {
          color: var(--cloud-color-primary);
          background: rgb(254, 241, 229);
        }

        &:hover {
          color: var(--cloud-color-primary);
          background: rgb(254, 241, 229);
        }
      }
    }
  }
}
</style>
