<script setup lang="ts">
import {ShowTypeMap} from '~/components/auth-mask/constant.ts'
import ChatAuthBg from '@/assets/images/goods/chatAuthBg.jpg'

const props = defineProps({
    showMask: {
        type: Boolean,
        default: false,
    },
    maskImg: {
        type: [String, undefined],
        default: ChatAuthBg,
    },
    hideMaskImg: {
        type: Boolean,
        default: false,
    },
    hasContent: {
        type: Boolean,
        default: false,
    },
    showType: {
        type: String,
        default: ShowTypeMap.Default,
    },
    height: {
        type: String,
        default: '180px',
    },
})
const maskStyle = computed(() => {
    const style: any = {}
    if (props.maskImg && !props.hideMaskImg)
        style.backgroundImage = `url(${props.maskImg})`

    return style
})

function openBuy() {
    routerOpen('/buyVip')
}
</script>

<template>
    <div class="auth-mask" :style="{ 'min-height': height }">
        <template v-if="!showMask || hasContent">
            <slot/>
        </template>
        <div v-if="showMask" class="mask-box" :class="{ 'hover-mask': showType === ShowTypeMap.Hover }">
            <div class="mask-box-bg" :style="maskStyle"/>
            <div z-2 text-center>
                <slot name="maskContent">
                    <div mb-20px>
                        开通个人版以上会员即可使用此功能
                    </div>
                    <a-button type="primary" w-134px rounded-32px @click="openBuy">
                        去升级
                    </a-button>
                </slot>
            </div>
        </div>
    </div>
</template>

<style scoped lang="less">
.auth-mask {
  display: inline-block;
  position: relative;
  width: 100%;
  height: 100%;

  .mask-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
      background-color: rgba(255, 255, 255, 0.9);
      filter: blur(16px);
    }

    &.hover-mask {
      opacity: 0;
      transition: opacity .1s ease-in-out;

      &:hover {
        opacity: 1;
      }
    }
  }
}
</style>
