<script setup lang="ts">
import WarningIcon from "@/Icons/warning.svg";
import paltform from "../paltform/index.vue";
const openModal = ref(false);

function closeModal() {
  openModal.value = false;
}
function showModal() {
  openModal.value = true;
}

defineExpose({
  closeModal,
  showModal,
});
</script>

<template>
  <a-modal
    class="download-modal"
    v-model:open="openModal"
    :centered="centered"
    destroy-on-close
    :width="614"
    :footer="null"
    @cancel="closeModal"
  >
    <div class="download-content">
      <div class="title mb-48px" >客户端下载</div>
      <div style="padding-bottom: 42px;">
        <paltform :styleObj="{background:'#ffffff'}"/>
      </div>
    </div>
  </a-modal>
</template>

<style  lang="less">
.download-modal {
  .ant-modal-body {
    width: 100%;
    background: url("@/assets/images/common/down-bg.png") no-repeat;
    background-size: cover;
    padding:32px 64px
  }
  .ant-modal-content{
    overflow: hidden;
  }
  .download-content {
    text-align: center;
    .title {
      font-weight: 500;
      font-size: 22px;
      color: #3d3d3d;
      line-height: 32px;
      margin-top: 27px;
    }
  }
}
</style>
