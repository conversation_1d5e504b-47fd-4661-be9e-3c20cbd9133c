<template>
  <div class="flex gap-4">
    <!-- 左侧卡片 (602px × 210px) -->
    <div
      class="card-left flex-1 min-w-415px h-[210px] bg-white rounded-xl shadow-sm relative overflow-hidden p-20px"
    >
      <!-- 顶部用户信息区域 -->
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <div class="w-46px h-46px">
            <img src="@/assets/images/dashboard/avatar.webp" alt="" />
          </div>
          <div class="ml-2 text-lg font-medium">{{ userInfo?.nickname }}</div>
        </div>
        <router-link to="/dashboard/user">
          <div class="flex items-center text-gray-500 cursor-pointer">
            <span>账号信息</span>
            <div
              class="ml-1 w-5 h-5 border border-gray-300 rounded-sm flex items-center justify-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </div>
          </div>
        </router-link>
      </div>

      <!-- 底部余额信息区域 -->
      <div class="balance p-24px mt-16px">
        <div class="relative flex justify-between items-center h-full z-1">
          <div class="flex gap-16">
            <div class="cursor-pointer" @click="showRechargeModal">
              <div class="text-14px">余额</div>
              <div>
                <span class="text-12px">¥</span>
                <span class="text-24px">{{
                  formatAmount(userInfo?.balance || 0)
                }}</span>
              </div>
            </div>
            <router-link to="/dashboard/coupons">
              <div class="color-#333333">
                <div class="text-14px">优惠券</div>
                <div class="text-24px">{{ userInfo?.couponCount || 0 }}张</div>
              </div>
            </router-link>
          </div>

          <a-button class="btn" @click="showRechargeModal"> 充值 </a-button>
        </div>
        <img
          src="@/assets/images/dashboard/info-bg.webp"
          alt=""
          class="balance-bg"
        />
      </div>
    </div>

    <!-- 右侧卡片 (276px × 210px) -->
    <div
      class="card-right w-[560px] h-[210px] bg-white rounded-xl shadow-sm p-4 text-center"
    >
      <div class="text-lg font-medium mb-20px">下载青虎云电脑</div>

      <div class="flex download-box">
        <paltform />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatAmount } from "~/utils/common-utils.ts";
import { useRechargeDialog } from "~/hooks/useRechargeDialog.tsx";
import paltform from "@/components/paltform/index.vue";

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

const { rechargeModal } = useRechargeDialog();


const showRechargeModal = () => {
  rechargeModal(() => {
    userStore.getUserInfo();
  });
};

</script>

<style scoped lang='less'>
.balance {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  background: #fde2ba;
  .balance-bg {
    position: absolute;
    top: 0;
    right: 0;
    width: 478px;
    height: 100%;
    // background: url("@/assets/images/dashboard/info-bg.webp") no-repeat;
    // background-size: cover;
    z-index: 0;
  }
}
.btn {
  width: 110px;
  height: 36px;
  background: linear-gradient(135deg, #e94e74 0%, #9b0fff 63%, #4d54ff 100%);
  border-radius: 20px 20px 20px 20px;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  border: none;
}

</style>
