<template>
  <a-card class="rounded-12px min-h-556px">
    <div>
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <!-- <div>我的云电脑</div> -->
          <router-link to="/product">
            <a-button class="buy-btn" size="small">购买云电脑</a-button>
          </router-link>
        </div>
        <div class="flex items-center text-gray-500 cursor-pointer" @click="router.push('/cloudDesktop')">
          <span>查看全部</span>
          <div
            class="ml-1 w-5 h-5 border border-gray-300 rounded-sm flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </div>
        </div>
      </div>
      <div class="mt-13px">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="tableLoading"
          :pagination="pagination"
          :row-class-name="
            (_record, index) => (index % 2 === 1 ? 'table-striped' : null)
          "
          @change="handleTableChange"
        >
          <!-- <template #headerCell="{ column }">
            <template v-if="column.key === 'name'">
              <span>
                <smile-outlined />
                Name
              </span>
            </template>
          </template> -->

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a>
                {{ record.name }}
              </a>
            </template>
            <template v-else-if="column.key === 'tags'">
              <span>
                <a-tag
                  v-for="tag in record.tags"
                  :key="tag"
                  :color="
                    tag === 'loser'
                      ? 'volcano'
                      : tag.length > 5
                      ? 'geekblue'
                      : 'green'
                  "
                >
                  {{ tag.toUpperCase() }}
                </a-tag>
              </span>
            </template>
             <!-- 设备状态 -->
             <template v-else-if="column.key === 'deviceState'">
              <StatusTag :status="record.deviceState" type="device"/>
            </template>

            <!-- 远程状态 -->
            <template v-else-if="column.key === 'remoteState'">
                <StatusTag :status="record.remoteState" type="remote"/>
            </template>
            <template v-else-if="column.key === 'action'">
              <span>
                <div>
                   <a-button type="link" @click="handleDesktopOperation('localRemote', record)">本地远程</a-button>
                </div>
               <div>
                  <a-button :disabled="[AllStatusMapping.STOPPING, AllStatusMapping.STOPPED].includes(record.deviceState)"  type="link" @click="handleDesktopOperation('networkRemote', record)">网页远程</a-button>
                </div>
              </span>
            </template>
          </template>
        </a-table>
      </div>

    </div>
  </a-card>
</template>

<script lang="ts" setup>
import { AllStatusMapping } from '~#/constant.ts';
import { getUserInstanceListApi } from "@/api/common/list.ts";
import type {
    CloudDesktopRecord,
    CloudDesktopOperationType,
    CloudDesktopFilterForm
} from '~/types/cloud-desktop';

import { useDownload } from '@/composables/use-download'
import {useCheckDueInstance} from "~/hooks/useCheckDueInstance.ts";

 const { open, close } = useDownload()



const columns = [
  {
    title: "云电脑名称",
    dataIndex: "desktopName",
    key: "desktopName",
  },
  {
    title: "网络地址",
    dataIndex: "networkAddress",
    key: "networkAddress",
  },
  {
    title: "设备区域",
    dataIndex: "zoneName",
    key: "zoneName",
  },
  {
    title: "到期时间",
    key: "expreTime",
    dataIndex: "expreTime",
  },
  {
    title: "设备状态",
    key: "deviceState",
    dataIndex: "deviceState",
  },
  {
    title: "远程状态",
    key: "remoteState",
    dataIndex: "remoteState",
  },
  {
    title: "操作",
    key: "action",
    dataIndex: "action",
  },
];
const router = useRouter();
const tableData = ref([]);
const tableLoading = ref(false);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: any) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50"],
});

const getUserInstanceList = async () => {
  let params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    expreState: ''
  };
  tableLoading.value = true;
  let res = await getUserInstanceListApi(params);
  tableLoading.value = false;
  if (res.success) {
    tableData.value = res?.payload || [];
    pagination.total = res.pageInfo.total;
  }
};

// 表格分页变化
const handleTableChange = (pag: any) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    getUserInstanceList()
}

const handleDesktopOperation = async (type: CloudDesktopOperationType, record: CloudDesktopRecord) => {
    switch (type) {
        case 'localRemote':
            // 弹出下载弹窗
            // downloadVisible.value = true;
             open()
            break;
        case 'networkRemote':
            // 跳转到远程连接页面
            // 这里使用了 Vue Router 的 resolve 方法来生成链接，确保路由正确解析
            let url = router.resolve({
                path: '/cloud-desktop-connect',
                query: {
                    workspaceId: record.workspaceId,
                    id: record.id,
                    terminalType: "WEB",
                    desktopName: record.desktopName,
                    connectType: 'network',
                    instanceId: record.instanceId
                }
            }).href;
            window.open(url, '_blank');
            break
        default:
            break
    }
}

onMounted(() => {
  getUserInstanceList();
  useCheckDueInstance()
});
</script>

<style scoped lang='less'>
.buy-btn {
  font-size: 14px;
  color: #08c18a;
  background: rgba(8, 193, 138, 0.1);
  border: 1px solid #08c18a;
  margin-left: 16px;
  border-radius: 50px;
  padding:0 10px;
}
:deep(.table-striped) td {
  background-color: #fafafa;
}
:deep(.ant-table-tbody) {
  td {
    border-top: none !important;
    border-bottom: none !important;
  }
}
:deep(.ant-table-thead) {
  th {
    background: #f4f5fa !important;
  }
}
</style>
