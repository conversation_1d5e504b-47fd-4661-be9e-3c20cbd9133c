<script setup lang="ts">
import { getContactMeStatusApi, type ContactMeInfo } from '@/api/common/user';

const LOCAL_STORAGE_KEY = 'contactMeStatus';
const visible = ref(false);
const weChatQrUrl = ref('https://upload.wikimedia.org/wikipedia/commons/thumb/b/bc/QR_code_of_Chinese_Wikipedia_main_page_20131019.svg/660px-QR_code_of_Chinese_Wikipedia_main_page_20131019.svg.png');
const loading = ref(true);
const pollCount = ref(0);
const MAX_POLL_COUNT = 20;
const qrCodeExpired = ref(false);

// 创建轮询函数
let intervalId: number | null = null;

// 检查用户是否已添加客服
const checkContactMe = async () => {
  try {
    // 从localStorage获取缓存状态
    const cachedStatus = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (cachedStatus === 'true') {
      return;
    }
    
    // 显示加载中
    loading.value = true;
    qrCodeExpired.value = false;
    
    // 调用接口获取状态
    const response = await getContactMeStatusApi<ContactMeInfo>();
    
    // 处理接口返回数据
    if (response) {
      // 设置二维码URL，如果接口返回的URL为空则使用默认URL
      if (response.weChatQrUrl) {
        weChatQrUrl.value = response.weChatQrUrl;
      }
      
      // 如果已添加客服
      if (response.contactMe) {
        // 缓存状态
        localStorage.setItem(LOCAL_STORAGE_KEY, 'true');
        // 隐藏弹窗
        visible.value = false;
      } else {
        // 显示弹窗
        visible.value = true;
        // 开始轮询
        startPolling();
      }
    }
    
    loading.value = false;
  } catch (error) {
    console.error('获取客服信息失败:', error);
    loading.value = false;
  }
};

// 轮询检查是否已添加客服
async function checkContactMeStatus() {
  if (pollCount.value >= MAX_POLL_COUNT) {
    // 标记二维码已过期
    qrCodeExpired.value = true;
    stopPolling();
    return;
  }
  
  try {
    const response = await getContactMeStatusApi<ContactMeInfo>();
    
    if (response && response.contactMe) {
      // 已添加客服，缓存状态并隐藏弹窗
      localStorage.setItem(LOCAL_STORAGE_KEY, 'true');
      visible.value = false;
      stopPolling();
    }
    
    pollCount.value++;
  } catch (error) {
    console.error('轮询检查失败:', error);
  }
}

// 开始轮询
const startPolling = () => {
  pollCount.value = 0;
  qrCodeExpired.value = false;
  // 清除可能存在的旧定时器
  stopPolling();
  // 创建新的定时器
  intervalId = window.setInterval(checkContactMeStatus, 3000);
};

// 停止轮询
const stopPolling = () => {
  if (intervalId !== null) {
    window.clearInterval(intervalId);
    intervalId = null;
  }
};

// 刷新二维码
const refreshQrCode = async () => {
  await checkContactMe();
};

// 组件卸载时清理定时器
onUnmounted(() => {
  stopPolling();
});

// 组件挂载时检查状态
onMounted(() => {
  checkContactMe();
});
</script>

<template>
  <div v-if="visible" class="contact-me-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">添加客服开始使用云电脑</h2>
      </div>
      
      <div class="modal-body">
        <p class="modal-desc">添加企业微信客服，即可开始使用青虎云电脑，专属客服全程为您提供支持服务！</p>
        
        <div class="qrcode-container">
          <!-- 加载中 -->
          <div v-if="loading" class="loading-spinner">
            <a-spin size="large" />
          </div>
          
          <!-- 二维码已过期 -->
          <div v-else-if="qrCodeExpired" class="qrcode-expired">
            <div class="expired-overlay">
              <div class="expired-text">二维码已过期</div>
              <a-button type="primary" @click="refreshQrCode" class="refresh-btn">
                <template #icon>
                  <reload-outlined />
                </template>
                刷新
              </a-button>
            </div>
            <img :src="weChatQrUrl" alt="企业微信二维码" class="qrcode-image qrcode-blur" />
          </div>
          
          <!-- 正常显示二维码 -->
          <img v-else :src="weChatQrUrl" alt="企业微信二维码" class="qrcode-image" />
        </div>
        
        <p class="scan-tip">请使用微信扫码添加客服</p>
      </div>
      
      <div class="modal-footer">
        <p class="footer-text">添加成功后弹窗将自动关闭</p>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.contact-me-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 400px;
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.modal-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px;
}

.modal-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

.qrcode-container {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
  position: relative;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qrcode-blur {
  filter: blur(2px);
  opacity: 0.6;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.qrcode-expired {
  position: relative;
  width: 100%;
  height: 100%;
}

.expired-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.expired-text {
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 15px;
}

.refresh-btn {
  background: linear-gradient(to right, #00ABF7, #00D492);
  border: none;
  
  &:hover {
    background: linear-gradient(to right, #0095e6, #00b87d);
    border: none;
  }
}

.scan-tip {
  font-size: 16px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 500;
}

.footer-text {
  font-size: 12px;
  color: #999;
  margin: 0;
}
</style> 