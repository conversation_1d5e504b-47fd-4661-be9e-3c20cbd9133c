<template>
  <div>
    <div class="bg-[#ffffff] h-maxcontent rounded-12px p-16px min-h-556px">
      <div class="max-w-md mx-auto bg-white rounded-lg">
        <!-- 头部标题栏 -->
        <div class="flex justify-between items-center line-height-28px mt-">
          <div class="flex items-center">
            <img
              src="@/assets/images/dashboard/recommend.webp"
              alt=""
              class="recommend-icon"
            />
            <span class="text-[16px] text">热门推荐</span>
          </div>
          <div class="text-gray-400 text-sm flex items-center cursor-pointer" @click="toBuyPage">
            全部产品
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="w-3.5 h-3.5 ml-0.5"
            >
              <path
                fill-rule="evenodd"
                d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
        <div class="mt-18px">
          <!-- 产品卡片 - 进阶版 -->
          <div class="product-card px-16px py-10px" v-for="item in recommendList">
            <h3 class="text-[14px] mb-0">{{ item.skuName || '-' }}</h3>
            <p class="text-12px leading-16px">
              {{ item.sepcRemark }}
            </p>
            <a-divider style="margin: 10px 0" />
            <div class="flex flex-wrap mt-2px">
              <div class="flex items-center w-50% leading-17px mb-12px">
                <img
                  src="@/assets/images/dashboard/cpu.webp"
                  alt=""
                  class="mr-6px"
                />
                <span class="text-xs">CPU：{{ item.vcpu }}核</span>
              </div>

              <div class="flex items-center w-50% leading-17px mb-12px">
                <img
                  src="@/assets/images/dashboard/memory.webp"
                  alt=""
                  class="mr-6px"
                />
                <span class="text-xs">内存：{{ item.memory }}G</span>
              </div>

              <div class="flex items-center w-50% leading-17px mb-12px">
                <img
                  src="@/assets/images/dashboard/system.webp"
                  alt=""
                  class="mr-6px"
                />
                <span class="text-xs">系统盘：{{ item.systemDisk }}G</span>
              </div>
            </div>

            <div>
              <div>
                <span class="text-12px">低至：</span>
                <span class="text-[#ff4d4f] text-2xl font-bold">
                  <span class="color-[#F53F3F]">¥</span>
                  <span class="color-[#F53F3F] text-32px leading-17px"
                    >{{ formatAmount(item.asLow) }}</span
                  >
                </span>
                <span class="text-12px">/月</span>
              </div>

              <a-button
                class="w-full flex items-center justify-center mt-6px"
                type="primary"
                @click="toBuyPay(item)"
              >
                <img
                  src="@/assets/images/dashboard/package.webp"
                  alt=""
                  class="mr-6px"
                />
                订购
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
import {getSkuList} from "~/api/common/product";
import {formatAmount} from "~/utils/common-utils";

const router = useRouter();
const recommendList = ref([]);
const fetchSkuList = async () => {
    const {success, payload} = await getSkuList({
        reCommend: 'YES'
    })
    if (success) {
        recommendList.value = payload
    }
}

const toBuyPage = () => {
    router.push({
        path: '/product'
    });
}

const toBuyPay = (item) => {
    let href = router.resolve({
        path: '/product/pay',
        query: {
            skuId: item.id
        }
    }).href;
    window.open(href, '_blank')
}

onMounted(() => {
    fetchSkuList()
})

</script>

<style lang="less" scoped>
.recommend-icon {
  position: relative;
  left: -9px;
  top: 7px;
}
.text {
  position: relative;
  left: -9px;
}
.product-card {
  width: 266px;
  height: 230px;
  background: linear-gradient(180deg, #f4fafe 0%, #ffffff 52%, #fdfeff 100%);
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #ffffff;
  margin-bottom: 14px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>