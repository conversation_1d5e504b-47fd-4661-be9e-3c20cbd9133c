<template>
  <div class="py-6">
    <!-- 个人认证部分 -->
    <div class="w-998px m-auto h-324px mb-6 flex">
      <div
        class="border border-solid border-[#DCDCDC] rounded-tl-[6px] rounded-bl-[6px] overflow-hidden flex"
      >
        <!-- 左侧图标区域 -->
        <div
          class="w-[170px] bg-gray-50 flex flex-col items-center justify-center"
        >
          <div class="w-[70px] h-[70px] flex items-center justify-center">
            <img src="@/assets/images/dashboard/person.webp" alt="" />
          </div>
          <div class="text-14px color-[#3D3D3D] leading-28px">个人认证</div>
        </div>

        <!-- 中间说明区域 -->
        <div class="w-336px flex items-center ml-46px">
          <div>
            <div class="flex items-center mb-4">
              <img
                src="@/assets/images/dashboard/check.webp"
                alt=""
                class="mr-4px"
              />
              <div class="text-14px color-[#86909C]">
                请准备身份证号码进行绑定
              </div>
            </div>

            <div class="flex items-center mb-4">
              <img
                src="@/assets/images/dashboard/check.webp"
                alt=""
                class="mr-4px"
              />
              <div class="text-14px color-[#86909C]">
                一个身份证号码仅支持绑定一个账号
              </div>
            </div>

            <div class="flex items-center">
              <img
                src="@/assets/images/dashboard/check.webp"
                alt=""
                class="mr-4px"
              />
              <div class="text-14px color-[#86909C]">可申请开具票据</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧认证选项区域 -->
      <div class="w-[444px]">
        <!-- 选项1 -->
        <div class="py-20px px-43px h-108px custom-border-1 rounded-tr-[6px]">
          <div class="flex justify-between items-center">
            <div class="text-sm font-medium color-[#3D3D3D]">
              手机扫描二维码
            </div>
            <a-button
              class="w-120px h-36px bg-[#165DFF] text-white text-14px flex items-center justify-center rounded"
              v-if="
                !userFaceIDStatus ||
                (userFaceIDStatus &&
                  userFaceIDStatus.auditStatus === 'NOT_SUBMIT')
              "
              @click="handleAuthClick"
            >
              微信认证
              <SwapRightOutlined />
            </a-button>
            <a-button
              class="w-120px h-36px bg-[#F53F3F] text-white text-14px flex items-center justify-center rounded"
              v-else-if="
                userFaceIDStatus && userFaceIDStatus.auditStatus === 'REJECT'
              "
              @click="handleAuthClick"
            >
              <CloseOutlined />
              认证未通过
            </a-button>
            <a-button
              class="w-120px h-36px bg-[#00B42A] text-white text-14px flex items-center justify-center rounded"
              v-else-if="
                userFaceIDStatus && userFaceIDStatus.auditStatus === 'PASS'
              "
            >
              <img
                src="@/assets/images/dashboard/success.webp"
                alt=""
                class="mr-4px"
              />
              已通过
            </a-button>
            <a-button
              class="w-120px h-36px bg-[#FF7D00] text-white text-14px flex items-center justify-center rounded"
              v-else-if="
                userFaceIDStatus && userFaceIDStatus.auditStatus === 'WAITE'
              "
            >
              <img
                src="@/assets/images/dashboard/clock.webp"
                alt=""
                class="mr-4px"
              />
              审核中
            </a-button>
          </div>
          <div class="flex items-center text-xs color-[#86909C]">
            <span>输入身份证信息</span>
            <span class="mx-1">→</span>
            <span>微信人脸识别</span>
          </div>
          <div class="mt-2 text-xs flex items-center" v-if="userFaceIDStatus && userFaceIDStatus.auditStatus ==='REJECT'">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-red-500"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <span class="ml-1 color-[#3D3D3D]"
              >认证未通过，原因：{{ userFaceIDStatus.remark }}</span
            >
          </div>
        </div>

        <!-- 选项2 -->
        <div class="py-20px px-43px h-108px custom-border-2">
          <div class="flex justify-between items-center">
            <div class="text-sm font-medium color-[#3D3D3D]">
              手机扫描二维码
            </div>
            <div
              class="w-120px h-36px text-center bg-gray-200 rounded flex items-center justify-center position-relative"
            >
              <span class="text-14px">浏览器认证</span>
              <span class="tag">开发中</span>
            </div>
          </div>
          <div class="flex items-center text-xs color-[#86909C]">
            <span>输入身份证信息</span>
            <span class="mx-1">→</span>
            <span>浏览器人脸识别</span>
          </div>
        </div>

        <!-- 选项3 -->
        <div class="py-20px px-43px h-108px custom-border-2 rounded-br-[6px]">
          <div class="flex justify-between items-center">
            <div class="text-sm font-medium color-[#3D3D3D]">填写基本信息</div>
            <div
              class="w-120px h-36px text-center bg-gray-200 rounded flex items-center justify-center position-relative"
            >
              <span class="text-14px">非大陆用户认证</span>
              <span class="tag">开发中</span>
            </div>
          </div>
          <div class="flex items-center text-xs color-[#86909C]">
            <span>(姓名、证件号码、证件上传)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 企业认证部分 -->
    <div
      class="w-998px m-auto h-168px flex justify-between border border-solid border-[#DCDCDC] rounded-[6px] overflow-hidden"
    >
      <div class="flex">
        <!-- 左侧图标区域 -->
        <div
          class="w-[170px] bg-gray-50 flex flex-col items-center justify-center py-10"
        >
          <div class="w-[70px] h-[70px] flex items-center justify-center mb-3">
            <img src="@/assets/images/dashboard/enter.webp" alt="" />
          </div>
          <div class="text-sm text-gray-700">企业认证</div>
        </div>

        <!-- 中间说明区域 -->
        <div class="w-336px flex items-center ml-46px">
          <div>
            <div class="flex items-center mb-4">
              <img
                src="@/assets/images/dashboard/check.webp"
                alt=""
                class="mr-4px"
              />
              <div class="text-sm color-[#86909C]">
                请准备营业执照、企业对公账号
              </div>
            </div>

            <div class="flex items-center">
              <img
                src="@/assets/images/dashboard/check.webp"
                alt=""
                class="mr-4px"
              />
              <div class="text-sm color-[#86909C]">
                支持开具普票+增值税专用发票
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧认证选项区域 -->
      <div class="w-[320px] flex items-center justify-end pr-43px">
        <div
          class="w-120px h-36px text-center bg-gray-200 text-xs rounded flex items-center justify-center position-relative"
        >
          <span class="text-gray-700">企业认证</span>
          <span class="tag">开发中</span>
        </div>
      </div>
    </div>
    <AuthModal
      @success="handleAuthSuccess"
      ref="authModalRef"
    />
  </div>
</template>

<script setup>
import { CloseOutlined, SwapRightOutlined } from "@ant-design/icons-vue";
import { refreshUserFaceIDStatusApi } from "@/api/common/auth.ts";
import AuthModal from "./auth-modal.vue";
import { message } from 'ant-design-vue';

const authModalRef = ref(null);
const userFaceIDStatus = ref(null); // 用户认证状态


const getUserFaceIDStatus = async (isTips = true) => {
  let res = await refreshUserFaceIDStatusApi();
  if (res.code === 200) {
    userFaceIDStatus.value = res.payload;
  } else {
    if(isTips){
        message.error(res.message);
        if(res.code == 503){
          userFaceIDStatus.value = {
            auditStatus:'REJECT',
            remark:res.message
          }
        }
    }

  }
};

const handleAuthSuccess = (payload) => {
  userFaceIDStatus.value = payload;
};

// 点击认证按钮
const handleAuthClick = () => {
  authModalRef.value.open();
};


onMounted(() => {
  getUserFaceIDStatus();
});
</script>

<style scoped lang="less">
.custom-border-1 {
  border-top: 1px solid #dcdcdc;
  border-right: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
}
.custom-border-2 {
  border-right: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
}
.tag {
  font-size: 8px;
  position: absolute;
  top: -8px;
  right: 0;
  border-radius: 0 6px 0 6px;
  color: #ff8503;
  background: #ffe6d0;
  padding: 0 2px;
}
</style>
