<template>
  <div class="">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-sm overflow-hidden">
      <!-- 头部标题栏 -->
      <div class="flex justify-between items-center px-16px py-14px">
        <div class="text-[16px] text-[#1D2129] leading-28px">常见问题</div>
        <div class="flex items-center text-[#666] text-[14px] cursor-pointer" @click="toDoHelp">
          帮助中心
          <div class="ml-4px rounded-sm flex items-center justify-end">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="w-3 h-3"
            >
              <path
                fill-rule="evenodd"
                d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="h-px bg-gray-200 mx-16px"></div>

      <!-- 问题列表 -->
      <div class="pt-22px pb-12px">
        <!-- 问题 01 - 红色 -->
        <div class="qa-list flex items-center px-16px cursor-pointer mb-16px" @click="toDoHelp">
          <div class="text-[#ff4d4f] text-[18px] font-medium mr-2">01</div>
          <div class="text w-239px text-[14px] text-[#333] leading-16px">
            云桌面使用网络要求
          </div>
        </div>
        <div class="qa-list flex items-center px-16px cursor-pointer mb-16px" @click="toDoHelp">
          <div class="text-[#FF6802] text-[18px] font-medium mr-2">02</div>
          <div class="text w-239px text-[14px] text-[#333] leading-16px">
            如何添加云桌面?
          </div>
        </div>
        <div class="qa-list flex items-center px-16px cursor-pointer mb-16px" @click="toDoHelp">
          <div class="text-[#F9A827] text-[18px] font-medium mr-2">03</div>
          <div class="text w-239px text-[14px] text-[#333] leading-16px">
            云桌面数据可以保存多久?
          </div>
        </div>
        <div class="qa-list flex items-center px-16px cursor-pointer mb-16px" @click="toDoHelp">
          <div class="text-[#DBDBDB] text-[18px] font-medium mr-2">04</div>
          <div class="text w-239px text-[14px] text-[#333] leading-16px">
            青虎云支持哪些外设设备?
          </div>
        </div>
        <div class="qa-list flex items-center px-16px cursor-pointer mb-16px" @click="toDoHelp">
          <div class="text-[#DBDBDB] text-[18px] font-medium mr-2">05</div>
          <div class="text w-239px text-[14px] text-[#333] leading-16px">
            云桌面在使用过程中出现“断连”情况怎么办?
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
// 组件逻辑可以在这里添加
const toDoHelp = () => {
  // 跳转到帮助中心的逻辑
  window.open('https://www.yuque.com/zhaojie/kb/gqgy9bkz2x7dih7f?singleDoc', '_blank');
};
</script>

<style scoped lang="less">
.qa-list {
  .text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    &:hover {
      color: var(--cloud-color-primary);
    }
  }
}
</style>