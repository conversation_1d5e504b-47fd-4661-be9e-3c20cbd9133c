<template>
  <div ref="modalRef">
    <a-modal
      v-model:open="visible"
      title="微信认证"
      @cancel="close"
      :footer="null"
      :getContainer="() => modalRef"
    >
      <div class="text-center">
        <div class="text-20px">微信认证</div>
        <div class="text-16px mt-17px mb-21px">
          请打开微信扫描二维码后继续完成认证
        </div>
        <a-spin :spinning="loading">
          <div class="w-200px h-200px p-7px bg-[#F1F1F1] rounded-[16px] m-auto position-relative">
            <QrCode
              :size="187"
              :value="qrcodeVal"
              :image-settings="{
                src: logoSrc,
                width: 40,
                height: 40,
                excavate: true
              }"
            />
            <div class="timeout-mask" v-if="isAuthTimeout" @click="handleRefresh">认证超时，请刷新</div>
          </div>
        </a-spin>
        <div
          class="text-14px lh-20px mt-24px flex items-center justify-center cursor-pointer"
          @click="handleRefresh"
        >
          <img
            src="@/assets/images/common/refresh.png"
            alt=""
            class="w-14px mr-6px"
          />
          刷新
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { message } from 'ant-design-vue';
import logoSrc from "@/assets/images/common/wechat.png";
import { getFaceIdUrlApi, refreshUserFaceIDStatusApi } from "@/api/common/auth.ts";
import QrCode from '~/components/qrcode/index.vue'
const emit = defineEmits(["success"]);

const modalRef = ref(null)
const visible = ref(false);
const loading = ref(false);
const qrcodeVal = ref(null);
const intervalId = ref(null);
const userFaceIDStatus = ref(null); // 用户认证状态
const isAuthTimeout = ref(false); // 是否超时
const qrcodeColor = ref({
  colorDark: "#000",
  colorLight: "#fff",
});

const close = (e) => {
  clearInterval(intervalId.value); // 清除轮询
  visible.value = false;
};

const open = (url) => {
  getFaceIdUrl();
};

const getFaceIdUrl = async () => {
  loading.value = true;
  let res = await getFaceIdUrlApi();
  loading.value = false;
  if (res.code === 200) {
    // 打开人脸识别弹窗
    if (res.payload) {
      qrcodeVal.value = res.payload;
      visible.value = true;

      handleInterval()
    }
  } else {
    // 处理错误情况
    console.error("获取人脸识别 URL 失败:", res.message);
  }
};

const getUserFaceIDStatus = async (isTips = true) => {
  let res = await refreshUserFaceIDStatusApi();
  if (res.code === 200) {
    userFaceIDStatus.value = res.payload;
    if (res.payload && res.payload.realName) {
      message.success('实名认证成功');
      emit("success", res.payload);
      close()
      
    } 
  } 
};

const handleInterval = () => {
  let count = 0;
  const maxCount = 120;
  intervalId.value = setInterval(async () => {
    await getUserFaceIDStatus();
    count++;
    if (count >= maxCount) {
      isAuthTimeout.value = true;
      clearInterval(intervalId.value);
    }
  }, 5000);
};

const handleRefresh = () => {
  isAuthTimeout.value = false;
  clearInterval(intervalId.value);
  getFaceIdUrl();
};

defineExpose({
  open,
});
</script>

<style scoped lang='less'>
:deep(.ant-modal-header) {
  border: none;
  background: #fbfbfb;
}
.timeout-mask{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
}
</style>
