<script setup lang="ts">
import {SMS_CHANNEL, SMS_TYPE, sendPublicSmsApi} from "@/api/common/auth";
import type {sendPublicSmsParams} from "@/api/common/auth";
import {CaptchaInfo} from "@/utils/constant";

const props = defineProps({
    channelType: {
        type: String as () => SMS_CHANNEL,
        default: SMS_CHANNEL.CHANNEL_LOGIN,
    },
    phone: {
        type: String,
        default: "",
    },
    phoneCountryCode: {
        type: [undefined, String],
        default: undefined,
    },
    sendValidMethods: {
        type: Function,
        default: () => () => {
        },
    },
});

const emit = defineEmits(["updateLoading"]);

const captcha = shallowRef<any>(null);
const ticket = shallowRef("");
const randstr = shallowRef("");

const codeLoading = shallowRef(false);
const message = useMessage();
const resetCounter = 60;

const {counter, pause, reset, resume, isActive} = useInterval(1000, {
    controls: true,
    immediate: false,
    callback(count: number) {
        if (count) {
            if (count === resetCounter) pause();
        }
    },
});

watch(
    () => codeLoading.value,
    (val: boolean) => {
        emit("updateLoading", val);
    }
);

async function getCode() {
    codeLoading.value = true;
    try {
        props.sendValidMethods && (await props.sendValidMethods());

        // 准备短信发送参数
        const sendSmsParams: sendPublicSmsParams = {
            phone: props.phone,
            type: SMS_TYPE.LOGIN_CODE, // 登录验证码类型
            captcha: ticket.value,
            randstr: randstr.value,
        };

        // 发送短信接口
        const res = await sendPublicSmsApi(sendSmsParams);
        if (res.success) {
            reset();
            resume();
            codeLoading.value = false;
            message.success("短信发送成功");
        } else {
            throw new Error(res.message || "发送失败");
        }
    } catch (error) {
        codeLoading.value = false;
        message.error("短信发送失败，请稍后再试");
    }
}

async function showTencentCaptcha() {
    try {
      const isValid = await props.sendValidMethods();
      console.log(isValid)
      if (!isValid) {
        codeLoading.value = false;
        return;
      }

        // 验证手机号通过后，显示腾讯云验证码
        // 引入腾讯云验证码
        if (!window.TencentCaptcha) {
            const script = document.createElement("script");
            script.src = "https://turing.captcha.qcloud.com/TJCaptcha.js";
            document.body.appendChild(script);

            script.onload = () => {
                initTencentCaptcha();
            };
        } else {
            initTencentCaptcha();
        }
    } catch (e) {
        console.log(e, "手机号码不合法");
        message.error("手机号码不合法");
    }
}

function initTencentCaptcha() {
    // 初始化腾讯云验证码
    captcha.value = new window.TencentCaptcha(
        CaptchaInfo.AppId,
        (res: any) => {
            console.log(res);
            if (res.ret === 0) {
                // 验证成功
                ticket.value = res.ticket;
                randstr.value = res.randstr;
                // 验证成功后获取短信验证码
                getCode();
            }
        }
    );
    // 显示验证码
    captcha.value.show();
}

onMounted(() => {
    // 加载腾讯云验证码脚本
    const script = document.createElement("script");
    script.src = "https://turing.captcha.qcloud.com/TJCaptcha.js";
    document.body.appendChild(script);
});

// 使用expose暴露方法
defineExpose({
    getCode,
    showTencentCaptcha,
});
</script>

<template>
    <div inline @click="showTencentCaptcha">
        <slot
                :loading="codeLoading"
                :disabled="isActive"
                :reset-counter="resetCounter"
                :counter="counter"
        >
            <a-button :loading="codeLoading" :disabled="isActive" size="large" class="sms-code-button">
                <template v-if="!isActive"> 获取验证码</template>
                <template v-else> {{ resetCounter - counter }} 秒</template>
            </a-button>
        </slot>
    </div>
</template>

<style scoped lang="less">
.sms-code-button {
  width: 119px;
}
</style>
