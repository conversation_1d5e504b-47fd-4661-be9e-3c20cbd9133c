<script setup lang="ts">
import Logo from "/logo.png";
import {useRechargeDialog} from "~/hooks/useRechargeDialog.tsx";

import TicketIcon from "@/Icons/ticket.svg?component";
import WalletIcon from "@/Icons/wallet.svg?component";
import SettingIcon from "@/Icons/setting.svg?component";
import UserIcon from "@/Icons/user.svg?component";
import VerifyIcon from "@/Icons/verify.svg?component";
import LogInIcon from "@/Icons/log-in.svg?component";
import HelpIcon from "@/Icons/help.svg?component";
import promotionIcon from "@/Icons/promotion.svg?component";

import { formatAmount, maskPhone } from '@/utils/common-utils';



const {rechargeModal} = useRechargeDialog()
const message = useMessage();
const userStore = useUserStore();
const layoutMenuStore = useLayoutMenu();
const router = useRouter();
const { avatar, nickname, userInfo } = storeToRefs(userStore);

async function logout() {
  const hide = message.loading("退出登录...", 0);
  try {
    await userStore.logout();
  } finally {
    hide();
    message.success("退出登录成功", 3);
    router
      .push({
        path: "/login",
      })
      .then(() => {
        layoutMenuStore.clear();
      });
  }
}

const recharge =  ()=>{
  rechargeModal(() => {
    userStore.getUserInfo()
  })
}

function goPersonal() {
  router.push("/dashboard/user");
}

function goSafetyManage() {
  router.push("/safety-manage/log");
}
function goAuth() {
  router.push("/dashboard/auth");
}
function goHelp() {
  window.open('https://www.yuque.com/zhaojie/kb/gqgy9bkz2x7dih7f?singleDoc', '_blank');
}
</script>

<template>
  <a-dropdown>
    <div class="h-60px flex items-center">
      <span
        hover="color-primary"
        flex
        items-center
        h-48px
        px-12px
        cursor-pointer
        class="transition-all-300"
      >
        <a-avatar
          :src="avatar || Logo"
          mr-8px
          size="small"
          class="w-34px h-34px"
        />
        <span class="anticon">{{ nickname }}</span>
      </span>
    </div>

    <template #overlay>
      <div class="user-avatar-modal">
        <div class="flex items-center">
          <a-avatar :src="avatar || Logo" mr-12px w-42px h-42px />
          <div>
            <div class="text-14px line-height-14px">
              {{ nickname || "/" }}
            </div>
            <div class="text-14px mt-8px line-height-14px color-[#999999]">
              账号ID：{{ maskPhone(userInfo.phone) || '-' }}
            </div>
          </div>
        </div>

        <div class="balance p-24px mt-20px">
          <div class="relative flex justify-between items-center h-full">
            <div class="flex gap-16">
              <div>
                <div class="text-14px">余额</div>
                <div>
                  <span class="text-12px">¥</span>
                  <span class="text-24px">{{ formatAmount(userInfo?.balance || 0) }}元</span>
                </div>
              </div>
            </div>

            <a-button class="btn" @click="recharge"> 充值 </a-button>
          </div>
        </div>

        <div class="options">
          <ul class="flex items-centent">
            <li @click="router.push('/dashboard/coupons')">
              <TicketIcon ml-4px />
              <span>优惠券管理</span>
              <span class="ticket-tag">{{ userInfo?.couponCount ? userInfo?.couponCount : 0 }}张可用</span>
            </li>
            <li @click="router.push('/cost/order')">
              <WalletIcon ml-4px />
              <span>费用管理</span>
            </li>
          </ul>
          <a-divider class="mt-7px! mb-7px!"  />
          <ul class="flex items-centent">
            <li @click="goSafetyManage">
              <SettingIcon ml-4px />
              <span>安全管理</span>
            </li>
            <li @click="goPersonal">
              <UserIcon ml-4px />
              <span>个人中心</span>
            </li>
          </ul>
          <a-divider class="mt-7px! mb-7px!" />
          <ul class="flex items-centent">
            <li @click="goAuth">
              <VerifyIcon ml-4px />
              <span>实名认证</span>
              <span class="ticket-tag verify-tag" v-if="userInfo.isRealName">已认证</span>
              <span class="ticket-tag" v-else-if="!userInfo.isRealName">未认证</span>
            </li>
            <li @click="goHelp">
              <HelpIcon ml-4px />
              <span>使用帮助</span>
            </li>
          </ul>
          <a-divider class="mt-7px! mb-7px!" />
          <ul class="flex items-centent">
             <li @click="router.push('/dashboard/my-promotion')">
              <promotionIcon ml-4px />
              <span>我的推广</span>
            </li>
            <li @click="logout">
              <LogInIcon ml-4px />
              <span>退出登录</span>
            </li>
          </ul>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<style lang="less" scoped>
.user-avatar-modal {
  width: 320px;
  position: relative;
  padding: 20px;
  background: #fff;
  box-shadow: var(--c-shadow);
   border-radius: 12px;
  .balance {
    width: 280px;
    height: 72px;
    background: url("@/assets/images/common/bg.png") no-repeat;
    background-size: cover;
    border-radius: 12px;
    .btn {
      width: 78px;
      height: 24px;
      background: linear-gradient(
        135deg,
        #e94e74 0%,
        #9b0fff 63%,
        #4d54ff 100%
      );
      border-radius: 20px 20px 20px 20px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      border: none;
      line-height: 24px;
      padding: 0;
    }
  }
}

.options {
  margin-top: 10px;

  & > ul {
    width: 100%;
    margin: 0;
    padding: 0;

    li {
      list-style: none;
      padding: 10px 0;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      span {
        margin-left: 8px;
      }

      &:hover {
        color: var(--cloud-color-primary);
      }
      .ticket-tag {
        height: 18px;
        line-height: 18px;
        background: linear-gradient(303deg, #ff333b 0%, #ff6b60 100%);
        box-shadow: 0px 2px 5px 0px rgba(255, 78, 78, 0.3);
        border-radius: 318px;
        font-weight: 400;
        font-size: 10px;
        color: #ffffff;
        text-align: center;
        margin-left: 4px;
        padding: 0 6px;
      }
      .verify-tag {
        background: linear-gradient(303deg, #1687ff 0%, #4da9ff 100%);
      }
      &:first-child {
        width: 170px;
      }
    }
  }
}
</style>
