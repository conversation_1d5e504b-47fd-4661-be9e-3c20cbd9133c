<script setup lang="ts">
const props = defineProps(['logo', 'title', 'desc', 'hiddenDesc', 'tipShow'])
const titleClass = computed(() => {
    return props.hiddenDesc ? 'two-ellipsis' : ''
})
</script>

<template>
    <div w-full inline-block flex-shrink-3>
        <div class="wrap-info flex items-center">
            <div class="flex-shink-0 mr-12px">
                <img class="logo" :src="logo">
            </div>
            <div w-0 flex-1>
                <template v-if="tipShow">
                    <a-tooltip :title="title">
                        <div class="ellipsis" :class="titleClass">
                            {{ title }}
                        </div>
                    </a-tooltip>
                </template>
                <template v-else>
                    <div class="ellipsis" :class="titleClass">
                        {{ title }}
                    </div>
                </template>
                <div v-if="!hiddenDesc">
                    <slot name="desc">
                        {{ desc }}
                    </slot>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="less">
.wrap-info {
  position: relative;
  width: 100%;

  .logo {
    width: 42px;
    height: 42px;
    object-fit: contain;
  }
}

.ellipsis {
  max-width: 300px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.two-ellipsis {
    white-space: pre-wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
}
</style>
