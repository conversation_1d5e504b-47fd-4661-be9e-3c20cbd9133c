<template>
  <div class="common-page">
    <div>
      <div class="page-title">{{title}}</div>
      <TCard class="mt-20px">
        <slot></slot>
      </TCard>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  title: {
    type: String,
    default: "个人中心"
  }
})
</script>

<style scoped lang='less'>
.common-page {
  .page-title {
    font-weight: 500;
    font-size: 20px;
    color: #3d3d3d;
    line-height: 28px;
    &::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 15px;
      background: #08c18a;
      border-radius: 278px 278px 278px 278px;
      margin-right: 6px;
    }
  }
  :deep(.ant-card-body) {
    padding: 0;
  }
}
</style>
