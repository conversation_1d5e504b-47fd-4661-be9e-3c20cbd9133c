<script setup lang="ts">
import WarningIcon from '@/Icons/warning.svg'

const props = defineProps({
    title: {
        type: String,
        default: '标题',
    },
    width: {
        type: [Number, String],
        default: 480,
    },
    tips: {
        type: String,
        default: '',
    },
    content: {
        type: [String, document, HTMLElement],
        default: '',
    },
    centered: {
        type: Boolean,
        default: true,
    },
    okText: {
        type: String,
        default: '确定',
    },
    cancelText: {
        type: String,
        default: '取消',
    },
    isHideCancel: {
        type: Boolean,
        default: false,
    },
    btnRtl: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['ok', 'cancel'])
const openModal = ref(false)
const openParams = ref(null)

function closeModal() {
    openModal.value = false
    emit('cancel')
}

function showModal(params) {
    openParams.value = unref(params)
    openModal.value = true
}

function doConfirm() {
    emit('ok', unref(openParams))
}

defineExpose({
    closeModal,
    showModal,
})
</script>

<template>
    <a-modal v-model:open="openModal" :title="title" :centered="centered" destroy-on-close :width="width" :footer="null"
             @cancel="closeModal">
        <slot>
            <div v-if="content" v-html="content"/>
            <div v-else flex items-start min-h-44px pl-8px>
                <WarningIcon flex-shrink-0 text-24px w-24px mr-8px/>
                <div text-16px w-full v-html="tips"/>
            </div>
        </slot>
        <div class="mt-24px text-right mr-[-16px]" :class="{ 'btn-rtl': btnRtl }">
            <a-button class="btn" type="primary" w-88px @click="doConfirm">
                {{ okText }}
            </a-button>
            <a-button v-if="!isHideCancel" class="btn" w-88px @click="closeModal">
                {{ cancelText }}
            </a-button>
        </div>
    </a-modal>
</template>

<style scoped lang="less">
.btn {
  margin-right: 16px;
}

.btn-rtl {
  direction: rtl;
}
</style>
