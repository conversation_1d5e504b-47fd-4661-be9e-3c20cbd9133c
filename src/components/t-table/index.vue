<script setup lang="ts">
import {QuestionCircleOutlined} from "@ant-design/icons-vue";

function customHeaderCell(payload: any) {
    console.log("customHeaderCell", payload);
    return payload;
}
</script>

<template>
    <a-table
            :sticky="{ offsetHeader: 60 }"
            :custom-header-cell="customHeaderCell"
            :show-sorter-tooltip="false"
    >
        <template #headerCell="headerItem">
            <slot name="headerCell" v-bind="headerItem">
                <span>{{ headerItem?.title }}</span>
                <a-tooltip v-if="headerItem?.column?.tips">
                    <template #title>
                        <div v-html="headerItem?.column?.tips"/>
                    </template>
                    <QuestionCircleOutlined cursor-pointer ml-4px/>
                </a-tooltip>
            </slot>
        </template>
        <template #bodyCell="bodyItem">
            <slot name="bodyCell" v-bind="bodyItem"/>
        </template>
    </a-table>
</template>

<style scoped lang="less"></style>
