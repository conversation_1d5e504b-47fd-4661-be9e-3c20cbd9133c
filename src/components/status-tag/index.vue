<script setup lang="ts">
import {computed} from 'vue'

import { AllStatusMapping } from '~#/constant.ts';


interface StatusTagProps {
    status: string
    type: 'device' | 'remote' | 'custom'
    customColor?: string
    customText?: string
}

const props = withDefaults(defineProps<StatusTagProps>(), {
    status: '',
    type: 'device',
})

interface StatusConfig {
    text: string
    color: string
}

interface StatusMapping {
    [key: string]: StatusConfig | undefined
}

// 设备状态配置
const deviceStatusConfig: StatusMapping = {
    [AllStatusMapping.RUNNING]: {text: '运行中', color: '#08C18A'},
    [AllStatusMapping.STOPPED]: {text: '已关机', color: '#F53F3F'},
    [AllStatusMapping.STARTING]: {text: '开机中', color: '#FF7D00'},
    [AllStatusMapping.STOPPING]: {text: '关机中', color: '#FF7D00'},
    [AllStatusMapping.DEFAULT]: {text: '未知', color: '#d9d9d9'},
}

// 远程状态配置
const remoteStatusConfig: StatusMapping = {
    [AllStatusMapping.RUNNING]: {text: '运行中', color: '#08C18A'},
    [AllStatusMapping.ACTIVE]: {text: '远程中', color: '#08C18A'},
    [AllStatusMapping.IDLE]: {text: '空闲', color: '#86909C'},
    [AllStatusMapping.DEFAULT]: {text: '未知', color: '#d9d9d9'},
}

// 计算状态显示文本
const statusText = computed(() => {
    if (props.type === 'custom' && props.customText) {
        return props.customText
    }

    const config = props.type === 'device' ? deviceStatusConfig : remoteStatusConfig
    return config[props.status]?.text || config.default.text
})

// 计算状态颜色
const statusColor = computed(() => {
    if (props.type === 'custom' && props.customColor) {
        return props.customColor
    }

    const config = props.type === 'device' ? deviceStatusConfig : remoteStatusConfig
    return config[props.status]?.color || config.default.color
})
</script>

<template>
    <span>
        <a-badge :color="statusColor"/>
        <span class="tetx-14px">{{ statusText }}</span>
    </span>
</template>

<style scoped lang="less">
/* 可根据需要添加自定义样式 */
</style> 