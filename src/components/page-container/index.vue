<script setup lang="ts">
import {useLayoutState} from '~/layouts/basic-layout/context'

defineProps<{
    title?: string
}>()
defineSlots<{
    default: (props: any) => any
    title: (props: any) => any
    content: (props: any) => any
    extraContent: (props: any) => any
    extra: (props: any) => any
    footer: (props: any) => any
}>()

const {contentWidth} = useLayoutState()
const contentCls = computed(() => {
    const cls: string[] = [
        'flex flex-col flex-1',
    ]
    if (contentWidth.value === 'Fluid')
        cls.push('w-full')

    else if (contentWidth.value === 'Fixed')
        cls.push(...['max-w-1200px w-1200px', 'mx-auto'])

    return cls
})
</script>

<template>
    <div class="ant-pro-page-container">
        <div :class="contentCls">
            <slot/>
        </div>
    </div>
</template>
