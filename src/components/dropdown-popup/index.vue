<script setup lang="ts">
import {CloseOutlined} from '@ant-design/icons-vue'

const props = defineProps(['title', 'width', 'contentStyle'])
const emit = defineEmits(['close'])
</script>

<template>
    <div :style="{ width: `${width}px` }" relative shadow>
        <div v-if="title" class="header w-full bg-#FBFBFB flex flex-between px-20px">
            <span>{{ title }}</span>
        </div>
        <div absolute top-14px right-14px z-999>
            <CloseOutlined @click="emit('close')"/>
        </div>
        <div class="content w-full h-max bg-#fff" :contentStyle="contentStyle">
            <slot/>
        </div>
    </div>
</template>

<style scoped lang="less">
.header {
  border-radius: 8px 8px 0 0;
  min-height: 50px;
  line-height: 50px;
}

.content {
  border-radius: 0 0 8px 8px;
}
</style>
