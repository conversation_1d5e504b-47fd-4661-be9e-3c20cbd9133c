<template>
  <qrcode-vue
      :value="props.value"
      :level="props.level"
      :render-as="props.renderAs"
      :background="props.background"
      :foreground="props.foreground"
      :gradient="props.gradient"
      :gradient-type="props.gradientType"
      :gradient-start-color="props.gradientStartColor"
      :gradient-end-color="props.gradientEndColor"
      :image-settings="props.imageSettings"
      :size="props.size"
  />
</template>
<script setup lang="ts">
import QrcodeVue from 'qrcode.vue'
import type {Level, RenderAs, GradientType, ImageSettings} from 'qrcode.vue'

const props = withDefaults(defineProps<{
  value: string
  level?: Level
  renderAs?: RenderAs
  background?: string
  foreground?: string
  margin?: number
  size?: number
  imageSettings?: ImageSettings
  gradient?: boolean
  gradientType?: GradientType
  gradientStartColor?: string
  gradientEndColor?: string
}>(), {
  value: '',
  level: 'M',
  renderAs: 'svg',
  background: '#ffffff',
  foreground: '#000000',
  margin: 0,
  size: 140,
  imageSettings: () => ({
    src: '',
    width: 16,
    height: 16,
    excavate: true,
  }),
  gradient: false,
  gradientType: 'linear',
  gradientStartColor: '#000000',
  gradientEndColor: '#38bdf8'
})
</script>
