<script setup lang="ts">
import WarningIcon from '@/Icons/warning.svg?component'

withDefaults(defineProps<{
    tip?: string,
    iconClass?: string,
    wFull?: boolean,
    hideUpdateBtn?: boolean,
    hideBg?: boolean,
    hideIcon?: boolean
}>(), {
    tip: '以下数据结果仅作为示例进行查看，如需查看真实数据，',
    hideUpdateBtn: false,
    hideIcon: true,
    iconClass: '',
    hideBg: false,
    wFull: true,
})
</script>

<template>
    <div class="waring-tips" :class="{ 'no-bg': hideBg, 'w-max-content': !wFull }">
        <WarningIcon v-if="!hideIcon" class="w-16px h-16px text-16px mr-4px color-#FF8503" :class="iconClass"/>
        <slot>
          <span class="line-height-12px text-12px">{{ tip }}
            <router-link v-if="!hideUpdateBtn" to="/buyVip" target="_blank">
              <span class="color-primary cursor-pointer">请购买或升级会员</span>
            </router-link>
          </span>
        </slot>
    </div>
</template>

<style scoped lang="less">
.waring-tips {
  width: 100%;
  height: max-content;
  padding: 8px 10px;
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;

  &.w-max-content {
    width: max-content;
  }

  &.no-bg {
    border: none;
    background: transparent;
    border-radius: 0;
    padding: 0;
  }
}
</style>
