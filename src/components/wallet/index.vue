<script setup lang="ts">
import {PayType, createOrderParams, createOrder, createPayOrder, payResult, checkOrder} from "~/api/common/product.ts";
import {useAuthentication} from "~/hooks/useAuthentication.ts";
import {formatAmount} from "~/utils/common-utils.ts";
import {usePayDialog} from "~/hooks/usePayDialog.tsx";
import {useRechargeDialog} from "~/hooks/useRechargeDialog.tsx";

const {showAuthenticationModal} = useAuthentication()
const {showPayModal, payTypeList} = usePayDialog()
const {rechargeModal} = useRechargeDialog()

// 支付方式
const payType = ref(PayType.balance)
const router = useRouter()
const message = useMessage();

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

// 充值方法
const handleRecharge = () => {
  rechargeModal(() => {
    userStore.getUserInfo()
  })
}

const abortController = new AbortController();

const checkPayStatus = async (orderNo: string, maxAttempts = 50, interval = 3000) => {
  let attempts = 0;

  const check = async () => {
    if (abortController.signal.aborted) {
      return false;
    }

    if (attempts >= maxAttempts) {
      abortController.abort();
      message.error('支付超时，请在订单管理中查看支付状态');
      return false;
    }

    try {
      const res = await checkOrder(orderNo);
      if (res.success && res.payload) {
        return true;
      }
      attempts++;
      await new Promise(resolve => setTimeout(resolve, interval));
      return check();
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return false;
      }
      console.error('检查支付状态失败:', error);
      return false;
    }
  };

  return check();
};

const handlePay = async (params: createOrderParams) => {
  const {success, payload , code, message:msg} = await createOrder({payType: payType.value, ...params})
  if (success) {
    const data = await createPayOrder<payResult>(payload)
    if (data.success) {
      //余额支付
      if (payType.value === PayType.balance) {
        const paySuccess = await checkPayStatus(data.payload.orderNo);
        if (paySuccess){
          message.success('订单支付成功，请等待服务器发配')
          await userStore.getUserInfo()
          if (!userInfo.value.isRealName) {
            showAuthenticationModal()
            return
          } else {
            setTimeout(async () => {
              router.push('/cloudDesktop')
            }, 1500)
          }
        }
      } else {
        //微信/支付宝二维码支付
        showPayModal(data.payload,payType.value,async ()=>{
          await userStore.getUserInfo()
          router.push('/cloudDesktop')
        })
      }
    }
  } else {
    if(code === 513) {
      message.error('云电脑无资源可用，请稍后再试')
    } else {
      message.error(msg || '订单创建失败')
    }
  }
}

defineExpose({
  handlePay
})

</script>

<template>

  <div class="flex items-center gap-x-4 mb-4">
    <div class="text-#4E5969">支付方式：</div>

    <div class=" p-x-5 w-32.5 h-8 rounded-1.5 flex-center border border-solid  cursor-pointer select-none gap-2"
         v-for="(item,index) in payTypeList" :key="index"
         :class="payType===item.id ? 'bg-#F1F6FF border-[#08C18A] text-[#08C18A]' : 'border-[#E0E0E0] bg-#F7F8F9'"
         @click="payType = item.id">
      <img :src="item.icon" class="w-4 h-4" alt="">
      <span v-text="item.name"/>
    </div>
  </div>

  <div class="mb-4">
    <div class="flex items-center ml-20 mb-4">
      <div class="font-#3D3D3D">当前余额为：</div>
      <div class="text-#F53F3F  text-4">¥{{ formatAmount(userInfo.balance) }}</div>
      <a-button type="link" size="small" @click="handleRecharge" class="text-#165DFF fw-600 px-2">
        立即充值>
      </a-button>
    </div>

    <div class="text-#86909C">
      请在订单生成的5分钟内完成支付，超时未支付系统将自动取消订单，若订单中使用优惠券或余额，订单取消后会自动退回您的账户。
    </div>
  </div>

</template>

<style scoped lang="less">

</style>
