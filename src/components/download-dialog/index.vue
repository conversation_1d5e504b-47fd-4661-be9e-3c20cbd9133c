<template>
  <a-dropdown placement="bottomRight" >
    <div class="h-60px flex items-center">
      <a-button type="primary" class="download-btn">
        客户端下载
        <template #icon>
          <DownloadIcon class="mr-4px icon" />
        </template>
      </a-button>
    </div>

    <template #overlay>
      <div class="dropdown-conter">
        <paltform />
      </div>
      
    </template>
  </a-dropdown>
</template>

<script setup>
import DownloadIcon from "@/Icons/download.svg?component";
import paltform from "../paltform/index.vue";

</script>

<style scoped lang='less'>
.download-btn {
  width: 112px;
  height: 32px;
  background: #f8f8f8;
  border-radius: 18px 18px 18px 18px;
  font-size: 14px;
  color: #333333;
  line-height: 14px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  .icon {
    color: #08c18a;
  }
  &:hover {
    .icon {
      color: #ffffff;
    }
  }
}
.dropdown-conter{
   width: 591px;
  height: 184px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.09);
  border-radius: 12px;
  padding: 30px 36px;
}
</style>
