<script setup lang="ts">
import {UserCoupon} from "~/api/common/user.ts";
import dayjs from "dayjs";
import {getAssetsByName} from "~/utils/common-utils.ts";

const props = defineProps({
  coupon: {
    type: Object as () => UserCoupon,
    required: true
  },
  canClick: {
    type: Boolean,
    default: false
  },
  currentCouponId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits<{
  (e: 'useCoupon', coupon: UserCoupon): void
}>()

const {type, subtract, name, startDate, endDate, description} = props.coupon

const handleUseCoupon = (coupon: UserCoupon) => {
  if (props.canClick) {
    emit('useCoupon', coupon)
  }
}

const computedBg = computed(() => {
  if (type === 2) return 'bg-#ff928f'
  if (type === 1) return 'bg-#5bbafd'
})
const computedColor = computed(() => {
  if (type === 2) return 'gradient-text'
  if (type === 1) return 'text-white'
})

</script>

<template>
  <div class="coupons flex rounded-1.2 select-none cursor-pointer" @click="()=>handleUseCoupon(coupon)">
    <div class="w-24 h-22 flex-center pos-relative rounded-1.2 "
         :style="`background: url(${getAssetsByName(`coupons_${type}.webp`)}) no-repeat;background-size: cover;`">
      <div class="pos-absolute left-0 top-0 text-2.5 text-white w-12 h-4 flex-center rounded-tl-1.2 rounded-br-3"
           :class="computedBg">
        <span v-if="type===1">满减券</span>
        <span v-if="type===2">代金券</span>
      </div>
      <div class="text-white" :class="computedColor">
        <span class="fw-600">￥</span>
        <span v-text="subtract/100" class="text-9.5 fw-600 ml--1"/>
      </div>
    </div>

    <div class="flex-1 m-2 flex justify-center flex-col space-y-0.5">
      <span class="text-#3D3D3D" v-text="name"/>
      <div class="text-3">
        <span class="text-#C8C9CC">使用时间：</span>
        <span class="text-#646566"
              v-text="dayjs(startDate).format('YYYY-MM-DD') + '-' + dayjs(endDate).format('YYYY-MM-DD')"/>
      </div>
      <div class="text-3">
        <span class="text-#C8C9CC">使用范围：</span>
        <span class="text-#646566" v-text="description"/>
      </div>
    </div>

    <div v-if="currentCouponId == coupon.id" class="pos-absolute right-2px bottom-0 w-40px h-38px">
      <img src="@/assets/images/common/selected.webp" alt="" class="w-40px h-38px"/>
    </div>
    <div v-else class="flex flex-col w-30px p-x-1.5 justify-center  border-l-dotted text-3 border-[#dbdbe2] cursor-pointer text-#F66351">立即使用
    </div>
  </div>
</template>

<style scoped lang="less">


.coupons {
  width: 345px;
  height: 88px;
  background: url("@/assets/images/coupons_bg.webp") no-repeat;
  background-size: contain;
  position: relative;
  .gradient-text {
    /* 创建渐变背景 */
    background: linear-gradient(90deg, #FFFEFF 50%, #FFC9A6 100%);

    /* 使背景裁剪到文字形状 */
    -webkit-background-clip: text;
    background-clip: text;

    /* 使文字本身透明,显示背景 */
    -webkit-text-fill-color: transparent;
  }

}
</style>
