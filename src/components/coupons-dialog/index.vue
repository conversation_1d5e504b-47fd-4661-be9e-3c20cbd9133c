<script setup lang="ts">
import Coupon from "./coupon.vue";
import {getUserCoupon, UserCoupon} from "~/api/common/user.ts";

const props = defineProps({
  productId: {
    type: Number,
    default: undefined
  },
  currentCouponId: {
    type: Number,
    default: null
  }
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 6,
  total: 0
});

// 优惠券数据
const couponList = ref<UserCoupon[]>([]);
const copyCouponList = ref<UserCoupon[]>([]);

// 对话框可见性
const visible = ref(false);

// 打开对话框
const open = () => {
  visible.value = true;
  if (props.productId){
    fetchCouponList(props.productId)
  }
};

// 关闭对话框
const close = () => {
  visible.value = false;
};

const fetchCouponList = async (productId:number) => {
  const {success, payload} = await getUserCoupon(productId)
  if (success) {
    couponList.value = payload
    copyCouponList.value = payload.slice(0, pagination.pageSize)
    pagination.total = payload.length
  }
}


const emit = defineEmits<{
  (e: 'useCoupon', coupon: UserCoupon): void
}>()

const handleUseCoupon = (coupon: UserCoupon) => {
  emit('useCoupon', coupon)
  close()
};

// 页码变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  const startIndex = (page - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;
  copyCouponList.value = couponList.value.slice(startIndex, endIndex);
};

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<template>
  <a-modal
      v-model:visible="visible"
      :title="'可用优惠券'"
      :footer="null"
      :width="800"
      :mask-closable="true"
      class="couponList-modal"
  >
    <!-- 优惠券列表 -->
    <div class="grid grid-cols-2 gap-4 p-2">
      <Coupon v-for="coupon in copyCouponList" :key="coupon.id" :coupon="coupon" :currentCouponId="currentCouponId"
              canClick @useCoupon="handleUseCoupon"/>
    </div>

    <!-- 分页 -->
    <div class="flex justify-end items-center mt-6">
      <div class="text-sm text-gray-500">共 {{ pagination.total }} 条</div>
      <a-pagination
          v-model:current="pagination.current"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :show-size-changer="false"
          @change="handlePageChange"
      >
      </a-pagination>
    </div>
  </a-modal>
</template>

<style lang="less">
.couponList-modal {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-modal-header {
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 500;
  }

  .ant-pagination-item-active {
    border: none;
    background: #e8f4ff;

    a {
      color: #1890ff;
    }
  }
}
</style>
