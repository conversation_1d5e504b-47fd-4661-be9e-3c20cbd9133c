<template>
  <div class="download-dialog p-12px">
    <div class="download-item" @click="handleClick('Windows')">
      <div class="flex items-center">
        <div>
          <img src="@/assets/images/common/windows.png" alt="" />
        </div>
        <div class="ml-12px">
          <div class="text-16px font-500">Windows</div>
          <div>1.0.0</div>
        </div>
      </div>
      <div class="download-tips">
        <img
          src="@/assets/images/dashboard/download.webp"
          alt=""
          class="w-18px mr-4px"
        />
        <span>点击下载</span>
      </div>
    </div>
    <div class="download-item mt-16px" @click="handleClick('MacOS')">
      <div class="flex items-center">
        <div>
          <img src="@/assets/images/common/macos.png" alt="" />
        </div>
        <div class="ml-12px">
          <div class="text-16px font-500">MacOS</div>
          <div>1.0.0<span class="text-10px color-[#999999]">（apple 芯片）</span></div>
        </div>
      </div>
      <div class="download-tips">
        <img
          src="@/assets/images/dashboard/download.webp"
          alt=""
          class="w-18px mr-4px"
        />
        <span>点击下载</span>
      </div>
    </div>
    <div class="download-item mt-16px" @click="handleClick('MacOS')">
      <div class="flex items-center">
        <div>
          <img src="@/assets/images/common/macos.png" alt="" />
        </div>
        <div class="ml-12px">
          <div class="text-16px font-500">MacOS</div>
          <div>1.0.0<span class="text-10px color-[#999999]">（intel 芯片）</span></div>
        </div>
      </div>
      <div class="download-tips">
        <img
          src="@/assets/images/dashboard/download.webp"
          alt=""
          class="w-18px mr-4px"
        />
        <span>点击下载</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import DownloadIcon from "@/Icons/download.svg?component";

const handleClick = (platform) => {
  useDownloadEvent(platform);
};
</script>

<style scoped lang='less'>
.download-btn {
  width: 112px;
  height: 32px;
  background: #f8f8f8;
  border-radius: 18px 18px 18px 18px;
  font-size: 14px;
  color: #333333;
  line-height: 14px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  .icon {
    color: #08c18a;
  }
  &:hover {
    .icon {
      color: #ffffff;
    }
  }
}
.download-dialog {
  width: 290px;
  height: 339px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.09);
  border-radius: 12px;
  .download-item {
    width: 266px;
    height: 89px;
    background: #ffffff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.09);
    border-radius: 12px;
    border: 1px solid #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    .download-tips {
      display: none;
    }
    &:hover {
      border: 1px solid #08c18a;
      .download-tips {
        display: block;
      }
    }
  }
}
</style>
