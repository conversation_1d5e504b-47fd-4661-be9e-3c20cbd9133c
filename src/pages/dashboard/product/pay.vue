<script setup lang="ts">
import {MinusOutlined, PlusOutlined, LeftOutlined} from '@ant-design/icons-vue'
import CouponsDialog from '@/components/coupons-dialog/index.vue'
import type {TableColumnsType} from "ant-design-vue";
import {message } from 'ant-design-vue';
import {
  getImageById, getMonthList,
  getProductPriceDetail,
  getSkuItemById, ImageRes, keyValueMap, MonthItem,
  ProductPriceDetail,
  SkuItem,
  getInstanceRegionListApi
} from "~/api/common/product.ts";
import Wallet from '@/components/wallet/index.vue'
import {formatAmount, getAssetsImgByName} from "~/utils/common-utils.ts";
import {instanceRenew} from "~/api/common/cloud-desktop.ts";
import type {InstanceRenew} from "~/types/cloud-desktop.ts";
import {getUserCoupon, UserCoupon} from "~/api/common/user.ts";
import DeleteIcon from '@/Icons/delete.svg';


const router = useRouter();
const walletRef = ref()

const route = useRoute()
const skuId = route.query.skuId
const logicIds = route.query.logicIds
const mirror = route.query.mirror // 添加获取 mirror 参数
const couponList =  ref<Array<UserCoupon>>([])

const chooseCouponRef = ref(null)

const isRenewal = ref<boolean>(false)
const isLoading = ref(false)

const renewalTableData = ref<InstanceRenew[]>([]);

const regionsList = ref<any[]>([])
const regionsValue = ref(null)
// 配置选项
const skuConfig = ref<SkuItem>({
  skuDefJson: {
    zoneName: '',
    region: '',
    zoneId: '',
    chargeType: []
  }
})

// 不适用平台
const unsupportedPlatforms: any[] = []

// 镜像类型
const imageTypeList = ['公共镜像']
const activeImageType = ref(0)

const imageId = ref('')
const activePlatform = ref('')

interface OperatingSystem {
  name: string;
  image: string;
  data: ImageRes[];
}

const operatingSystems = ref([
  {name: 'Windows', image: getAssetsImgByName('windows.webp'), data: []},
  {name: 'Linux', image: getAssetsImgByName('alma-linux.webp'), data: []},
  {name: 'CentOS', image: getAssetsImgByName('cent.webp'), data: []},
  {name: 'openSUSE', image: getAssetsImgByName('open-suse.webp'), data: []},
  {name: 'RockyLinux', image: getAssetsImgByName('rocky-linux.webp'), data: []}
])

// 付费类型和周期
const payType = ref(0)
const payTypeList = ['包月']
const duration = ref<number>(1)
const durationList = ref<Array<MonthItem>>([])

// 数量
const quantity = ref(1)

// 优惠信息
const coupon: UserCoupon = ref({})
const currentCouponId = ref<number | null>(null)

// 价格计算
const priceInfo = ref<ProductPriceDetail>({
  productId: 0,
  actualPrice: 0,
  couponPrice: 0,
  totalPrice: 0
})
// 协议同意
const agreedToTerms = ref(false)

const createOrderParams = () => {
  const baseParams: any = {
    duration:duration.value,
    productCount: quantity.value,
    productId: priceInfo.value.productId,
    additionalInfo: [],
    couponId: coupon.value.id || undefined,
    // regionName:regionsList.value.find((item: { name: string; value: string }) => item.value === regionsValue.value)?.name,
    // region: regionsValue.value,
  };

  // 根据场景添加不同的附加信息
  if (isRenewal.value) {
    baseParams.additionalInfo.push({
      key: 'logicIds',
      value: logicIds as string,
      region: regionsValue.value,
    });

  } else {
    baseParams.additionalInfo.push({
      key: 'imageId',
      value: imageId.value,

    });
    baseParams.additionalInfo.push({
      key: 'region',
      value: regionsValue.value,
    });
    baseParams.additionalInfo.push({
      key: 'regionName',
      value: regionsList.value.find((item: { name: string; value: string }) => item.value === regionsValue.value)?.name,
    });
  }
  console.log(111, baseParams)
  return baseParams;
};


// 支付方法
const handlePay = async () => {
  if(!agreedToTerms.value) {
    message.error('请先阅读并且同意协议')
    return
  }
  isLoading.value = true
  try {
    await walletRef.value.handlePay(createOrderParams());
  } finally {
    isLoading.value = false;
  }
}

const chooseCoupon = () => {
  chooseCouponRef.value?.open()
}

const handleQuantityChange = (e: any) => {
  const value = parseInt(e.target.value)
  if (isNaN(value)) {
    quantity.value = 1
  } else if (value < 1) {
    quantity.value = 1
  } else if (value > 20) {
    quantity.value = 20
  } else {
    quantity.value = value
  }
}

watch(() => quantity.value, (val, old) => {
  if (val !== old) {
    getProducePrice()
  }
})

const columns: TableColumnsType = [
  {
    title: '云电脑名称',
    dataIndex: 'desktopName',
    key: 'desktopName',
  },
  {
    title: '网络地址',
    dataIndex: 'networkAddress',
    key: 'networkAddress',
  },
  {
    title: '设备区域',
    dataIndex: 'zoneName',
    key: 'zoneName',
  },
  {
    title: '到期时间',
    dataIndex: 'expreTime',
    key: 'expreTime',
  },
  {
    title: '月卡价格',
    dataIndex: 'monthPrice',
    key: 'monthPrice',
    customRender: ({text}: { text: number }) => `¥${formatAmount(text)}/月`
  },
];

const changePlatform = (os: OperatingSystem) => {
  activePlatform.value = os.name
  imageId.value = os.data[0].imageId
}

const changeDuration = (item: keyValueMap) => {
  duration.value = item.value
  getProducePrice()
}

const imageList = computed(() => {
  const baseList = operatingSystems.value.find(item => item.name === activePlatform.value)?.data || []
  return [
    ...baseList.map(item => ({ label: item.imageName, value: item.imageId })),
    { label: '海外地区（等待上线）', value: 'overseas', disabled: true }
  ]
})

const getProducePrice = async () => {
  priceInfo.value = await getProductPriceDetail({
    itemId: skuId as string,
    duration: duration.value,
    productCount: quantity.value,
    productType: isRenewal.value ? 'RENEW_INSTANCE' : 'BUY_INSTANCE',
    couponId: coupon.value.id,
    productItemIds: logicIds ? logicIds as string : ''
  })
  if(priceInfo.value.productId){
    const {success, payload} = await getUserCoupon(priceInfo.value.productId)
    if (success){
      couponList.value = payload
    }
  }
}

const fetchSkuItemById = async (id: string) => {
  isLoading.value = true
  const {success, payload} = await getSkuItemById(id)
  if (success) {
    isLoading.value = false
    skuConfig.value = payload
    await getProducePrice()
  }
}

const fetchImageById = async (id: string, regionValue?: string) => {
  let params = {
    region: regionValue
  }
  const {success, payload} = await getImageById(id, params)
  if (success) {
    payload.forEach((item: SkuItem) => {
      operatingSystems.value.forEach(platform => {
        if (item.osType === platform.name) {
          platform.data.push(item)
        }
      })
    })

    // 查找第一个有数据的操作系统，设置 activePlatform 和 imageId
    const firstWithData = operatingSystems.value.find(platform => platform.data.length > 0)
    if (firstWithData) {
      // 如果存在 mirror 参数，直接匹配 imageId
      if (mirror) {
        const matchedImage = payload.find(item => item.imageId == mirror)
        if (matchedImage) {
          activePlatform.value = matchedImage.osType
          imageId.value = matchedImage.imageId
          return
        }
      }
      // 如果没有 mirror 参数或未匹配到，使用默认行为
      activePlatform.value = firstWithData.name
      imageId.value = firstWithData.data[0]?.imageId || ''
    }
  }
}

const fetchRenewInfo = async (logicIds: string[]) => {
  const {success, payload} = await instanceRenew(logicIds)
  if (success) {
    renewalTableData.value = payload
    await getProducePrice()
  }
}

const handleCouponSelected = async (data: UserCoupon) => {
  if(currentCouponId.value == data.id){
    handleDeleteCoupon()
    return
  }
  currentCouponId.value = data.id
  coupon.value = data
  await getProducePrice()
}

const fetchMonthList = async () => {
  const {success, payload} = await getMonthList()
  if (success) {
    durationList.value = payload
    duration.value = payload[0].value
  }
}

const handleBack = () => {
  if (route.query.logicIds) {
    router.push("/cloudDesktop");
  } else {
    router.push("/product");
  }
}

const getInstanceRegionList = async () => {
  regionsList.value = []
  const {success, payload} = await getInstanceRegionListApi()
  if (success && payload.length) {
    regionsList.value = payload[0].regions
    regionsValue.value = regionsList.value[0]?.value
    fetchImageById(skuId as string, regionsValue.value)
  }
}

const handleRegionsChange = ()=>{
  clearAllData()
  fetchImageById(skuId as string, regionsValue.value)
}

const clearAllData = () => {
  operatingSystems.value.forEach(item => {
    item.data = [];
  });
};

const handleDeleteCoupon = async () => {
  coupon.value = {}
  currentCouponId.value = null
  await getProducePrice()
}

onMounted(() => {
  fetchMonthList()
  if (skuId) {
    getInstanceRegionList()
    fetchSkuItemById(skuId as string)

  } else if (logicIds?.length) {
    isRenewal.value = true
    fetchRenewInfo(logicIds as string[])
  }
})

</script>

<template>
  <a-spin :spinning="isLoading">
    <div class="pay-container min-h-screen p-0">

      <!-- 头部导航 -->
      <div v-if="isRenewal" class="flex w-42 items-center p-4 gap-2 cursor-pointer">
        <div class="w-1.5 h-4 bg-#08C18A rounded-70"></div>
        <a-typography-title :level="4" class="!m-0">云电脑续费</a-typography-title>
      </div>

      <div v-else class="flex w-42 items-center p-4 gap-2 cursor-pointer">
        <div class="w-1.5 h-4 bg-#08C18A rounded-70"></div>
        <a-typography-title :level="4" class="!m-0">云电脑购买</a-typography-title>
      </div>

      <div class="px-5 pb-5 ">

        <div v-if="isRenewal" class="bg-white rounded-5 p-5 mb-4 space-y-2">
          <div>续费设备须知(请认真阅读购买须知，青虎云电脑提供的设备皆受国家网络监管，不提供一切翻墙服务请合法合规使用)
          </div>
          <div>1、设备服务续费后无法退换，频繁更换设备会有潜在安全风险。</div>
          <div>2、青虎云电脑提供的国内/外设备，均受国家网络部门监管，禁止访问国家明令禁止登录的敏感网站，不提供一切翻墙服务
          </div>
        </div>

        <div class="bg-white rounded-5 p-5 pos-relative">

          <div class="flex flex-col gap-2 mb-4" v-if="isRenewal">
            <div>
              <span>续费设备数：</span>
              <span class="text-[#08C18A]" v-text="renewalTableData.length"/>
            </div>

            <a-table :columns="columns" :data-source="renewalTableData" :pagination="false" row-key="id"/>
          </div>

          <template v-else>
            <div class="flex items-center gap-x-4 mb-4">
              <span class="text-#4E5969 ">配置参数：</span>
              <div class="bg-#F7F8F9 rounded-1.5 text-#3D3D3D w-37.5 h-8 flex-center gap-1">
                <img src="@/assets/images/product/cpu.webp" class="w-4.5 h-4.5" alt="">
                <span>CPU：{{ skuConfig.vcpu }}</span>
              </div>
              <div class="bg-#F7F8F9 rounded-1.5 text-#3D3D3D w-37.5 h-8 flex-center gap-1">
                <img src="@/assets/images/product/memory.webp" class="w-4.5 h-4.5" alt="">
                <span>内存：{{ skuConfig.memory }}</span>
              </div>
              <div class="bg-#F7F8F9 rounded-1.5 text-#3D3D3D w-37.5 h-8 flex-center gap-1">
                <img src="@/assets/images/product/disk.webp" class="w-4.5 h-4.5" alt="">
                <span>系统盘：{{ skuConfig.systemDisk }}</span>
              </div>
              <!-- <div class="bg-#F7F8F9 rounded-1.5 text-#3D3D3D w-37.5 h-8 flex-center gap-1">
                <img src="@/assets/images/product/location.webp" class="w-4.5 h-4.5" alt="">
                <span>地区：{{ skuConfig?.skuDefJson?.zoneName }}</span>
              </div> -->
            </div>

             <div class="flex items-center gap-x-4 mb-4">
                <span class="text-#4E5969">地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;区：</span>
                <a-select v-model:value="regionsValue" class="image-select w-95 h-8"
                          :options="regionsList.map(item => ({ label: item.name, value: item.value }))" @change="handleRegionsChange"/>
              </div>

            <div v-if="unsupportedPlatforms.length" class="text-#3D3D3D ml-20 mb-4">
              不适用平台：{{ unsupportedPlatforms.join('、') }}
            </div>

            <!--          <div class="flex items-center ml-20  w-120 h-9 p-x-4 bg-#FFF7EF rounded-1 text-#595654 gap-1 mb-4">-->
            <!--            <ExclamationCircleFilled class="text-#FF7D00"/>-->
            <!--            <span>温馨提示：该配与容联云崩三方平台，平台适配率90%~99%</span>-->
            <!--          </div>-->

            <div class="flex items-center gap-x-4 mb-4">
              <span class="text-#4E5969 ">镜像类型：</span>
              <div class="flex items-center gap-4">
                <div class="bg-#F7F8F9 w-30 h-8 rounded-1.5 flex-center cursor-pointer select-none"
                     v-for="(item,index) in imageTypeList" :key="index" v-text="item"
                     :class="activeImageType===index ? 'border border-solid border-[#08C18A] text-[#08C18A]' : ''"
                     @click="activeImageType = index"
                />
              </div>
            </div>

            <div class="flex ml-20 gap-5 mb-4">
              <template v-for="(os, index) in operatingSystems">
                <div v-if="os?.data.length"
                     :key="index" @click="changePlatform(os)"
                     class="cursor-pointer flex flex-col items-center rounded-1.5 p-4 w-22 h-20 select-none"
                     :class="activePlatform === os.name ? 'border border-solid border-[#08C18A] text-[#08C18A]' : 'border-transparent'">
                  <img :src="os.image" :alt="os.name" class="w-8 h-8 mb-1"/>
                  <span class="text-sm">{{ os.name }}</span>
                </div>
              </template>
            </div>

            <div class="flex ml-20 items-center gap-4 mb-4">
              <span class="text-#4E5969">选择镜像类型</span>
              <a-select v-model:value="imageId" class="image-select w-95 h-8"
                        :options="imageList" @change="(value) => {
                          if(value === 'overseas') {
                            message.info('海外地区镜像即将上线，敬请期待！')
                          }
                        }"/>
            </div>
          </template>

          <div class="flex items-center gap-x-4 mb-4">
            <div class="text-#4E5969">计费类型：</div>
            <div class="bg-#F7F8F9 w-18 h-8 rounded-1.5 flex-center cursor-pointer select-none"
                 v-for="(item,index) in payTypeList" :key="index" v-text="item"
                 :class="payType===index ? 'border border-solid border-[#08C18A] text-[#08C18A]' : ''"
                 @click="payType = index"
            />
          </div>

          <div class="flex items-center gap-x-4 mb-4">
            <div class="text-#4E5969">购买时长：</div>
            <div class="bg-#F7F8F9 p-x-5  h-8 rounded-1.5 flex-center cursor-pointer select-none"
                 v-for="(item) in durationList" :key="item.value" v-text="item.name"
                 :class="duration===item.value ? 'border border-solid border-[#08C18A] text-[#08C18A]' : ''"
                 @click="changeDuration(item)"
            />
          </div>

          <div class="flex ml-20 items-center gap-4 mb-4">
            <div class="flex-center">
              <img src="@/assets/images/product/calendar.webp" class="w-4 h-4" alt="">
              <span class="text-#3D3D3D">可用时长：</span>
              <span class="text-#08C18A">无限制</span>
            </div>

            <div class="flex-center">
              <img src="@/assets/images/product/clock.webp" class="w-4 h-4" alt="">
              <span class="text-#3D3D3D">可用时间段：</span>
              <span class="text-#08C18A">全天可用</span>
            </div>
          </div>

          <!--        计费方式说明-->
          <div class="bg-#FAFAFA rounded-2 text-#86909C text-3 w-160 px-3 py-2 ml-20 mb-4">
            <div class="mb-2">计费方式说明：</div>
            <ul class="list-disc pl-4 space-y-2">
              <li>
                购买时，如您【计费方式】选择【包月】，则购买的
                <span class="text-#FF7D00">云电脑即为【套餐机】</span>
              </li>
              <li>
                套餐机为先购买后使用模式在套餐有效期内可随意使用，
                <span class="text-#FF7D00">不另外收取费用</span>
              </li>
              <li>套餐机到期后将无法继续使用，如到期7天后仍未续费，云电脑将被
                <span class="text-#FF7D00">系统自动释放，数据不可找回，请及时续费。</span>
              </li>
            </ul>
          </div>


          <div  v-if="!isRenewal" class="flex items-center gap-x-4 mb-4">
            <div class="text-#4E5969">数量：</div>
            <div>
              <a-input-group compact class="flex items-center">
                <a-button
                    @click="quantity > 1 ? quantity-- : quantity = 1"
                    :disabled="quantity <= 1"
                    class="rounded-l"
                    type="default"
                >
                  <template #icon>
                    <MinusOutlined/>
                  </template>
                </a-button>
                <a-input
                    type="number"
                    v-model:value="quantity"
                    :min="1"
                    :max="20"
                    style="width: 100px; text-align: center"
                    class="rounded-none"
                    @change="handleQuantityChange"
                />
                <a-button
                    @click="quantity < 20 ? quantity++ : quantity = 20"
                    :disabled="quantity >= 20"
                    class="rounded-r"
                    type="default"
                >
                  <template #icon>
                    <PlusOutlined/>
                  </template>
                </a-button>
              </a-input-group>
            </div>
          </div>


          <div class="flex items-center gap-x-4 mb-4">
            <div class="text-#4E5969">优惠券：</div>
            <div class="flex items-center gap-1">
              <template v-if="coupon.id">
                <span>已选</span>
                <span class="text-#08C18A" v-text="coupon.subtract / 100"/> 元“{{ coupon.name }}”优惠券
                <img src="@/assets/images/product/choose_icon.webp" class="w-3 h-3 ml-1 cursor-pointer" @click="chooseCoupon" alt="">
                <span @click="handleDeleteCoupon" class="flex items-center text-#FA3D33 cursor-pointer ml-2px">
                  <DeleteIcon class="w-3 h-3 ml-1 cursor-pointer" />
                  <span class="ml-2px">取消</span>
                </span>
                
              </template>
              <template v-else>
                <span @click="chooseCoupon" class="text-#08C18A select-coupon">{{ couponList.length }} 张优惠券可用</span>
                <img src="@/assets/images/product/choose_icon.webp" class="w-3 h-3 ml-1 cursor-pointer" @click="chooseCoupon" alt="">
              </template>
            </div>
          </div>

          <Wallet ref="walletRef"/>

        </div>

        <div class="bg-white rounded-5 mt-5 p-5 ">
          <!-- 价格信息 -->
          <div class="w-50">
            <div class="flex flex-col  space-y-3">

              <div class="flex">
                <span class="text-#4E5969">订单金额：</span>
                <span>¥{{ formatAmount(priceInfo?.totalPrice) }}</span>
              </div>

              <div class="flex">
                <span class="text-#4E5969">优惠金额：</span>
                <span class="text-red-500 line-through">¥{{ formatAmount(priceInfo?.couponPrice) }}</span>
              </div>

              <div class="flex items-end">
                <span class="text-#4E5969">订单总额：</span>
                <div class="text-red-500">
                  <span class="mr-1">¥</span>
                  <span class="text-red-500 text-4xl font-600">{{ formatAmount(priceInfo?.actualPrice) }}</span>
                </div>
              </div>

            </div>
          </div>

          <!-- 协议确认 -->
          <div class="flex-end">
            <Checkbox v-model:checked="agreedToTerms">
              我同意电商云设备
              <a href="/workbench/service-agreement.html" target="_blank" class="text-#017AFF">《设备服务协议》</a>
              <a href="/workbench/purchase-notes.html" target="_blank" class="text-#017AFF">《设备购买须知》</a>
            </Checkbox>
            <div class="flex-end">
              <a-button type="text" class="w-30 h-14 mr-12px"
                      @click="handleBack">
                      <LeftOutlined />
               返回
              </a-button>
              <a-button type="primary" class="w-67 h-14 text-white text-5 button-primary rounded-2"
                      @click="handlePay">
              立即购买
            </a-button>
            </div>

          </div>

        </div>

      </div>

      <CouponsDialog ref="chooseCouponRef" @useCoupon="handleCouponSelected" :productId="priceInfo?.productId" :currentCouponId="currentCouponId"/>

    </div>
  </a-spin>
</template>

<style scoped lang="less">
.pay-container {
  background: linear-gradient(140deg, #eeeffb 0%, #F2F1FC 55%, #F6F8FD 100%);
}

</style>

<style>
.image-select .ant-select-selector {
  background: #F7F8FA !important;
  border: none !important;
}
.select-coupon{
  cursor: pointer;
}
</style>
