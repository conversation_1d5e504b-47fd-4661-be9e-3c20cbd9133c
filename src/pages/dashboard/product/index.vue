<script setup lang="ts">
import {SkuItem, getSkuList} from "~/api/common/product";
import {formatAmount, getAssetsImgByName} from "~/utils/common-utils.ts";


const router = useRouter()
const productList = ref<SkuItem>([])
const activeIndex = ref<number | null>(null)
const defaultIndex = ref<number | null>(null)

const toBuy = (id: Number) => {
  let path = `product/pay?skuId=${id}`
  router.push(path)
}

const fetchSkuList = async () => {
  const {success, payload} = await getSkuList()
  if (success) {
    productList.value = payload
    productList.value.map((item: SkuItem, index: number) => {
      if(item.memory == '8' && Number(item.vcpu) == 4){
        activeIndex.value = index
        defaultIndex.value = index
      }
    })
  }
}


onMounted(() => {
  fetchSkuList()
})

</script>

<template>
  <div class="product-page h-full p-5">

    <div class="text-center mt-64px">
      <div class="mb-4 font-600 text-6.5">
        <span class="gradient-text">解锁本地般的电脑体验，</span>
        <span class="text-#312020">告别传统硬件设备限制</span>
      </div>
      <div class="text-#3D3D3D text-sm fw-500 mb-4">
        购买须知：购买后无法退换，请按需购买；付款成功后1小时内，云电脑将完成分配并生效。
      </div>
    </div>

    <div class="flex justify-center items-center pt-70px">
      <div v-for="(product,index) in productList" :key="product.title"
          class=" bg-white  p-x-20px p-y-30px w-78  product-item rounded-16px" :class="{ active: activeIndex === index }">
          <div class="item-tag" v-if="product.memory == 8 && product.vcpu == 4">
            <img src="@/assets/images/common/hot.png" alt="">
            <span class="color-[#FFFC67]">80%</span>用户选择
          </div>

        <div class="flex gap-2 ">
          <img :src="getAssetsImgByName(`product_icon_${index as number+1}.webp`)" class="w-36px h-36px" alt="">
          <div class="text-#3D3D3D text-5 fw-500">{{ product.skuName }}</div>
        </div>

        <div class="text-#86909C mt-10px">{{ product.sepcRemark }}</div>

        <div class="mt-12px">
          <div class="flex items-center gap-2 h-44px border-b border-b-[#F6F6F6] border-b-solid">
            <span class="flex-1 text-#86909C">CPU：</span>
            <span class="flex-1">{{ product.vcpu }}核</span>
          </div>
          <div class="flex items-center gap-2 h-44px border-b border-b-[#F6F6F6] border-b-solid">
            <span class="flex-1 text-#86909C">内存：</span>
            <span class="flex-1">{{ product.memory }}G</span>
          </div>
          <div class="flex items-center gap-2 h-44px border-b border-b-[#F6F6F6] border-b-solid">
            <span class="flex-1 text-#86909C">系统盘：</span>
            <span class="flex-1">{{ product.systemDisk }}G</span>
          </div>

          <!-- <div class="flex mt-20px">
            <span class="p-x-10px h-20px text-12px color-[#ffffff] block text-center  rounded-2px"  style="background: linear-gradient( 270deg, #FF2D33 0%, #FF967C 100%);">
              特惠
            </span>
            <span class="ml-6px p-x-10px h-20px text-12px color-[#7C7A7A] block text-center  rounded-2px border-1px border-[#DBDBDB] border-solid">
              即将售罄
            </span>
          </div> -->
          <div>
            <img src="@/assets/images/common/tips.png" alt="" class="w-100px h-20px mt-10px "/>
          </div>

          <div class="flex items-center">
            <span class="text-red-400">活动价：¥ <span class="text-32px font-600">{{ formatAmount(product.asLow) }}</span>/月</span>
          
          </div>
          <div class="flex items-center">
            <span class="text-#666666-400">日常价：</span>
            <span class="text-sm line-through"  v-text="`¥${formatAmount(product.asOrig)}/月`"/>
          </div>
        </div>

        <a-button type="primary" class="w-full h-36px rounded-100px button-primary mt-12px" @click="toBuy(product.id)">
          立即购买
        </a-button>
      </div>
    </div>

  </div>
</template>

<style lang="less" scoped>
.product-page {
  background-image: url("@/assets/images/product/product_bg.webp");
  background-repeat: no-repeat;
  background-size: cover;

  .product-item {
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.08);
    margin-right: 26px;
    transition: transform 0.3s cubic-bezier(0.4,0,0.2,1);
    position: relative;
    border: 2px solid #fff;
    // &:hover {
    //   transform: translateY(-20px);
    //   border: 2px solid #08C18A;
    // }
    &:last-child {
      margin-right: 0;
    }
    .item-tag{
      height: 32px;
      background: #FA3D33;
      border-radius: 16px 16px 0px 16px;
      position: absolute;
      top: -16px;
      right: -2px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color:#fff;
      img{
        width: 16px;
        height: 16px;
      }
    }
  }
  .active {
      transform: translateY(-20px);
      border: 2px solid #08C18A;
  }
}

.gradient-text {
  /* 创建渐变背景 */
  background: linear-gradient(to right, #FF1E16 0%, #9E2AF6 55%, #163DFF 100%);

  /* 使背景裁剪到文字形状 */
  -webkit-background-clip: text;
  background-clip: text;

  /* 使文字本身透明,显示背景 */
  -webkit-text-fill-color: transparent;
}

.grid {
  grid-template-columns: repeat(auto-fit, minmax(310px, 1fr));
}
</style>
