<script setup lang="tsx">
import type {TableColumnsType} from 'ant-design-vue';
import {message} from 'ant-design-vue';
import dayjs from 'dayjs';
import {ReloadOutlined, SearchOutlined} from '@ant-design/icons-vue';
import {
  createPayOrder,
  getOrderList,
  OrderItem,
  OrderStatus,
  orderStatusMap, payResult,
  PayType,
  payTypeMap,
} from "~/api/common/product.ts";
import {usePayDialog} from "~/hooks/usePayDialog.tsx";
import {formatAmount} from "~/utils/common-utils.ts";

const {showPayModal} = usePayDialog()

const visible = ref(false)
const currentItem = ref<OrderItem>({})

// 筛选条件
const filterForm = reactive({
  dateRange: null as [dayjs.Dayjs, dayjs.Dayjs] | null,
  status: '' as '' | OrderStatus,
});

// 表格数据
const tableData = ref<Array<OrderItem>>([]);
// 表格加载状态
const loading = ref(false);
// 分页设置
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 200,
  showSizeChanger: false,
  showQuickJumper: false,
  showTotal: (total: number) => `共 ${total} 条`
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '充值订单号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 120,
  },
  {
    title: '订单类型',
    dataIndex: 'productType',
    key: 'productType',
    customRender: ()=>'余额充值'
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    key: 'orderStatus',
    customRender: ({text,}: { text: OrderStatus }) => {
      return (
          <span>{orderStatusMap[text]}</span>
      );
    }
  },
  {
    title: '充值金额',
    dataIndex: 'orderPrice',
    key: 'orderPrice',
    customRender: ({text}: { text: number }) => text ? `¥${formatAmount(text)}` : '/'
  },
  {
    title: '实付金额',
    dataIndex: 'actualPrice',
    key: 'actualPrice',
    customRender: ({text}: { text: number }) => text ? `¥${formatAmount(text)}` : '/'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '支付时间',
    dataIndex: 'payTime',
    key: 'payTime',
    customRender: ({text}: { text: string }) => text || '/'
  },
  {
    title: '支付方式',
    dataIndex: 'payType',
    key: 'payType',
    customRender: ({text}: { text: PayType }) => {
      return payTypeMap[text] || '/'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    customRender: ({record}: { record: OrderItem }) => (
        <div class="flex flex-col gap-1">
          <a-button type="link" onClick={() => handleViewDetail(record)}>详情</a-button>
          {record.orderStatus === OrderStatus.PENDING && (
              <a-button type="link" onClick={() => handlePay(record)}>支付</a-button>
          )}
        </div>
    )
  }
];

// 获取充值记录数据
const fetchOrderList = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    const res = await getOrderList({
      payTimeStart: filterForm.dateRange ? dayjs(filterForm.dateRange[0]).format('YYYY-MM-DD 00:00:00') : '',
      payTimeEnd: filterForm.dateRange ? dayjs(filterForm.dateRange[1]).format('YYYY-MM-DD 23:59:59') : '',
      status: filterForm.status || undefined,
      isBalance: 1,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    });
    if (res.success && res.payload) {
      tableData.value = res.payload;
      pagination.total = res.pageInfo.total;
    }
  } catch (error) {
    console.error('获取充值记录失败:', error);
    message.error('获取充值记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  fetchOrderList();
};

// 处理重置
const handleReset = () => {
  filterForm.dateRange = null;
  filterForm.status = undefined;
  pagination.pageNum = 1;
  fetchOrderList();
};

// 处理表格变化（分页、排序、筛选）
const handleTableChange = (pag: any) => {
  pagination.pageNum = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchOrderList();
};

// 处理支付
const handlePay = async (row: OrderItem) => {
  const data = await createPayOrder<payResult>(row.orderNo)
  if (data.success) {
    showPayModal(data.payload, row.payType, () => {
      handleReset()
    })
  }
};

// 处理查看详情
const handleViewDetail = (row: OrderItem) => {
  currentItem.value = row
  visible.value = true
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderList();
});
</script>

<template>
  <div class="recharge-record h-full p-0">
    <!-- 头部导航 -->
    <div class="flex w-42 items-center py-4 mx-5 gap-2 cursor-pointer">
      <div class="w-1.5 h-4 bg-#08C18A rounded-70"></div>
      <a-typography-title :level="4" class="!m-0">充值记录</a-typography-title>
    </div>

    <a-card class="shadow rounded-3 mx-5">

      <!-- 筛选条件 -->
      <a-form layout="inline" class="mb-6 ml-16px">
        <a-form-item label="支付日期">
          <a-range-picker
              v-model:value="filterForm.dateRange"
              format="YYYY-MM-DD"
              :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>

        <a-form-item label="订单状态">
          <a-select v-model:value="filterForm.status" style="width: 200px">
            <a-select-option value="">全部</a-select-option>
            <a-select-option v-for="(key,value) in orderStatusMap" :key="value" :value="value">
              {{ key }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined/>
              </template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <ReloadOutlined/>
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 表格 -->
      <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          :row-class-name="(_record, index) => (index % 2 === 1 ? 'bg-#fafbff' : null)"
          @change="handleTableChange"
      />
    </a-card>

    <a-modal title="充值订单详情" v-model:visible="visible" width="650px" :footer="null" class="recharge-detail-modal">

      <div class="flex flex-col gap-3 h-35">

        <div class="flex gap-10">
          <div class="flex gap-1">
            <span class="text-#86909C w-25">充值订单号：</span>
            <span class="text-#1D2129 w-50" v-text="currentItem.orderNo"/>
          </div>

          <div class="flex gap-1">
            <span class="text-#86909C w-25">订单类型：</span>
            <span class="text-#1D2129 w-50" >余额充值</span>
          </div>
        </div>

        <div class="flex gap-10">
          <div class="flex gap-1">
            <span class="text-#86909C w-25">订单状态：</span>
            <span class="text-#1D2129 w-50" v-text="orderStatusMap[currentItem.orderStatus]"/>
          </div>

          <div class="flex gap-1">
            <span class="text-#86909C w-25">创建时间：</span>
            <span class="text-#1D2129 w-50" v-text="currentItem.createTime"/>
          </div>
        </div>

        <div class="flex gap-10">
          <div class="flex gap-1">
            <span class="text-#86909C w-25">支付方式：</span>
            <span class="text-#1D2129 w-50" v-text="payTypeMap[currentItem.payType] || '/'"/>
          </div>

          <div class="flex gap-1">
            <span class="text-#86909C w-25">支付时间：</span>
            <span class="text-#1D2129 w-50" v-text="currentItem.payTime"/>
          </div>
        </div>

        <div class="flex gap-10">
          <div class="flex gap-1">
            <span class="text-#86909C w-25">充值金额：</span>
            <span class="text-#F53F3F w-50" v-text="currentItem.orderPrice? `￥${formatAmount(currentItem.orderPrice)}`  : '/'"/>
          </div>

          <div class="flex gap-1">
            <span class="text-#86909C w-25">实付金额：</span>
            <span class="text-#F53F3F w-50" v-text="currentItem.actualPrice? `￥${formatAmount(currentItem.actualPrice)}`  : '/'"/>
          </div>
        </div>
      </div>
    </a-modal>

  </div>
</template>


<style scoped lang="less">


</style>


<style lang="less">
.recharge-record {
  .ant-table-thead {
    .ant-table-cell {
      background: #f4f5fa;
    }
  }


  .ant-select-selector {
    background: #F7F8FA !important;
    border: none !important;

    input::placeholder {
      color: #86909C !important;
    }
  }

  .ant-picker-range {
    background: #F7F8FA !important;
    border: none !important;

    .ant-picker-input {
      input::placeholder {
        color: #86909C !important;
      }
    }

  }

}

</style>
