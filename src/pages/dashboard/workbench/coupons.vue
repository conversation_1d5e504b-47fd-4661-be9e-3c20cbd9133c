<script setup lang="ts">
const router = useRouter();

import Coupon from "~/components/coupons-dialog/coupon.vue";
import {getUserCoupon, UserCoupon} from "~/api/common/user.ts";

const activeType = ref('all')
const activeStatus = ref('available')

const statusList = [
  {name: '可使用', key: 'available'},
  {name: '已过期', key: 'expired'},
]

const changeStatus = (item: any) => {
  activeStatus.value = item.key
  filterCouponList.value = couponList.value.filter((item: UserCoupon) => {
    if (!item.isExpired && activeStatus.value === 'available') return true
    if (item.isExpired && activeStatus.value === 'expired') return true
  })
}
const couponList = ref<UserCoupon[]>([])
const filterCouponList = ref<UserCoupon[]>([])

const fetchCouponList = async () => {
  const {success, payload} = await getUserCoupon()
  if (success) {
    couponList.value = payload
    filterCouponList.value = payload
  }
}

const changeTab = (tab: any) => {
  if (tab === 'all') {
    filterCouponList.value = couponList.value
  } else {
    filterCouponList.value = couponList.value.filter((item: UserCoupon) => item.type === Number(tab))
  }
}

onMounted(() => {
  fetchCouponList()
})

</script>

<template>
  <div class="h-full p-0 ">

    <div class="flex w-42 items-center py-4 mx-5 gap-2 cursor-pointer">
      <div class="w-1.5 h-4 bg-#08C18A rounded-70"></div>
      <a-typography-title :level="4" class="!m-0">优惠券管理</a-typography-title>
    </div>

    <a-card class="shadow rounded-3 mx-3">

      <a-tabs v-model:activeKey="activeType" @change="changeTab">
        <a-tab-pane tab="全部" key="all">
          <div class="flex gap-3">
            <div class="w-20 h-8 rounded-25 flex-center border-#08C18A border-1px select-none cursor-pointer"
                 :class="activeStatus===item.key ? 'border-solid text-#08C18A bg-[rgba(8,193,138,0.1)]' : 'bg-#F2F3F5 text-#658D95'"
                 v-for="item in statusList" :key="item.key" v-text="item.name" @click="changeStatus(item)"/>
          </div>

          <div class="coupon-grid gap-3 py-4">
            <template v-if="filterCouponList.length">
              <Coupon
                v-for="coupon in filterCouponList"
                :key="coupon.id"
                :coupon="coupon"
                canClick="true"
                @useCoupon="router.push('/product')"
              />
            </template>
            <div v-else class="w-full py-10 flex-center">
              <a-empty description="暂无优惠券" />
            </div>
          </div>

        </a-tab-pane>
        <a-tab-pane tab="代金券" key="2">
          <div class="flex gap-3">
            <div class="w-20 h-8 rounded-25 flex-center border-#08C18A border-1px select-none cursor-pointer"
                 :class="activeStatus===item.key ? 'border-solid text-#08C18A bg-[rgba(8,193,138,0.1)]' : 'bg-#F2F3F5 text-#658D95'"
                 v-for="item in statusList" :key="item.key" v-text="item.name" @click="changeStatus(item)"/>
          </div>

          <div class="coupon-grid gap-3 py-4">
            <template v-if="filterCouponList.length">
              <Coupon v-for="coupon in filterCouponList" :key="coupon.id" :coupon="coupon"/>
            </template>
            <div v-else class="w-full py-10 flex-center">
              <a-empty description="暂无优惠券" />
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane tab="满减券" key="1">
          <div class="flex gap-3">
            <div class="w-20 h-8 rounded-25 flex-center border-#08C18A border-1px select-none cursor-pointer"
                 :class="activeStatus===item.key ? 'border-solid text-#08C18A bg-[rgba(8,193,138,0.1)]' : 'bg-#F2F3F5 text-#658D95'"
                 v-for="item in statusList" :key="item.key" v-text="item.name" @click="changeStatus(item)"/>
          </div>

          <div class="coupon-grid gap-3 py-4">
            <template v-if="filterCouponList.length">
              <Coupon v-for="coupon in filterCouponList" :key="coupon.id" :coupon="coupon"/>
            </template>
            <div v-else class="w-full py-10 flex-center">
              <a-empty description="暂无优惠券" />
            </div>
          </div>
        </a-tab-pane>

      </a-tabs>
    </a-card>

  </div>
</template>

<style scoped lang="less">
.coupon-grid {
  display: flex;
  flex-wrap: wrap;
}
</style>
