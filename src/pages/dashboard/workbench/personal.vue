<template>
  <div class="personal">
    <CommonPage title="个人中心">
      <div class="card-content">
        <div class="title">基本信息</div>
        <a-divider />
        <div class="flex items-center">
          <div class="label">手机号</div>
          <div class="text">
            <span>{{ maskPhone(userInfo.phone)}}</span>
            <router-link to="/dashboard/auth" class="auth-btn ml-22px">去认证&gt;</router-link>
          </div>
        </div>
        <a-divider />
        <div class="flex items-center">
          <div class="label">用户协议</div>
          <div class="text">我已阅读并同意<router-link to="/user-agreement.html" target="_blank">《用户协议》</router-link> 和 <router-link to="/privacy-policy-part.html" target="_blank">《隐私协议》</router-link></div>
        </div>
        <a-divider />
        <div class="flex" v-if="userInfo?.weChatQrUrl">
          <div class="label">专属客服</div>
          <div>
              <img :src="userInfo?.weChatQrUrl" alt="企业微信二维码" class="w-148px object-contain" />
          </div>
        </div>
      </div>
    </CommonPage>
  </div>
</template>

<script setup>
import CommonPage from "@/components/commom-page/index.vue";
import { maskPhone } from '@/utils/common-utils';
const userStore = useUserStore()
const {userInfo} = storeToRefs(userStore);

onMounted(() => {
  userStore.checkContactMe()
})

</script>

<style scoped lang='less'>
.personal {
  padding: 20px;

  .card-content {
    padding: 20px;
    .title {
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      line-height: 28px;
    }
    .label {
      width: 135px;
      padding-left: 16px;
    }
    .text {
      font-weight: 400;
      font-size: 14px;
      color: #969799;
      line-height: 16px;
    }
    .auth-btn {
      font-weight: 400;
      font-size: 14px;
      color: #08c18a;
      line-height: 16px;
    }
  }
}
</style>
