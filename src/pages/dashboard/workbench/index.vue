<script setup lang="ts">
import HomeHead from '~@/components/dashboard/home-head.vue'
import CloudDesktopList from '~/components/dashboard/cloud-desktop-list.vue'
import ProductRecommend from '~/components/dashboard/product-recommend.vue'
import FAQList from '~/components/dashboard/FAQList.vue'
import ContactMeModal from '~/components/dashboard/ContactMeModal.vue'


const userStore = useUserStore()
const { userInfo: userInfoData } = storeToRefs(userStore)
</script>

<template>
  <div class="w-full p-20px">
    <div>
      <div class="notification-bar flex items-center">
        <div class="indicator w-4px h-15px bg-green-500 rounded-sm mr-3"></div>
        <div class="color-[#1D2129] text-20px">首页</div>
        <template v-if="!userInfoData.isRealName">
            <a-divider type="vertical" />
            <div class="message color-[#86909C] text-14px flex-1">
                为了保证您的账户及资源安全，结果相关法律规定，请在使用青虎云电脑前完成实名认证。
                <router-link
                        to="/dashboard/auth"
                        class="verify-link text-green-500 text-sm hover:text-green-600"
                >
                    去认证 >
                </router-link>
            </div>
        </template>
      </div>
      <div class="mt-20px">
         <home-head />
      </div>
      <div class="mt-12px flex justify-between">
        <div class="flex-1">
          <cloud-desktop-list />
        </div>
        <div class="ml-12px w-298px">
            <product-recommend />
            <!-- <FAQList/> -->
        </div>
      </div>
    </div>
    
    <!-- 添加客服弹窗组件 -->
    <ContactMeModal />
  </div>
</template>

<style scoped lang="less">
</style>
