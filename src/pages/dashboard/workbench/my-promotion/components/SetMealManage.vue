<script setup lang="ts">
    import { Modal } from 'ant-design-vue';
    import { FormOutlined } from '@ant-design/icons-vue';
    import type { Dayjs } from 'dayjs';
    import dayjs from 'dayjs';
    import { useTablePagination, handleCopy } from "~/utils/common-utils.ts";
    import { appInfoEnum, qinghuSetMealListByCrm } from "~#/constant.ts";
    import { getSetMealManageListApi, createLinkApi, getLinkListApi, updateLinkNameApi, createPromotionCode } from '~/api/common/promotion';
    import QRCode from 'qrcode';

    import result_img from '@/assets/images/common/20250521-171122.png';

    const message = useMessage();
    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const isTableLoading = ref(false);
    const tableData = ref([]);
    const discountId = ref('');

    const isAddLinkModalOpen = ref(false);
    const isAddLinkModalLoading = ref(false);
    const addLinkForm = ref({
        linkName: ''
    });

    const isSeeLinkModalOpen = ref(false);
    const seeLinkQueryForm = ref({
        url: '',
        urlName: '',
        createTime: []
    });
    const seeLinkTableKey = ref(1);
    const isSeeLinkTableLoading = ref(false);
    const seeLinkTableData = ref([]);
    const seeLinkTablePagination = useTablePagination();

    onMounted(() => {
        getSetMealManageList();
    });

    const getSetMealManageList = async () => {
        try {
            isTableLoading.value = true;
            // isAdmin 是否管理员查询 1：普通用户，2：管理员
            let params = {
                isAdmin: 1,
                appId: appInfoEnum.appId,
                tenantId: appInfoEnum.tenantId,
                userId: userId?.value || null
            }
            let res = await getSetMealManageListApi(params);
            if (res.code == 200) {
                let _channelList = res.channelList || [];
                tableData.value = _channelList.map(v => {
                    v['costPriceData'] = {
                        // 后台设置的全部达秘套餐
                        allSetMealList: qinghuSetMealListByCrm,
                        // 成本价列表
                        costPriceList: !!v.costPrice ? JSON.parse(v.costPrice) : []
                    };
                    // 佣金比例列表
                    v['commissionRateList'] = !!v.stairBrokerageRate ? JSON.parse(v.stairBrokerageRate) : [];
                    return v;
                });
            }
        } catch (err) {
            console.error(err);
        } finally {
            isTableLoading.value = false;
        }
    }

    const getCostPrice = (data) => {
        let { allSetMealList, costPriceList } = data;
        let text = '';
        if (costPriceList.length && allSetMealList.length) {
            for (let cpItem of costPriceList) {
                for (let setMealItem of allSetMealList) {
                    if (String(cpItem.key) === String(setMealItem.id)) {
                        text = text + setMealItem.name + '：' + cpItem.value + '<br/>';
                    }
                }
            }
        } else {
            text = '/';
        }
        return text;
    }

    const setAddLinkModalShow = isFlag => {
        isAddLinkModalOpen.value = isFlag;
        if (!isFlag) {
            addLinkForm.value.linkName = '';
        }
    }
    const setSeeLinkModalShow = isFlag => {
        isSeeLinkModalOpen.value = isFlag;
        if (!isFlag) {
            discountId.value = '';
            seeLinkQueryForm.value = {
                url: '',
                urlName: '',
                createTime: []
            }
            isSeeLinkTableLoading.value = false;
            seeLinkTableData.value = [];
            seeLinkTablePagination.value.total = 0;
            seeLinkTableKey.value = 1;
        }
    }
    const handleModalOpen = (type, id) => {
        discountId.value = id;
        if (type === 'addLink') {
            setAddLinkModalShow(true);
        }
        else if (type === 'seeLink') {
            handleSeeLinkTableChange({
                current: 1,
                pageSize: 10
            });
            setSeeLinkModalShow(true);
        }
    }
    const handleModalClose = type => {
        if (type === 'addLink') {
            setAddLinkModalShow(true);
            Modal.confirm({
                title: '确认关闭？',
                icon: () => null,
                onOk: async () => {
                    setAddLinkModalShow(false);
                    Promise.reject();
                }
            });
        }
        else if (type === 'seeLink') {
            setSeeLinkModalShow(false);
        }
    }
    const handleAddLinkModalSave = async () => {
        if (!addLinkForm.value.linkName) {
            return message.warning('链接名称不能为空！');
        }
        try {
            isAddLinkModalLoading.value = true;
            let params = {
                appId: appInfoEnum.appId,
                tenantId: appInfoEnum.tenantId,
                userId: userId?.value || null,
                discountId: discountId.value,
                urlName: addLinkForm.value.linkName
            }
            let res = await createLinkApi(params);
            if (res.code == 200) {
                getLinkList();
                getSetMealManageList();
                setTimeout(() => {
                    setAddLinkModalShow(false);
                    message.success('新增的链接成功！');
                }, 300);
            } else if (res.code == 609) {
                message.error('新增的链接名称已存在！');
            } else {
                message.error('新增的链接名称失败！');
            }
        } catch (err) {
            console.error(err);
        } finally {
            isAddLinkModalLoading.value = false;
        }
    }

    // 获取链接列表
    const getLinkList = async () => {
        try {
            isSeeLinkTableLoading.value = true;
            let params = {
                appId: appInfoEnum.appId,
                userId: userId?.value || null,
                discountId: discountId.value,
                url: seeLinkQueryForm.value.url || null,
                urlName: seeLinkQueryForm.value.urlName || null,
                pageNum: seeLinkTablePagination.value.current,
                pageSize: seeLinkTablePagination.value.pageSize
            }
            let { createTime = [] } = seeLinkQueryForm.value;
            if (createTime.length > 0) {
                params['startTime'] = createTime[0];
                params['endTime'] = createTime[1];
            }
            let res = await getLinkListApi(params);
            if (res.code == 200) {
                let _discountUrlList = res.discountUrlList || [];
                let _total = Number(res?.Page?.total || 0);
                _discountUrlList = _discountUrlList.map(v => {
                    v['editFlag'] = false;
                    return v;
                });
                seeLinkTableData.value = _discountUrlList;
                seeLinkTablePagination.value.total = _total;
                seeLinkTableKey.value += 1;
            }
        } catch (err) {
            console.error(err);
        } finally {
            isSeeLinkTableLoading.value = false;
        }
    }

    const handleSeeLinkTableChange = pagination => {
        seeLinkTablePagination.value = {
            ...seeLinkTablePagination.value,
            ...pagination
        }
        getLinkList();
    }

    // 修改链接名称
    const updateLinkName = async data => {
        try {
            let params = {
                id: data.id,
                urlName: data.urlName
            }
            let res = await updateLinkNameApi(params);
            if (res.code == 200) {
                message.success('修改成功！');
                getLinkList();
            } else if (res.code == 609) {
                message.error('修改的名字存在重复！');
            } else {
                message.error('修改失败！');
            }
        } catch (err) {
            console.error(err);
        }
    }

    const  generateQR = async (url)=> {
        return QRCode.toDataURL(url)
    }

    const getImg = (url,flg)=> {
        if (flg == 1) {
          generateQR(url).then(res => {
            // 创建 canvas
            let canvas = document.createElement('canvas')
            canvas.id = 'my_canvas'
            canvas.width = 850
            canvas.height = 1065
            let ctx = canvas.getContext('2d')
            if (!ctx) return

            let img1 = new Image()
            let img2 = new Image()
            img1.crossOrigin = 'anonymous'
            img2.crossOrigin = 'anonymous'
            img1.src = result_img
            img2.src = res // 生成的二维码base64

            img1.onload = function() {
                ctx.drawImage(img1, 0, 0, 850, 1065)
                ctx.drawImage(img2, 75, 850, 208, 208)
                let oA = document.createElement('a')
                oA.download = ''
                oA.href = canvas.toDataURL('image/png')
                document.body.appendChild(oA)
                oA.click()
                oA.remove()
            }
          })
        } else if (flg == 2) {
        //   let queryList = (url.match(/\?.*$/g) || [''])[0].split('&');
        //   let urlCode = '';
        //   for (let i of queryList) {
        //     if (i.match(/^urlCode=/)) {
        //       urlCode = i.replace('urlCode=', '')
        //     }
        //   }
        //   if (urlCode) {
        //     createPromotionCode({urlCode}).then(res => {
        //       let canvas = document.getElementById('my_canvas2')
        //       let ctx = canvas.getContext('2d')
        //       let img1 = new Image()
        //       let img2 = new Image()
        //       img1.crossOrigin = 'anonymous'
        //       img2.crossOrigin = 'anonymous'
        //       img1.src = require('../../assets/images/WeChatAppletDistribution.jpg') // 背景图路经
        //       img2.src = res // 生成的二维码base64
        //       img1.onload = function() {
        //         ctx.drawImage(img1, 0, 0, 750, 1334) // 背景图载入画板
        //         ctx.save();
        //         ctx.beginPath();
		// 				    ctx.arc(190, 1215, 85, 0, Math.PI * 2, false);
		// 				    ctx.closePath();
		// 				    ctx.clip();           
        //         ctx.drawImage(img2, 110, 1135, 160, 160)
        //         ctx.restore()
        //         let oA = document.createElement('a')
        //         oA.download = '' // 设置下载的文件名，默认是'下载'
        //         oA.href = canvas.toDataURL('image/png')
        //         document.body.appendChild(oA)
        //         oA.click()
        //         oA.remove() // 下载之后把创建的元素删除
        //       }
        //     })
        //   } else {
        //     this.$message({
        //       message: '信息有误',
        //       type: 'error'
        //     })
        //   }
        }
    }
</script>

<template>
    <div class="set-meal-manage">
        <a-table 
            bordered 
            sticky
            :loading="isTableLoading" 
            :scroll="{ x: 'max-content' }"
            :data-source="tableData" 
            :pagination="false" 
        >
            <a-table-column fixed="left" align="center" key="discountName" title="推广类型" data-index="discountName" :width="140" />

            <a-table-column align="center" key="costPriceData" title="成本价" data-index="costPriceData" :width="220">
                <template #default="{ record }">
                    <div v-html="getCostPrice(record.costPriceData)"></div>
                </template>
            </a-table-column>

            <a-table-column align="center" key="commissionRateList" title="佣金比例" data-index="commissionRateList" :width="220">
                <template #default="{ record }">
                    <div v-if="record.commissionRateList.length == 1">
                        <div>
                            {{ record.commissionRateList[0].key }}
                            人以下
                            {{ record.commissionRateList[0].value }}%
                        </div>
                    </div>
                    <div v-else-if="record.commissionRateList.length == 2">
                        <div>
                            {{ record.commissionRateList[0].key }}
                            人以下
                            {{ record.commissionRateList[0].value }}%
                        </div>
                        <div>
                            {{ record.commissionRateList[1].condition }}
                            {{ record.commissionRateList[1].key }}
                            人
                            {{ record.commissionRateList[1].value }}%
                        </div>
                    </div>
                    <div v-else-if="record.commissionRateList.length == 3">
                        <div>
                            {{ record.commissionRateList[0].key }}
                            人以下
                            {{ record.commissionRateList[0].value }}%
                        </div>
                        <div>
                            {{ record.commissionRateList[1].condition }}
                            {{ record.commissionRateList[1].key }}
                            人
                            {{ record.commissionRateList[1].value }}%
                        </div>
                        <div>
                            {{ record.commissionRateList[2].condition }}
                            {{ record.commissionRateList[2].key }}
                            人
                            {{ record.commissionRateList[2].value }}%
                        </div>
                    </div>
                    <div v-else>/</div>
                </template>
            </a-table-column>

            <a-table-column align="center" key="orderCount" title="推广海报" data-index="orderCount" :width="120">
                <template #default="{ record }">
                    <div>
                        <a-button type="link" @click="getImg(record.romotionPoster, 1)">
                            下载PC海报
                        </a-button>
                        <!-- <a-button type="link">
                            小程序海报
                        </a-button> -->
                    </div>
                </template>
            </a-table-column>

            <a-table-column align="center" key="romotionPoster" title="推广链接" data-index="romotionPoster" :width="120">
                <template #default="{ record }">
                    <div class="single-overflow" style="width: 120px;">{{ record.romotionPoster }}</div>
                </template>
            </a-table-column>
            <a-table-column align="center" key="orderNum" title="累计订单数" data-index="orderNum" :width="110" />
            <a-table-column align="center" key="commission" title="累计佣金" data-index="commission" :width="100" />
            <a-table-column align="center" key="urlLimit" title="推广链接数" data-index="urlLimit" :width="110" />

            <a-table-column align="center" key="status" title="状态" data-index="status" :width="80">
                <template #default="{ record }">
                    <span style="color: #8c939d; " v-if="record.status === 0">有效</span>
                    <span style="color: #f5365c;" v-else>无效</span>
                </template>
            </a-table-column>

            <a-table-column align="center" title="相关操作" :width="240">
                <template #default="{ record }">
                    <a-button type="link" @click="handleModalOpen('addLink', record.discountId)">
                        新增链接
                    </a-button>
                    <a-divider type="vertical" />
                    <a-button v-if="record.urlLimit > 0" type="link" @click="handleModalOpen('seeLink', record.discountId)">
                        查看链接
                    </a-button>
                </template>
            </a-table-column>
        </a-table>

        <!-- 新增链接弹层 -->
        <a-modal
            v-model:open="isAddLinkModalOpen"
            width="25%"
            :destroyOnClose="true"
            :keyboard="false"
            :maskClosable="false"
            @cancel="handleModalClose('addLink')"
        >
            <div style="text-align: left;margin-bottom: 40px;">
                <h3>新增链接</h3>
            </div>

            <div style="margin: 0 20px;">
                <a-form>
                    <a-form-item label="链接名称">
                        <a-input 
                            style="width: 200px;"
                            placeholder="请输入链接名称" 
                            v-model:value="addLinkForm.linkName"
                        />
                    </a-form-item>
                </a-form>
            </div>

            <template #footer>
                <div style="text-align: right;margin-right: 20px;">
                    <a-button @click="setAddLinkModalShow(false)">取消</a-button>
                    <a-button :loading="isAddLinkModalLoading" type="primary" @click="handleAddLinkModalSave">保存</a-button>
                </div>
            </template>
        </a-modal>

        <!-- 查看链接弹层 -->
        <a-modal
            v-model:open="isSeeLinkModalOpen"
            width="80%"
            :destroyOnClose="true"
            :keyboard="false"
            :maskClosable="false"
            @cancel="handleModalClose('seeLink')"
        >
            <div style="text-align: left;margin-bottom: 40px;">
                <h3>链接信息</h3>
            </div>

            <div>
                <div style="margin: 20px 0;">
                    <span class="filter-item">
                        <span class="filter-label">推广链接：</span>
                        <a-input
                            allowClear
                            style="width: 160px;" 
                            v-model:value="seeLinkQueryForm.url"
                            placeholder="请输入推广链接"
                            @change="getLinkList"
                        />
                    </span>

                    <span class="filter-item">
                        <span class="filter-label">链接名称：</span>
                        <a-input
                            allowClear
                            style="width: 160px;" 
                            v-model:value="seeLinkQueryForm.urlName"
                            placeholder="请输入链接名称"
                            @change="getLinkList"
                        />
                    </span>

                    <span class="filter-item">
                        <span class="filter-label">创建时间：</span>
                        <a-range-picker
                            allowClear
                            v-model:value="seeLinkQueryForm.createTime as [Dayjs, Dayjs]"
                            value-format="YYYY-MM-DD" 
                            format="YYYY-MM-DD"
                            separator="至"
                            :placeholder="['开始日期', '结束日期']"
                            @change="getLinkList"
                        />
                    </span>
                </div>

                <div style="margin-bottom: 20px;">
                    <a-button type="primary" @click="handleModalOpen('addLink', discountId)">
                        新增链接
                    </a-button>
                </div>

                <a-table 
                    bordered 
                    :key="seeLinkTableKey"
                    :loading="isSeeLinkTableLoading" 
                    :scroll="{ x: 'max-content' }"
                    :data-source="seeLinkTableData" 
                    :pagination="seeLinkTablePagination" 
                    @change="handleSeeLinkTableChange"
                >
                    <a-table-column fixed="left" align="center" title="序号" :width="80">
                        <template #default="{ index }">
                            <span>{{ index + 1 }}</span>
                        </template>
                    </a-table-column>

                    <a-table-column align="center" key="urlName" title="名称" data-index="urlName" :width="200">
                        <template #default="{ record }">
                            <div>
                                <a-input
                                    v-if="record.editFlag"
                                    style="width: 150px;" 
                                    v-model:value="record.urlName"
                                    @blur="updateLinkName(record)"
                                />
                                <template v-else>
                                    <span style="margin-right: 10px;">{{ record.urlName }}</span>
                                    <FormOutlined 
                                        style="cursor: pointer;" 
                                        @click="record.editFlag = true; seeLinkTableKey += 1;"
                                    />
                                </template>
                            </div>
                        </template>
                    </a-table-column>

                    <a-table-column align="center" key="url" title="推广链接"  data-index="url" :width="250" />
                    <a-table-column align="center" key="newNumber" title="累计拉新人数" data-index="newNumber" />
                    <a-table-column align="center" key="orderNumber" title="累计订单数" data-index="orderNumber" />
                    <a-table-column align="center" key="commission" title="累计佣金" data-index="commission" />
                    
                    <a-table-column align="center" key="gmv" title="累计销售额" data-index="gmv">
                        <template #default="{ record }">
                            <span>{{ record.gmv == null ? 0 : (record.gmv / 100).toFixed(2) }}</span>
                        </template>
                    </a-table-column>
                    
                    <a-table-column align="center" key="createTime" title="创建时间" data-index="createTime">
                        <template #default="{ record }">
                            <span>{{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm') }}</span>
                        </template>
                    </a-table-column>

                    <a-table-column align="center" title="相关操作">
                        <template #default="{ record }">
                            <a-button type="link" @click="handleCopy(record.url)">
                                复制链接
                            </a-button>
                        </template>
                    </a-table-column>
                </a-table>
            </div>

            <template #footer>
                <div style="text-align: right;margin-right: 20px;">
                    <a-button @click="setSeeLinkModalShow(false)">取消</a-button>
                </div>
            </template>
        </a-modal>
    </div>
</template>

<style scoped lang="less">
.set-meal-manage {
    :deep(.ant-table-thead > tr > th) {
        background: #F4F5FA;
        border: none;
    }

}
    .filter-item{
        margin-right: 20px;
    }
</style>