<script setup lang="ts">
    import type { Dayjs } from 'dayjs';
    import dayjs from 'dayjs';
    import { appInfoEnum } from "~#/constant.ts";
    import { getPullNewDetailListApi } from '~/api/common/promotion';
    import { useTablePagination, handleCopy } from "~/utils/common-utils.ts";

    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const queryForm = ref({
        registerTime: [],
        status: null,
        registerType: null,
        urlName: ''
    });

    const isTableLoading = ref(false);
    const tableData = ref([]);
    const tablePagination = useTablePagination();

    onMounted(() => {
        getPullNewDetailList();
    });

    // 获取拉新详情列表
    const getPullNewDetailList = async () => {
        try {
            isTableLoading.value = true;
            // isAdmin 是否管理员查询 1：普通用户，2：管理员
            let params = {
                isAdmin: 1,
                appId: appInfoEnum.appId,
                pageNum: tablePagination.value.current,
                pageSize: tablePagination.value.pageSize,
                status: queryForm.value.status || null,
                registerType: queryForm.value.registerType || null,
                urlName: queryForm.value.urlName || null
            }
            if (params['isAdmin'] === 1) {
                params['currentUserId'] = userId?.value || null;
            }
            let { registerTime = [] } = queryForm.value;
            if (registerTime.length > 0) {
                params['startTime'] = registerTime[0];
                params['endTime'] = registerTime[1];
            }
            let res = await getPullNewDetailListApi(params);
            if (res.code == 200) {
                let _pullNewList = res?.pullNewList || [];
                let _total = Number(res?.Page?.total || 0);
                tableData.value = _pullNewList;
                tablePagination.value.total = _total;
            }
        } catch (err) {
            console.error(err);
        } finally {
            isTableLoading.value = false;
        }
    }

    const handleTableChange = pagination => {
        tablePagination.value = {
            ...tablePagination.value,
            ...pagination
        }
        getPullNewDetailList();
    }
</script>

<template>
    <div class="pull-new-detail">
        <div class="flex items-centent flex-wrap mt-9px mb-20px">
            <span class="filter-item">
                <span class="filter-label">注册时间：</span>
                <a-range-picker
                    allowClear
                    v-model:value="queryForm.registerTime as [Dayjs, Dayjs]"
                    value-format="YYYY-MM-DD" 
                    format="YYYY-MM-DD"
                    separator="至"
                    :placeholder="['开始日期', '结束日期']"
                    @change="getPullNewDetailList"
                />
            </span>
            
            <span class="filter-item">
                <span class="filter-label">状态：</span>
                <a-select 
                    allowClear 
                    style="width: 160px;" 
                    placeholder="请选择状态" 
                    v-model:value="queryForm.status"
                    @change="getPullNewDetailList"
                >
                    <a-select-option value="0">未转化</a-select-option>
                    <a-select-option value="1">已转化</a-select-option>
                </a-select>
            </span>

            <span class="filter-item">
                <span class="filter-label">注册类型：</span>
                <a-select 
                    allowClear 
                    style="width: 160px;" 
                    placeholder="请选择注册类型" 
                    v-model:value="queryForm.registerType"
                    @change="getPullNewDetailList"
                >
                    <a-select-option value="0">PC端</a-select-option>
                    <a-select-option value="1">小程序</a-select-option>
                </a-select>
            </span>

            <span class="filter-item">
                <span class="filter-label">推广链接：</span>
                <a-input 
                    allowClear 
                    style="width: 280px;" 
                    v-model:value="queryForm.urlName"
                    placeholder="请输入推广链接或者推广链接名称" 
                    @change="getPullNewDetailList"
                /> 
            </span>
        </div>

        <a-table 
            bordered 
            sticky
            :loading="isTableLoading" 
            :scroll="{ x: 'max-content' }"
            :data-source="tableData" 
            :pagination="tablePagination" 
            @change="handleTableChange"
        >
            <a-table-column fixed="left" align="center" key="urlName" title="推广链接名称" data-index="urlName" />
            <a-table-column fixed="left" align="center" key="userPhone" title="手机号" data-index="userPhone" />

            <a-table-column align="center" key="createTime" title="注册时间" data-index="createTime">
                <template #default="{ record }">
                    <span>{{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm') }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="registerType" title="注册类型" data-index="registerType">
                <template #default="{ record }">
                    <span>{{ record.registerType == '1' ? '小程序' : 'PC端' }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="conversionStatus" title="状态" data-index="conversionStatus">
                <template #default="{ record }">
                    <span v-if="record.conversionStatus === 1">转化</span>
                    <span v-else>未转化</span>
                </template>
            </a-table-column>
        </a-table>
    </div>
</template>

<style scoped lang="less">
.pull-new-detail {
    :deep(.ant-input) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-input-affix-wrapper) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-select-selector) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-picker) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-table-thead > tr > th) {
        background: #F4F5FA;
        border: none;
    }
    .filter-item{
        margin-right: 20px
    }
}
</style>