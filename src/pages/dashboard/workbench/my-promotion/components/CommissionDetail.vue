<script setup lang="ts">
    import type { Dayjs } from 'dayjs';
    import dayjs from 'dayjs';
    import { appInfoEnum } from "~#/constant.ts";
    import { getCommissionDetailListApi } from '~/api/common/promotion';
    import { useTablePagination, handleCopy } from "~/utils/common-utils.ts";

    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const queryForm = ref({
        orderNo: '',
        mobilePhone: '',
        url: '',
        payTime: [],
        registerType: null,
        priceType: null
    });

    const isTableLoading = ref(false);
    const originTableData = ref([]);
    const originTableTotal = ref(0);
    const tableData = ref([]);
    const tablePagination = useTablePagination();

    onMounted(() => {
        getCommissionDetailList();
    });

    const handleCommissionDetailListFilter = page => {
        if (originTableData.value.length > 0) {
            if (page === 1) {
                tablePagination.value.current = 1;
            }
            let dataList = [ ...originTableData.value ];
            if (queryForm.value.orderNo.trim() !== '') {
                dataList = originTableData.value.filter(v => {
                    return v.orderNo == queryForm.value.orderNo.trim()
                });
            }
            if (queryForm.value.mobilePhone.trim() !== '') {
                if (queryForm.value.mobilePhone.trim().length != 4) {
                    dataList = [];
                } else {
                    dataList = originTableData.value.filter(v => {
                        return v.phone.slice(v.phone.length - 4) == queryForm.value.mobilePhone.trim()
                    });
                }
            }
            if (queryForm.value.url.trim() !== '') {
                dataList = originTableData.value.filter(v => {
                    return v.urlName == queryForm.value.url.trim() || v.url == queryForm.value.url.trim()
                });
            }
            if (queryForm.value.payTime && queryForm.value.payTime.length > 0) {
                dataList = originTableData.value.filter(v => {
                    return (
                        (v.createTime || '').split(' ')[0].replace(/[\D]/g, '') >= queryForm.value.payTime[0].replace(/[\D]/g, '') 
                        && (v.createTime || '').split(' ')[0].replace(/[\D]/g, '') <= queryForm.value.payTime[1].replace(/[\D]/g, '')
                    )
                });
            }
            if (queryForm.value.registerType != null) {
                dataList = originTableData.value.filter(v => {
                    return queryForm.value.registerType == '0' && !v.registerType || queryForm.value.registerType == '1' && v.registerType == '1'
                });
            }
            if (queryForm.value.priceType != null) {
                dataList = originTableData.value.filter(v => {
                    return queryForm.value.priceType == v.priceType
                });
            }
            tablePagination.value.total = dataList.length || 0;
            tableData.value = dataList.slice(
                (tablePagination.value.current - 1) * tablePagination.value.pageSize, 
                tablePagination.value.current * tablePagination.value.pageSize
            );
        }
    }

    // 获取佣金详情列表
    const getCommissionDetailList = async () => {
        try {
            isTableLoading.value = true;
            // isAdmin 是否管理员查询 1：普通用户，2：管理员
            let params = {
                isAdmin: 1,
                appId: appInfoEnum.appId,
                userId: userId?.value || null,
                pageNum: 1,
                pageSize: 500
            }
            let res = await getCommissionDetailListApi(params);
            if (res.code == 200) {
                let _orderList = res?.orderList || [];
                let _total = Number(res?.page?.total || 0);
                _orderList = _orderList.map(v => {
                    v['costPriceOrCommissionRate'] = {
                        commissionRate: v.commissionRate,
                        priceType: v.priceType
                    }
                    return v;
                });
                originTableData.value = _orderList;
                originTableTotal.value = _total;
                handleCommissionDetailListFilter(1);
            }
        } catch (err) {
            console.error(err);
        } finally {
            isTableLoading.value = false;
        }
    }

    const handleTableChange = pagination => {
        tablePagination.value = {
            ...tablePagination.value,
            ...pagination
        }
        handleCommissionDetailListFilter(null);
    }

    const getCpOrCr = (data: any = {}) => {
        const dataObj = {
            '1': '佣金比例：' + data.commissionRate + "%",
            '2': '成本价：' + data.commissionRate
        }
        return dataObj[String(data.priceType)] || '/';
    }
</script>

<template>
    <div class="commission-detail">
        <div class="flex items-centent flex-wrap mt-9px">
            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">订单号：</span>
                <a-input 
                    allowClear 
                    style="width: 160px;" 
                    v-model:value="queryForm.orderNo"
                    placeholder="请输入订单号" 
                    @change="handleCommissionDetailListFilter(1)"
                />
            </div>
            
            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">手机号：</span>
                <a-input 
                    allowClear 
                    style="width: 180px;" 
                    v-model:value="queryForm.mobilePhone"
                    placeholder="请输入手机号后4位"
                    @change="handleCommissionDetailListFilter(1)"
                />
            </div>
            
            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">推广链接：</span>
                <a-input 
                    allowClear 
                    style="width: 280px;" 
                    v-model:value="queryForm.url"
                    placeholder="请输入推广链接或者推广链接名称" 
                    @change="handleCommissionDetailListFilter(1)"
                /> 
            </div>
            
            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">成交时间：</span>
                <a-range-picker
                    allowClear
                    v-model:value="queryForm.payTime as [Dayjs, Dayjs]"
                    value-format="YYYY-MM-DD" 
                    format="YYYY-MM-DD"
                    separator="至"
                    :placeholder="['开始日期', '结束日期']"
                    @change="handleCommissionDetailListFilter(1)"
                />
            </div>

            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">注册类型：</span>
                <a-select 
                    allowClear 
                    style="width: 160px;" 
                    placeholder="请选择状态" 
                    v-model:value="queryForm.registerType"
                    @change="handleCommissionDetailListFilter(1)"
                >
                    <a-select-option value="0">PC端</a-select-option>
                    <a-select-option value="1">小程序</a-select-option>
                </a-select>
            </div>

            <div class="mr-30px flex items-center mb-20px">
                <span class="filter-label">佣金类型：</span>
                <a-select 
                    allowClear 
                    style="width: 180px;" 
                    placeholder="请选择状态" 
                    v-model:value="queryForm.priceType"
                    @change="handleCommissionDetailListFilter(1)"
                >
                    <a-select-option value="1">佣金比例</a-select-option>
                    <a-select-option value="2">成本价</a-select-option>
                </a-select>
            </div>
        </div>

        <a-table 
            bordered 
            sticky
            :loading="isTableLoading" 
            :scroll="{ x: 'max-content' }"
            :data-source="tableData" 
            :pagination="tablePagination" 
            @change="handleTableChange"
        >
            <a-table-column fixed="left" align="center" key="discountName" title="推广类型" data-index="discountName" />
            <a-table-column fixed="left" align="center" key="urlName" title="推广链接名称" data-index="urlName" />

            <a-table-column align="center" key="registerType" title="注册类型" data-index="registerType">
                <template #default="{ record }">
                    <span>{{ record.registerType == '1' ? '小程序' : 'PC端' }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="orderNo" title="订单号" data-index="orderNo" />
            <a-table-column align="center" key="phone" title="手机号" data-index="phone" />
            <a-table-column align="center" key="productName" title="购买类型" data-index="productName" />

            <a-table-column align="center" key="productPrice" title="购买价格" data-index="productPrice">
                <template #default="{ record }">
                    <span>{{ record.productPrice }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="costPriceOrCommissionRate" title="成本价格/佣金比例" data-index="costPriceOrCommissionRate">
                <template #default="{ record }">
                    <div>{{ getCpOrCr(record.costPriceOrCommissionRate) }}</div>
                </template>
            </a-table-column>

            <a-table-column align="center" key="commission" title="所得佣金" data-index="commission">
                <template #default="{ record }">
                    <span>{{ record.commission }}</span>
                </template>
            </a-table-column>
            
            <a-table-column align="center" key="createTime" title="成交时间" data-index="createTime">
                <template #default="{ record }">
                    <span>{{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm') }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="status" title="返现状态" data-index="status">
                <template #default="{ record }">
                    <span style="color: #CECECE;" v-if="record.status === 2">已返现</span>
                    <span style="color:#2062E4;" v-else>未返现</span>
                </template>
            </a-table-column>
        </a-table>
    </div>
</template>

<style scoped lang="less">
.commission-detail{
    :deep(.ant-input) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-input-affix-wrapper) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-select-selector) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-picker) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-table-thead > tr > th) {
        background: #F4F5FA;
        border: none;
    }
}
</style>