<script setup lang="ts">
    import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
    import type { UploadChangeParam } from 'ant-design-vue';
    import { handleCopy, getBase64 } from "~/utils/common-utils.ts";
    import { appInfoEnum, accountTypeEnum } from "~#/constant.ts";
    import { commissionWithdrawApi, getWithdrawRecordListApi, uploadUrl } from '~/api/common/promotion';
    // import { uploadUrl } from '~/api/common/public';

    enum operateTypeEnum {
        TJ = 'TJ',
        CXSH = 'CXSH'
    }
    enum currentPageEnum {
        SCAN_CODE = 'scanCode',
        INFO_SUBMIT = 'infoSubmit',
        UPLOAD_INVOICE = 'uploadInvoice',
        INVOICE_INFO_SUBMIT = 'invoiceInfoSubmit'
    }
    interface IConfig  {
        operateType: operateTypeEnum,
        currentPage: currentPageEnum, 
        status: null | number
    }

    const emit = defineEmits(['update:isOpen', 'updateWithdrawStatus', 'updateWithdrawRefuseReason']);

    const message = useMessage();
    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const token = useAuthorization();

    const props = defineProps({
        isOpen: {
            type: Boolean,
            default: false
        },
        // 账户类型 (包含：1：公司，2：个人)
        accountType: {
            type: Number,
            default: -1
        },
        // 提现单号
        withdrawNo: {
            type: String,
            default: ''
        },
        // 提现状态
        withdrawStatus: {
            type: Number,
            default: -1
        },
        // 提现驳回原因
        withdrawRefuseReason: {
            type: [null, String],
            default: null
        },
        personalInfo: {
            type: Object,
            default: () => {
                return new Object();
            }
        },
        personalOtherData: {
            type: Object,
            default: () => {
                return new Object({
                    withdrawAmount: null,
                    arriveAmount: null
                });
            }
        },
        companyInfo: {
            type: Object,
            default: () => {
                return new Object();
            }
        },
        currentAccountType: {
            type: Number,
            default: -1
        },
    });

    const isCompanyDisabled = ref({
        scanCode: false,
        infoSubmit: false,
        uploadInvoice: false,
        invoiceInfoSubmit: false
    });

    const companyOtherData = ref({
        invoiceBase64Url: '',
        invoiceUploadUrl: '',
        isInvoiceUploadLoading: false,
        logisticsCompany: '',
        logisticsNumber: '',
        isMailShow: false
    });

    const personalStepList = computed(() => {
        return [
            {
                title: '填写收款信息',
                description: props.withdrawStatus === 0 ? '进行中' : props.withdrawStatus > 0 ? '已完成' : '未进行'
            },
            // {
            //     title: '扫码签约',
            //     description: props.withdrawStatus === 1 ? '进行中' : props.withdrawStatus > 1 ? '已扫码' : '未进行'
            // },
            {
                title: '等待审核',
                description: props.withdrawStatus === 2 ?  '进行中' : props.withdrawStatus > 2 ? '审核通过' : '未进行'
            },
            {
                title: '收款成功',
                description: props.withdrawStatus > 2 ? '已打款' : '未进行'
            }
        ]
    });

    const companyStepList = computed(() => {
        return [
            {
                title: '填写收款信息',
                description: props.withdrawStatus === 0 ? '进行中' : props.withdrawStatus > 0 ? '已完成' : '未进行'
            },
            {
                title: '等待审核',
                description: props.withdrawStatus === 2 ? '进行中' : props.withdrawStatus > 2 ? '审核通过' : '未进行'
            },
            {
                title: '收款交易',
                description: props.withdrawStatus === 3 ? '进行中' : props.withdrawStatus > 3 ? '已打款': '未进行'
            },
            {
                title: '上传发票',
                description: props.withdrawStatus === 4 ? '进行中' : props.withdrawStatus > 4 ? '已上传' : '未进行'
            }
        ]
    });

    const resetCompanyOtherData = (data = {}) => {
        companyOtherData.value = {
            ...companyOtherData.value,
            invoiceBase64Url: '',
            invoiceUploadUrl: '',
            logisticsCompany: '',
            logisticsNumber: '',
            ...data
        }
    }

    const handleModalClose = () => {
        emit('update:isOpen', false);
        resetCompanyOtherData();
    }

    const tj = (currentPage: currentPageEnum) => {
        if (currentPage === currentPageEnum.SCAN_CODE) {
            message.success('等待管理员进行审核！'); 
        }
        isCompanyDisabled.value[currentPage] = true;
        handleCommissionWithdraw({
            operateType: operateTypeEnum.TJ,
            currentPage: currentPage, 
            status: null
        });
    }

    const cxsh = (currentPage: currentPageEnum, status) => {
        handleCommissionWithdraw({
            operateType: operateTypeEnum.CXSH,
            currentPage: currentPage, 
            status: status
        });
    }

    const handleCommissionWithdraw = (config: IConfig) => {
        let params1 = { 
            appId: appInfoEnum.appId,
            id: props.withdrawNo
        }
        if (config.operateType === operateTypeEnum.CXSH) {
            params1['status'] = config.status;
        }
        if (config.currentPage === currentPageEnum.INVOICE_INFO_SUBMIT) {
            params1['invoice'] = companyOtherData.value.invoiceBase64Url;
            params1['logistics'] = companyOtherData.value.logisticsCompany + companyOtherData.value.logisticsNumber;
        }
        commissionWithdrawApi(params1).then(res1 => {
            if (res1.code == 200) {
                if (config.operateType === operateTypeEnum.CXSH) {
                    message.success('重新发起成功！');
                }
                let params2 = {
                    appId: appInfoEnum.appId,
                    userId: userId?.value || null,
                    id: props.withdrawNo
                }
                getWithdrawRecordListApi(params2).then(res2 => {
                    if (res2.code == 200) {
                        let withdrawRecordData = res2.list && res2.list.length > 0 ? res2.list[0] : {};
                        emit('updateWithdrawStatus', withdrawRecordData.withdrawStatus);
                        if (config.operateType === operateTypeEnum.CXSH) {
                            emit('updateWithdrawRefuseReason', withdrawRecordData.refuseReason);
                        }
                        if (config.currentPage === currentPageEnum.INVOICE_INFO_SUBMIT) {                            
                            resetCompanyOtherData();
                        }
                    }
                });
            }
        });
    }

    const handleInvoiceBeforeUpload = (file) => {
        return true;
    }
    const handleInvoiceUploadChange = (info: UploadChangeParam) => {
        if (info.file.status === 'uploading') {
            companyOtherData.value.isInvoiceUploadLoading = true;
            return;
        }
        if (info.file.status === 'done') {
            if (info.file?.response) {
                getBase64(info.file.originFileObj, (base64Url: string) => {
                    companyOtherData.value.invoiceBase64Url = base64Url;
                });
                companyOtherData.value.invoiceUploadUrl = info.file.response;
                message.success('上传成功！');
            } else {
                message.error('上传失败！');
            }
            companyOtherData.value.isInvoiceUploadLoading = false;
        }
        if (info.file.status === 'error') {
            companyOtherData.value.isInvoiceUploadLoading = false;
            message.error('上传失败！');
        }
    }
   
</script>

<template>
    <!-- 提现详情弹层 -->
    <a-modal
        v-model:open="props.isOpen"
        width="1400px"
        :destroyOnClose="true"
        :keyboard="false"
        :maskClosable="false"
        :title="null"
        :footer="null"
        @cancel="handleModalClose"
    >
        <div style="width: 96%;margin: 40px auto;">
            <!-- 公司 -->
            <div v-if="currentAccountType === accountTypeEnum.COMPANY">
                <a-steps :current="props.withdrawStatus === 5 ? 4 : props.withdrawStatus === 6 ? 4 : props.withdrawStatus" :items="companyStepList" />

                <div style="margin-top: 40px;display: flex;justify-content: center;">
                    <div v-if="props.withdrawStatus === 0">
                        <a-form 
                            :model="props.companyInfo" 
                            :label-col="{ span: 6 }" 
                            :wrapper-col="{ span: 12 }"
                            style="margin-top: 20px;"
                        >
                            <a-form-item label="公司信息">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.companyInfo.alipayName" 
                                    placeholder="请输入公司信息" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="开户行与支行">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.companyInfo.bank" 
                                    placeholder="请输入开户行与支行" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="银行卡">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.companyInfo.accountNumber" 
                                    placeholder="请输入银行卡"
                                    disabled
                                />
                            </a-form-item>
                        </a-form>
                        <div style="margin-top: 60px;text-align: center;">
                            <a-button type="primary" :disabled="isCompanyDisabled.infoSubmit" @click="tj(currentPageEnum.INFO_SUBMIT)">
                                提交
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 2">
                        <div v-if="props.withdrawRefuseReason != null" style="font-size: 14px;color: #f5365c;">
                            审核失败：{{ props.withdrawRefuseReason }}
                        </div>
                        <a-form 
                            :model="props.companyInfo" 
                            :label-col="{ span: 6 }" 
                            :wrapper-col="{ span: 12 }"
                            style="margin-top: 20px;"
                        >
                            <a-form-item label="公司信息">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.companyInfo.alipayName" 
                                    placeholder="请输入公司信息" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="开户行与支行">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.companyInfo.bank" 
                                    placeholder="请输入开户行与支行" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="银行卡">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.companyInfo.accountNumber" 
                                    placeholder="请输入银行卡"
                                    disabled
                                />
                            </a-form-item>
                        </a-form>
                        <div style="font-size: 14px;color: #f5365c;">
                            温馨提示：信息已经上交，无法进行更改，请耐心等待审核结果
                        </div>
                        <div style="margin-top: 30px;text-align: center;">
                            <a-button v-if="props.withdrawStatus === 2 && props.withdrawRefuseReason == null" disabled>
                                审核中
                            </a-button>
                            <a-button v-else danger @click="cxsh(currentPageEnum.INFO_SUBMIT, 2)">
                                重新审核
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 3">
                        <div style="margin-top: 30px;text-align: center;">
                            <a-button type="primary" :disabled="isCompanyDisabled.uploadInvoice" @click="tj(currentPageEnum.UPLOAD_INVOICE)">
                                上传发票
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 4 || props.withdrawStatus === 5 || props.withdrawStatus === 6" style="width: 100%;">
                        <div style="font-size: 14px; color: #f5365c;">
                            温馨提示：以下是我司的发票信息，开票后可以直接上传电子发票。
                        </div>
                        
                        <div style="margin-top: 20px;background: #F5F5F5;padding: 20px 0;text-indent: 20px;">
                            <a-row>
                                <a-col :span="8">
                                    <span style="font-size: 14px;color: #666666;">公司名称：广州萌啦信息科技有限公司</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('广州萌啦信息科技有限公司')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                                <a-col :span="8">
                                    <span style="font-size: 14px;color: #666666;">税号：91440101MA9UQ7X797</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('91440101MA9UQ7X797')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                            </a-row>
                            <a-row style="margin-top: 20px;">
                                <a-col :span="12">
                                    <span style="font-size: 14px;color: #666666;">单位地址：广州市黄埔区坑田大街32号鱼珠智谷E-PARK创意园C05号(仅限办公)</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;"
                                        @click="handleCopy('广州市黄埔区坑田大街32号鱼珠智谷E-PARK创意园C05号(仅限办公)')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                            </a-row>
                            <a-row style="margin-top: 20px;">
                                <a-col :span="10">
                                    <span style="font-size: 14px;color: #666666;">电话号码：15913116396</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('15913116396')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                                <a-col :span="8">
                                    <span style="font-size: 14px;color: #666666;">开户银行：中国农业银行股份有限公司广州鱼珠支行</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('中国农业银行股份有限公司广州鱼珠支行')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                            </a-row>
                            <a-row style="margin-top: 20px;">
                                <a-col :span="10">
                                    <span style="font-size: 14px;color: #666666;">银行账户：44062501040013775</span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('44062501040013775')"
                                    >
                                        点击复制
                                    </a>
                                </a-col>
                            </a-row>
                        </div>

                        <div style="margin-top: 40px;">
                            <a-row>
                                <a-col :span="4">上传电子发票或者纸质发票照片：</a-col>
                                <a-col :span="5">
                                    <a-upload
                                        class="avatar-uploader"
                                        list-type="picture-card"
                                        name="file"
                                        :action="uploadUrl"
                                        :headers="{
                                            'X-Auth-Token': token
                                        }"
                                        :maxCount="1"
                                        :show-upload-list="false"
                                        :before-upload="handleInvoiceBeforeUpload"
                                        @change="handleInvoiceUploadChange"
                                    >
                                        <img style="width: 100%; height: 100%;" v-if="companyOtherData.invoiceUploadUrl" :src="companyOtherData.invoiceUploadUrl" />
                                        <div v-else>
                                            <LoadingOutlined v-if="companyOtherData.isInvoiceUploadLoading" />
                                            <PlusOutlined v-else />
                                        </div>
                                    </a-upload>
                                </a-col>
                            </a-row>
                        </div>

                        <div style="margin-top: 30px;">
                            发票邮寄（纸质发票）：
                            <a-input style="width: 170px;" placeholder="请输入物流公司" v-model="companyOtherData.logisticsCompany" />
                            <a-input style="width: 170px;margin-left: 10px;" placeholder="请输入物流单号" v-model="companyOtherData.logisticsNumber" />
                        </div>

                        <div style="margin-top: 20px;">
                            <a @click="companyOtherData.isMailShow = !companyOtherData.isMailShow" style="color: #666666;">
                                点击查看邮寄信息
                            </a>
                            <div v-if="companyOtherData.isMailShow" style="margin-top: 10px;margin-left: 20px;">
                                <a-row>
                                    <span style="font-size: 14px;color: #666666;">
                                        单位地址: 广州市黄埔区坑田大街32号鱼珠智谷E-PARK创意园智慧楼307（仅限办公）
                                    </span>
                                    <a 
                                        style="margin-left: 20px;color: #979797;" 
                                        @click="handleCopy('广州市黄埔区坑田大街32号鱼珠智谷E-PARK创意园智慧楼307（仅限办公）')"
                                    >
                                        点击复制
                                    </a>
                                </a-row>
                                <a-row>
                                    <a-col :span="3">
                                        <span style="font-size: 14px;color: #666666;">姓名: Lily Liu</span>
                                    </a-col>
                                    <a-col :span="5">
                                        <span style="font-size: 14px;color: #666666;">联系电话: 13265162250</span>
                                        <a 
                                            style="margin-left: 20px;color: #979797;"
                                            @click="handleCopy('13265162250')"
                                        >
                                            点击复制
                                        </a>
                                    </a-col>
                                </a-row>
                            </div>
                        </div>

                        <div style="margin-top: 30px;text-align: center;">
                            <div style="font-size: 14px;color: #f5365c;" v-if="props.withdrawRefuseReason != null">
                                审核失败：{{ props.withdrawRefuseReason }}
                            </div>
                            <template v-if="props.withdrawStatus === 4 && props.withdrawRefuseReason == null">
                                <a-button style="margin-top: 10px;" type="primary" :disabled="isCompanyDisabled.invoiceInfoSubmit" @click="tj(currentPageEnum.INVOICE_INFO_SUBMIT)">
                                    提交
                                </a-button>
                            </template>
                            <a-button v-if="props.withdrawRefuseReason != null" @click="cxsh(currentPageEnum.INVOICE_INFO_SUBMIT, 5)" style="margin-top: 10px;" danger>
                                重新审核
                            </a-button>
                            <a-button v-if="props.withdrawStatus === 6" style="margin-top: 10px;" type="primary">
                                审核通过
                            </a-button>
                            <a-button v-if="props.withdrawStatus === 5 && props.withdrawRefuseReason == null" style="margin-top: 10px;" type="primary" disabled>
                                审核中
                            </a-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人 -->
            <div v-if="currentAccountType === accountTypeEnum.PERSONAL">
                <a-steps :current="props.withdrawStatus === 3 ? props.withdrawStatus + 1 : props.withdrawStatus" :items="personalStepList" />

                <div style="margin-top: 40px;display: flex;justify-content: center;">
                    <div v-if="props.withdrawStatus === 0">
                        <div style="color: #f5365c;" v-if="props.withdrawStatus == 0 && props.withdrawRefuseReason">
                            驳回理由：{{ props.withdrawRefuseReason }}
                        </div>
                        <a-form 
                            :model="props.personalInfo" 
                            :label-col="{ span: 6 }" 
                            :wrapper-col="{ span: 12 }"
                            style="margin-top: 20px;"
                        >
                            <a-form-item label="姓名">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.alipayName" 
                                    placeholder="请输入姓名" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="手机号">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.personalInfo.phone" 
                                    placeholder="请输入手机号" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="开户行与支行">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.bank" 
                                    placeholder="请输入开户行与支行"
                                    disabled 
                                />
                            </a-form-item>
                            <a-form-item label="身份证">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.identityNumber" 
                                    placeholder="请输入身份证"
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="银行卡号">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.accountNumber" 
                                    placeholder="请输入银行卡号"
                                    disabled
                                />
                            </a-form-item>
                        </a-form>
                        <div style="margin-top: 60px;text-align: center;">
                            <a-button type="primary" disabled v-if="props.withdrawStatus === 0 && !props.withdrawRefuseReason">
                                信息审核中
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 1">
                        <img src="@/assets/images/common/saoma.png" />
                        <div style="margin-top: 40px;text-align: center;">
                            <a-button type="primary" :disabled="isCompanyDisabled.scanCode" @click="tj(currentPageEnum.SCAN_CODE)">
                                下一步
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 2">
                        <div v-if="props.withdrawRefuseReason != null" style="font-size: 14px;color: #f5365c;">
                            审核失败：{{ props.withdrawRefuseReason }}
                        </div>
                        <a-form 
                            :model="props.personalInfo" 
                            :label-col="{ span: 6 }" 
                            :wrapper-col="{ span: 12 }"
                            style="margin-top: 20px;"
                        >
                            <a-form-item label="姓名">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.alipayName" 
                                    placeholder="请输入姓名" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="手机号">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.personalInfo.phone" 
                                    placeholder="请输入手机号" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="开户行与支行">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.bank" 
                                    placeholder="请输入开户行与支行"
                                    disabled 
                                />
                            </a-form-item>
                            <a-form-item label="身份证">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.identityNumber" 
                                    placeholder="请输入身份证"
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="银行卡号">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.accountNumber" 
                                    placeholder="请输入银行卡号"
                                    disabled
                                />
                            </a-form-item>
                        </a-form>
                        <div style="font-size: 14px;color: #f5365c;">
                            温馨提示：信息已经上交，无法进行更改，请耐心等待审核结果
                        </div>
                        <div style="margin-top: 30px;text-align: center;">
                            <a-button v-if="props.withdrawStatus === 2 && props.withdrawRefuseReason == null" disabled>
                                审核中
                            </a-button>
                            <a-button v-else danger @click="cxsh(currentPageEnum.INFO_SUBMIT, 2)">
                                重新审核
                            </a-button>
                        </div>
                    </div>

                    <div v-if="props.withdrawStatus === 3">
                        <a-form 
                            :model="props.personalInfo" 
                            :label-col="{ span: 6 }" 
                            :wrapper-col="{ span: 12 }"
                            style="margin-top: 20px;"
                        >
                            <a-form-item label="姓名">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.alipayName" 
                                    placeholder="请输入姓名" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="手机号">
                                <a-input 
                                    style="width: 300px;" 
                                    v-model:value="props.personalInfo.phone" 
                                    placeholder="请输入手机号" 
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="开户行与支行">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.bank" 
                                    placeholder="请输入开户行与支行"
                                    disabled 
                                />
                            </a-form-item>
                            <a-form-item label="身份证">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.identityNumber" 
                                    placeholder="请输入身份证"
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="银行卡号">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalInfo.accountNumber" 
                                    placeholder="请输入银行卡号"
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="提现金额">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalOtherData.withdrawAmount" 
                                    placeholder="请输入提现金额"
                                    disabled
                                />
                            </a-form-item>
                            <a-form-item label="到账金额">
                                <a-input 
                                    style="width: 300px;"
                                    v-model:value="props.personalOtherData.arriveAmount" 
                                    placeholder="请输入到账金额"
                                    disabled
                                />
                            </a-form-item>
                        </a-form>
                    </div>
                </div>
            </div>
        </div>
    </a-modal>
</template>

<style scoped lang="less">
    
</style>