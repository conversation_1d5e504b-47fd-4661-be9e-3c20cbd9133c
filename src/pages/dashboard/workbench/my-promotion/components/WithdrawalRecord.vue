<script setup lang="ts">
    import dayjs from 'dayjs';
    import { appInfoEnum } from "~#/constant.ts";
    import { getWithdrawRecordListApi } from '~/api/common/promotion';

    const emit = defineEmits(['seeProgress']);

    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const isTableLoading = ref(false);
    const tableData = ref([]);

    onMounted(() => {
        getWithdrawalRecordList();
    });

    // 获取提现记录列表
    const getWithdrawalRecordList = async () => {
        try {
            isTableLoading.value = true;
            let params = {
                appId: appInfoEnum.appId,
                userId: userId?.value || null
            }
            let res = await getWithdrawRecordListApi(params);
            if (res.code == 200) {
                tableData.value = res?.list || [];
            }
        } catch (err) {
            console.error(err);
        } finally {
            isTableLoading.value = false;
        }
    }
</script>

<template>
    <div class="withdrawal-record">
        <a-table 
            bordered 
            sticky
            :loading="isTableLoading" 
            :scroll="{ x: 'max-content' }"
            :data-source="tableData" 
            :pagination="false" 
        >
            <a-table-column fixed="left" align="center" key="id" title="提现单号" data-index="id" />
            <a-table-column align="center" key="name" title="名称" data-index="name" />
            <a-table-column align="center" key="identityNumber" title="纳税号" data-index="identityNumber" />
            <a-table-column align="center" key="accountNumber" title="银行卡号" data-index="accountNumber" />
            <a-table-column align="center" key="withdrawAmount" title="提现金额" data-index="withdrawAmount" />

            <a-table-column align="center" key="createTime" title="申请时间" data-index="createTime">
                <template #default="{ record }">
                    <span>{{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm') }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="updateTime" title="审批时间" data-index="updateTime">
                <template #default="{ record }">
                    <span>{{ dayjs(record.updateTime).format('YYYY-MM-DD HH:mm') }}</span>
                </template>
            </a-table-column>

            <a-table-column align="center" key="withdrawStatus" title="状态" data-index="withdrawStatus">
                <template #default="{ record }">
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 0 && !record.refuseReason">
                        信息审核中
                    </span>
                    <span style="color: #FE1616;font-weight: bold;" v-if="record.withdrawStatus == 0 && record.refuseReason">
                        提现失败
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 1">
                        等待扫码签约
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 2">
                        等待审核
                    </span>
                    <span style="color: #CECECE;font-weight: bold;" v-if="record.withdrawStatus == 3">
                        提现成功
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 4">
                        等待上传发票
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 5 && !record.refuseReason">
                        发票审核中
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 5 && record.refuseReason">
                        待再次上传发票
                    </span>
                    <span style="color: #2062E5;font-weight: bold;" v-if="record.withdrawStatus == 6">
                        已上传发票
                    </span>
                </template>
            </a-table-column>

            <a-table-column align="center" title="操作">
                <template #default="{ record }">
                    <a-button type="link" @click="emit('seeProgress', record)">
                        查看进度
                    </a-button>
                </template>
            </a-table-column>
        </a-table>
    </div>
</template>

<style scoped lang="less">
.withdrawal-record{
    :deep(.ant-table-thead > tr > th) {
        background: #F4F5FA;
        border: none;
    }
}
</style>