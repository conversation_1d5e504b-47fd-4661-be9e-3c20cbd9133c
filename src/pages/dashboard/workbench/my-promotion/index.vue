<script setup lang="ts">
    import { Modal } from 'ant-design-vue';
    import { appInfoEnum, accountTypeEnum } from "~#/constant.ts";
    import { getPartnerInfoApi, getUserAccountInfoApi, createUserAccount<PERSON>pi, createWithdrawaRecordApi, getWithdrawRecordListApi } from '~/api/common/promotion';
    import SetMealManage from './components/SetMealManage.vue';
    import PullNewDetail from './components/PullNewDetail.vue';
    import CommissionDetail from './components/CommissionDetail.vue';
    import WithdrawalRecord from './components/WithdrawalRecord.vue';
    import WithdrawDetailModal from './components/WithdrawDetailModal.vue';
    import PageContainer from '~/components/page-container/index.vue'
    import { LeftOutlined } from '@ant-design/icons-vue';


    const message = useMessage();
    const userStore = useUserStore();
    const { userId } = storeToRefs(userStore);

    const info = ref({
        orderNumber: 0,
        withdrawableMoney: 0,
        withdrawnMoney: 0
    });
    const company = ref({
        id: null,
        alipayName: '',
        bank: '',
        accountNumber: '',
        accountType: accountTypeEnum.COMPANY,
        appId: appInfoEnum.appId,
        userId: ''
    });
    const companyInfo = ref({
        alipayName: '',
        bank: '',
        accountNumber: ''
    });
    const personal = ref({
        id: null,
        alipayName: '',
        phone: '',
        bank: '',
        identityNumber: '',
        accountNumber: '',
        accountType: accountTypeEnum.PERSONAL,
        appId: appInfoEnum.appId,
        userId: ''
    });
    const personalInfo = ref({
        alipayName: '',
        phone: '',
        bank: '',
        identityNumber: '',
        accountNumber: ''
    });
    const isEditModalOpen = ref(false);
    const isEditModalLoading = ref(false);

    const isWithdrawModalOpen = ref(false);
    const isWithdrawModalLoading = ref(false);
    const accountList = ref([]);
    const accountType = ref(-1);
    
    const isWithdrawDetailModalOpen = ref(false);
    const withdrawNo = ref('');
    const withdrawStatus = ref(-1);
    const withdrawRefuseReason = ref(null);
    const personalOtherData = ref({
        withdrawAmount: null,
        arriveAmount: null
    });

    const currentAccountType = ref(-1);

    const tabKey = ref('5');

    const hasCompany = computed(() => {
        const cAccountNumber = companyInfo.value.accountNumber;
        const pAccountNumber = personalInfo.value.accountNumber;
        return (cAccountNumber != null && cAccountNumber !== '') && (pAccountNumber == null || pAccountNumber === '');
    });
    const hasPersonal = computed(() => {
        const cAccountNumber = companyInfo.value.accountNumber;
        const pAccountNumber = personalInfo.value.accountNumber;
        return (cAccountNumber == null || cAccountNumber === '') && (pAccountNumber != null && pAccountNumber !== '');
    });
    const hasCompanyAndPersonal = computed(() => {
        const cAccountNumber = companyInfo.value.accountNumber;
        const pAccountNumber = personalInfo.value.accountNumber;
        return (cAccountNumber != null && cAccountNumber !== '') && (pAccountNumber != null && pAccountNumber !== '');
    });

    onMounted(() => {
        getPartnerInfo();
        tx(3);
    });

    const getPartnerInfo = () => {
        // isAdmin 是否管理员查询 1：普通用户，2：管理员
        let params = { 
            isAdmin: 1, 
            userId: userId?.value || null,
            appId: appInfoEnum.appId
        }
        getPartnerInfoApi(params).then(res => {
            if (res.code == 200) {
                info.value = res.info;
            }
        });
    }

    const tx = type => {
        accountList.value = [];
        let params = { 
            appId: appInfoEnum.appId, 
            userId: userId?.value || null
        }
        getUserAccountInfoApi(params).then(res => {
            if (res.code == 200) {
                if (res.company == null && res.personal == null) {
                    if (type != 3) {
                        setEditModalShow(true);
                    }
                } else {
                    if (type == 1) {
                        setWithdrawModalShow(true);
                    }
                    if (res.company != null) {
                        accountList.value.push(res.company);
                        // 用于公司账号信息编辑反显
                        company.value = { ...res.company };
                        // 仅用于展示
                        companyInfo.value = { ...res.company };
                    }
                    if (res.personal != null) {
                        accountList.value.push(res.personal);
                        // 用于个人账号信息编辑反显
                        personal.value = { ...res.personal };
                        // 仅用于展示
                        personalInfo.value = { ...res.personal };
                    }
                }
            }
        });
    }

    const setEditModalShow = (isFlag: boolean) => {
        isEditModalOpen.value = isFlag;
        if (!isFlag) {
            company.value = {
                id: null,
                alipayName: '',
                bank: '',
                accountNumber: '',
                accountType: accountTypeEnum.COMPANY,
                appId: appInfoEnum.appId,
                userId: ''
            }
            personal.value = {
                id: null,
                alipayName: '',
                phone: '',
                bank: '',
                identityNumber: '',
                accountNumber: '',
                accountType: accountTypeEnum.PERSONAL,
                appId: appInfoEnum.appId,
                userId: ''
            }
        }
    }
    const handleEditModalOpen = () => {
        tx(2);
        setEditModalShow(true);
    }
    const handleEditModalClose = () => {
        setEditModalShow(true);
        Modal.confirm({
            title: '确认关闭？',
            icon: () => null,
            onOk: async () => {
                setEditModalShow(false);
                Promise.reject();
            }
        });
    }

    const setWithdrawModalShow = (isFlag: boolean) => {
        isWithdrawModalOpen.value = isFlag;
    }
    const handleWithdrawModalOpen = () => {
        tx(1);
        setWithdrawModalShow(true);
    }
    const handleWithdrawModalClose = () => {
        accountType.value = -1;
        withdrawNo.value = '';
        withdrawStatus.value = -1;
        withdrawRefuseReason.value = null;
    }

    const getCheckStatus = (key, fnName) => {
        let config = {
            'companyInfo': {
                fieldList: ['alipayName', 'bank', 'accountNumber'],
                checkData: company.value
            },
            'personalInfo': {
                fieldList: ['alipayName', 'phone', 'bank', 'identityNumber', 'accountNumber'],
                checkData: personal.value
            }
        }
        let isFLag = config[key]['fieldList'][fnName](field => {
            let _data = config[key]['checkData'][field];
            return (typeof _data == 'string' && _data.trim() == '') || _data == null; 
        });
        return isFLag;
    }

    const checkInfo = () => {
        // 检查公司账号信息
            // 全部为空
            let _isCompanyEveryEmpty = getCheckStatus('companyInfo', 'every');
            // 至少一项为空
            let _isCompanySomeEmpty = getCheckStatus('companyInfo', 'some');
          
        // 检查个人账号信息
            // 全部为空
            let _isPersonalEveryEmpty = getCheckStatus('personalInfo', 'every');
            // 至少一项为空
            let _isPersonalSomeEmpty = getCheckStatus('personalInfo', 'some');

        let result = {
            isFlag: true,
            isCompany: !_isCompanyEveryEmpty && !_isCompanySomeEmpty,
            isPersonal: !_isPersonalEveryEmpty && !_isPersonalSomeEmpty
        }

        if (_isCompanyEveryEmpty && _isPersonalEveryEmpty) {
            message.warning('公司账号信息和个人账号信息请至少填写一份！');
            result.isFlag = false;
        } 
        else if (_isPersonalEveryEmpty && _isCompanySomeEmpty) {
            message.warning('请完善公司账号相关信息！');
            result.isFlag = false;
        }
        else if (_isCompanyEveryEmpty && _isPersonalSomeEmpty) {
            message.warning('请完善个人账号相关信息！');
            result.isFlag = false;
        }
        else {
            if (!_isCompanyEveryEmpty && _isCompanySomeEmpty && !_isPersonalEveryEmpty && _isPersonalSomeEmpty) {
                message.warning('请完善公司账号信息和个人账号信息！');
                result.isFlag = false;
            }
            if (!_isPersonalEveryEmpty && !_isPersonalSomeEmpty && !_isCompanyEveryEmpty && _isCompanySomeEmpty) {
                message.warning('请完善公司账号相关信息！');
                result.isFlag = false;
            }
            if (!_isCompanyEveryEmpty && !_isCompanySomeEmpty && !_isPersonalEveryEmpty && _isPersonalSomeEmpty) {
                message.warning('请完善个人账号相关信息！');
                result.isFlag = false;
            }
        }
        return result;
    }

    const handleEditModalSave = async () => {
        let { isFlag, isCompany, isPersonal } = checkInfo();
        let requestList = [];

        if (isFlag) {
            // 公司账号信息
            if (isCompany) {
                let params = {
                    company: {
                        ...company.value,
                        userId: userId?.value || null
                    }
                }
                requestList.push(createUserAccountApi(params));
            }

            // 个人账号信息
            if (isPersonal) {
                let params = {
                    personal: {
                        ...personal.value,
                        userId: userId?.value || null
                    }
                }
                requestList.push(createUserAccountApi(params));
            }

            try {
                isEditModalLoading.value = true;
                if (requestList.length == 2) {
                    const [res1, res2] = await Promise.all(requestList);
                    if (res1.code == 200 && res2.code == 200) {
                        setEditModalShow(false);
                        message.success(res2.message);
                        tx(3);
                    }
                }
                else if (requestList.length == 1) {
                    const res = await requestList[0];
                    if (res.code == 200) {
                        setEditModalShow(false);
                        message.success(res.message);
                        tx(3);
                    }
                }
            } catch (err) {
                console.error(err);
            } finally {
                isEditModalLoading.value = false;
            }
        }
    }

    // 创建提现记录
    const createWithdrawaRecord = async () => {
        if (!accountType.value || accountType.value == -1) {
            return message.warning('未选择收款账号！');
        }
        try {
            isWithdrawModalLoading.value = true;
            let params = {
                appId: appInfoEnum.appId,
                tenantId: appInfoEnum.tenantId,
                userId: userId?.value || null,
                userAccountId: accountType.value
            }
            console.log('params', params);
            let res = await createWithdrawaRecordApi(params);
            if (res.code == 200) {
                message.success(res.message);
                setWithdrawModalShow(false);
                // 提现单号
                withdrawNo.value = res.withdrawNo;
                getWithdrawInfo(res => {
                    if (res.code == 200) {
                        let withdrawRecordData = res.list && res.list.length > 0 ? res.list[0] : {};
                        withdrawStatus.value = withdrawRecordData.withdrawStatus;
                        withdrawRefuseReason.value = withdrawRecordData?.refuseReason || null;
                        isWithdrawDetailModalOpen.value = true;
                    }
                    tx(2);
                });
            } else {
                message.error(res.message);
            }
        } catch (err) {
            console.error(err);
        } finally {
            isWithdrawModalLoading.value = false;
        }
    }

    // 获取提现信息
    const getWithdrawInfo = async callback => {
        try {
            let params = { 
                appId: appInfoEnum.appId,
                userId: userId?.value || null,
                id: withdrawNo.value
            }
            let res = await getWithdrawRecordListApi(params);
            callback && callback(res);
        } catch (err) {
            console.error(err);
        }
    }

    // 查看提现进度
    const seeWithdrawProgress = async data => {
        // 提现单号
        withdrawNo.value = data.id;
        currentAccountType.value = data.accountType

        getWithdrawInfo(res => {
            if (res.code == 200) {
                let withdrawRecordData = res.list && res.list.length > 0 ? res.list[0] : {};
                accountType.value = withdrawRecordData.accountType || -1;
                withdrawStatus.value = withdrawRecordData.withdrawStatus;
                withdrawRefuseReason.value = withdrawRecordData?.refuseReason || null;
                personalOtherData.value.withdrawAmount = withdrawRecordData.withdrawAmount;
                personalOtherData.value.arriveAmount = withdrawRecordData.arriveAmount;
                isWithdrawDetailModalOpen.value = true;
            }
        });
    }

     const handleSelectChangeAccount = () =>{
        accountList.value.map((item) => {
            if (item.id === accountType.value) {
                currentAccountType.value = item.accountType;
            }
        });
    }
</script>

<template>
<PageContainer style="background-color: #F0FAFD;">
    <div class="w-full p-20px">
        <div class="flex items-center cursor-pointer mb-20px" @click="$router.back(-1)">
            <LeftOutlined /><span class="text-20px ml-12px">我的推广</span>
        </div>
        <div class="my-promotion">
            <a-row style="margin-left: 20px;">
                <a-col :span="3">
                    <a-card shadow="always" class="summary-card">
                        <div class="summary-data">
                            <span style="color: #2062E5;">{{ info.orderNumber || 0 }}</span>
                        </div>
                        <div class="summary-desc">
                            <span>累计订单数</span>
                        </div>
                    </a-card>
                </a-col>

                <a-col :span="3" style="margin-left: 30px;">
                    <a-card shadow="always" class="summary-card">
                        <div class="summary-data">
                            <span style="color: #f5365c;">{{ info.withdrawableMoney || 0 }}￥</span>
                        </div>
                        <div class="summary-desc">
                            <span>可提现佣金</span>
                        </div>
                    </a-card>
                </a-col>

                <a-col :span="3" style="margin-left: 30px;">
                    <a-card shadow="always" class="summary-card">
                        <div class="summary-data">
                            <span style="color: #666666;">{{ info.withdrawnMoney || 0 }}￥</span>
                        </div>
                        <div class="summary-desc">
                            <span>已提现佣金</span>
                        </div>
                    </a-card>
                </a-col>

                <!-- 收款账号信息展示区域 -->
                <a-col :span="8" style="margin-left: 20px;">
                    <div v-if="hasCompany">
                        <a-row>
                            <span style="color: #666666;font-size: 12px;">
                                公司信息: {{ companyInfo.alipayName }}
                            </span>
                        </a-row>
                        <a-row style="margin-top: 10px;">
                            <span style="color: #666666;font-size: 12px;">
                                姓名: {{ companyInfo.bank }}
                            </span>
                        </a-row>
                        <a-row style="margin-top: 10px;">
                            <span style="color: #666666;font-size: 12px;">
                                银行卡号: {{ companyInfo.accountNumber }}
                            </span>
                        </a-row>
                    </div>

                    <div v-else-if="hasPersonal || hasCompanyAndPersonal">
                        <a-row>
                            <span style="color: #666666;font-size: 12px;">
                                姓名: {{ personalInfo.alipayName }}
                            </span>
                        </a-row>
                        <a-row style="margin-top: 10px;">
                            <span style="color: #666666;font-size: 12px;">
                                手机号: {{ personalInfo.phone }}
                            </span>
                        </a-row>
                        <a-row style="margin-top: 10px;">
                            <span style="color: #666666;font-size: 12px;">
                                纳税号: {{ personalInfo.identityNumber }}
                            </span>
                        </a-row>
                        <a-row style="margin-top: 10px;">
                            <span style="color: #666666;font-size: 12px;">
                                银行卡号: {{ personalInfo.accountNumber }}
                            </span>
                        </a-row>
                    </div>
                </a-col>

                <a-col :span="4">
                    <a-row>
                        <a-button type="primary" @click="handleEditModalOpen">
                            编辑
                        </a-button>
                    </a-row>
                    <a-row style="margin-top: 18px;" @click="handleWithdrawModalOpen">
                        <a-button type="primary">
                            提现
                        </a-button>
                    </a-row>
                </a-col>
            </a-row>

            <a-row>
                <p style="color: #f5365c;font-size: 12px;text-indent: 20px;margin-top: 10px;">
                    注：每月仅可提现一次，每月月中进行结算（用户当月5号之后发起的提现将于次月结算），若提现金额不够100元请先多去做推广。如有疑问，可联系管理KC: (微信：menglakeji20)
                </p>
            </a-row>

            <div class="tabs-wrap">
                <a-tabs type="card" v-model:activeKey="tabKey" :destroyInactiveTabPane="true">
                    <a-tab-pane key="5" tab="推广介绍">
                        <div style="width: 100%;background-color: rgba(0,0,0,.02)">
                            <img style="width: 100%;" src="@/assets/images/common/glz_promotion.png" alt="橄榄枝计划" />
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="1" tab="套餐管理">
                        <SetMealManage />
                    </a-tab-pane>

                    <a-tab-pane key="2" tab="拉新详情">
                        <PullNewDetail />
                    </a-tab-pane>

                    <a-tab-pane key="3" tab="佣金详情">
                        <CommissionDetail />
                    </a-tab-pane>

                    <a-tab-pane key="4" tab="提现记录">
                        <WithdrawalRecord @seeProgress="seeWithdrawProgress" />
                    </a-tab-pane>
                </a-tabs>
            </div>

            <!-- 编辑弹层 -->
            <a-modal
                v-model:open="isEditModalOpen"
                width="550px"
                :destroyOnClose="true"
                :keyboard="false"
                :maskClosable="false"
                @cancel="handleEditModalClose"
            >
                <div style="text-align: center;margin-bottom: 30px;">
                    <h3>收款账号信息编辑</h3>
                </div>

                <a-row>
                    <span style="color: #f5365c;;font-size: 14px;">
                        公司账号：需要提供发票；收到款后必须操作上传发票，否则影响后续提现。
                    </span>
                    <a-form 
                        :model="company" 
                        :label-col="{ span: 6 }" 
                        :wrapper-col="{ span: 12 }"
                        style="margin-top: 20px;"
                    >
                        <a-form-item label="公司信息">
                            <a-input 
                                style="width: 300px;" 
                                v-model:value="company.alipayName" 
                                placeholder="请输入公司信息" 
                            />
                        </a-form-item>
                        <a-form-item label="开户行与支行">
                            <a-input 
                                style="width: 300px;" 
                                v-model:value="company.bank" 
                                placeholder="请输入开户行与支行" 
                            />
                        </a-form-item>
                        <a-form-item label="银行卡">
                            <a-input 
                                style="width: 300px;"
                                v-model:value="company.accountNumber" 
                                placeholder="请输入银行卡"
                                @keyup.native="company.accountNumber=company.accountNumber.replace(/[^\d]/g,'')"
                            />
                        </a-form-item>
                    </a-form>

                    <span style="color: #f5365c;font-size: 14px;">
                        个人账号：无须提供发票，但要扣7.5%的税费，请以最终提现金额为准。
                    </span>
                    <a-form 
                        :model="personal" 
                        :label-col="{ span: 6 }" 
                        :wrapper-col="{ span: 12 }"
                        style="margin-top: 20px;"
                    >
                        <a-form-item label="姓名">
                            <a-input 
                                style="width: 300px;"
                                v-model:value="personal.alipayName" 
                                placeholder="请输入姓名" 
                            />
                        </a-form-item>
                        <a-form-item label="手机号">
                            <a-input 
                                style="width: 300px;" 
                                v-model:value="personal.phone" 
                                placeholder="请输入手机号" 
                                @keyup.native="personal.phone=personal.phone.replace(/[^\d]/g,'')"
                            />
                        </a-form-item>
                        <a-form-item label="开户行与支行">
                            <a-input 
                                style="width: 300px;"
                                v-model:value="personal.bank" 
                                placeholder="请输入开户行与支行" 
                            />
                        </a-form-item>
                        <a-form-item label="身份证">
                            <a-input 
                                style="width: 300px;"
                                v-model:value="personal.identityNumber" 
                                placeholder="请输入身份证"
                            />
                        </a-form-item>
                        <a-form-item label="银行卡号">
                            <a-input 
                                style="width: 300px;"
                                v-model:value="personal.accountNumber" 
                                placeholder="请输入银行卡号"
                                @keyup.native="personal.accountNumber=personal.accountNumber.replace(/[^\d]/g,'')"
                            />
                        </a-form-item>
                    </a-form>
                </a-row>

                <template #footer>
                    <div style="text-align: center;">
                        <a-button @click="setEditModalShow(false)">取消</a-button>
                        <a-button :loading="isEditModalLoading" type="primary" @click="handleEditModalSave">保存</a-button>
                    </div>
                </template>
            </a-modal>

            <!-- 提现弹层 -->
            <a-modal
                v-model:open="isWithdrawModalOpen"
                width="500px"
                :destroyOnClose="true"
                :keyboard="false"
                :maskClosable="false"
                @cancel="handleWithdrawModalClose"
            >
                <div style="text-align: center;margin-bottom: 30px;">
                    <h3>选择收款账号</h3>
                </div>

                <a-row>
                    <span style="margin-left: 60px;">选择收款账号</span>
                    <a-radio-group name="radioGroup" v-model:value="accountType" style="margin-left: 60px;" @change="handleSelectChangeAccount">
                        <a-radio 
                            v-for="(item, index) in accountList" 
                            :key="index" 
                            :value="item.id"
                        >
                            <span v-if="item.accountType == accountTypeEnum.COMPANY">公司账户</span>
                            <span v-else-if="item.accountType == accountTypeEnum.PERSONAL">个人账户</span>
                        </a-radio>
                    </a-radio-group>
                </a-row>

                <template #footer>
                    <div style="text-align: center;">
                        <a-button type="primary" :loading="isWithdrawModalLoading" @click="createWithdrawaRecord">下一步</a-button>
                    </div>
                </template>
            </a-modal>

            <!-- 提现详情弹层 -->
            <WithdrawDetailModal 
                v-model:isOpen="isWithdrawDetailModalOpen" 
                :accountType="accountType" 
                :withdrawNo="withdrawNo"
                :withdrawStatus="withdrawStatus" 
                :withdrawRefuseReason="withdrawRefuseReason"
                :personalInfo="personalInfo"
                :personalOtherData="personalOtherData"
                :companyInfo="companyInfo"
                :currentAccountType="currentAccountType"
                @updateWithdrawStatus="data => withdrawStatus = data"
                @updateWithdrawRefuseReason="data => withdrawRefuseReason = data"
            />
        </div>
    </div>
</PageContainer>
</template>

<style scoped lang="less">
    .my-promotion {
        height: 100%;
        padding: 20px 0px;
        background-color: #fff;
        /deep/ .ant-card.summary-card {
            text-align: center;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            .ant-card-body {
                padding: 15px;
                .summary-data {
                    font-size: 22px;
                }
                .summary-desc {
                    font-size: 14px;
                    color: #666;
                }
            }
        }

        /deep/ .tabs-wrap {
            margin-top: 10px;
            margin-left: 20px;
            border-radius: 4px;
            box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.05);
            .ant-tabs {
                .ant-tabs-nav {
                    margin: 0;
                    .ant-tabs-nav-wrap {
                        background: rgba(0, 0, 0, 0.04);
                        .ant-tabs-nav-list {
                            .ant-tabs-tab {
                                border-color: transparent;
                                border-radius: 0;
                                margin-left: 0;
                                background: transparent;
                                &.ant-tabs-tab-active {
                                    border-width: 1px;
                                    border-style: solid;
                                    border-color: rgba(5, 5, 5, 0.06) rgba(5, 5, 5, 0.06) #fff rgba(5, 5, 5, 0.06);
                                    background: #fff;
                                }
                            }
                        }
                    }
                }
                .ant-tabs-content-holder {
                    padding: 15px;
                }
            }
        }
    }
</style>