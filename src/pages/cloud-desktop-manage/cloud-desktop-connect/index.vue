<script setup lang="ts">
import {useRoute, useRouter} from 'vue-router'
import { connectInstanceSession, disconnectSession } from '~/api/common/cloud-desktop'
import {message} from 'ant-design-vue'
// 引入页面容器组件
import RemoteLayout from '~/layouts/remote-layout/index.vue'
import lodash from 'lodash'
// 引入需要使用的Ant Design图标
import {
    SettingOutlined,
    CheckOutlined,
    CopyOutlined,
    InteractionOutlined,
    ReloadOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    DisconnectOutlined,
    WarningOutlined,
    CheckCircleOutlined,
    UpOutlined,
    DownOutlined,
    ThunderboltOutlined,
    QuestionCircleOutlined
} from '@ant-design/icons-vue';

import KeyOutlinedIcon from '@/Icons/key.svg';
import {handleOperateElectron} from "~/utils/common-utils.ts";

const route = useRoute()
const router = useRouter()

const isMobile = ref(route.query.isMobile)

// 连接状态
const connectInfo = ref({
    ip: '',
    port: '',
    tunnelUrl: '',
    sessionId: ''
})
const connecting = ref(false)
const connected = ref(false)
const fullscreen = ref(false)
const guacDisplayRefs = ref(null)

// 性能指标 - 使用performance API获取更精确的时间测量
const frameRate = ref(0)
const frameCount = ref(0)
const lastFrameUpdate = ref(performance.now())
// 帧时间数组，用于计算平均帧率和方差
const frameTimes = ref<number[]>([])
// 最大保存帧时间数量，避免数组过大
const maxFrameTimesSaved = 120 // 增加采样数量以提高精度
// 帧率历史记录，用于滑动窗口平滑
const frameRateHistory = ref<number[]>([])
// 最大保存的帧率历史记录数量
const maxFrameRateHistorySaved = 10 // 增加历史记录数量以获得更平滑的结果
// 网络延迟（毫秒）
const networkLatency = ref(0)
// 最小/最大帧率记录
const minFrameRate = ref(9999)
const maxFrameRate = ref(0)
// 帧时间方差 - 用于评估帧率稳定性
const frameTimeVariance = ref(0)
// 性能面板显示开关
const showPerformancePanel = ref(false)
// 渲染统计
const totalFramesReceived = ref(0)
const renderStartTime = ref(performance.now())
// 当前帧间隔
const currentFrameInterval = ref(0)
// 帧率计算方法：simple(简单计数)、average(平均间隔)、weighted(加权平均)
const frameRateMethod = ref<'simple' | 'average' | 'weighted'>('weighted')

// 是否共享剪贴板
const toggleCopyToRemoteStatus = shallowRef(true);

// 连接参数
const connetId = ref(route.query.id as string)
const instanceId = ref(route.query.instanceId as string)
const workspaceId = ref(route.query.workspaceId as string)
const terminalType = ref(route.query.terminalType as string)
const instanceName = ref(route.query.desktopName as string)
const connectType = ref(route.query.connectType as string || 'local') // local 或 network

// 音频状态
const audioEnabled = ref(true)
const audioPlayers = ref<Guacamole.AudioPlayer[]>([])

// GPU加速设置
const gpuAcceleration = ref(true) // 默认开启GPU加速
const gpuAccelerationAvailable = ref(false) // 是否支持GPU加速
const gpuInfo = ref('') // GPU信息
const renderMode = ref('auto') // 渲染模式：auto, gpu, cpu

// 剪贴板状态
const clipboardContent = ref('')
const showClipboardNotification = ref(false)
// 添加剪贴板下拉菜单可见状态控制
const clipboardDropdownVisible = ref(false)

// Guacamole 客户端
const guacClient = ref<Guacamole.Client>(null)
const guacDisplay = ref<Guacamole.Display>(null)
const guacDisplayElement = ref<HTMLElement>(null)
const keyboardControl = ref<Guacamole.Keyboard>(null)
const mouseControl = ref<Guacamole.Mouse>(null)

const isInitAudioResume = shallowRef(false)

// 连接错误信息
const errorMessage = ref('')

// 添加新的响应式变量来控制显示比例
const scale = ref(1) // 默认缩放比例
const fitToScreen = ref(true) // 是否自动适应屏幕
const nativeWidth = ref(1024) // 原始宽度
const nativeHeight = ref(768) // 原始高度
const optimalResolution = ref(96) // 画质 DPI
// 添加显示偏移量变量，用于坐标映射
const displayOffsetX = ref(0)
const displayOffsetY = ref(0)
const clipboardText = shallowRef('')

// 添加预设分辨率选项
const resolutionOptions = ref([
    {label: '1024 × 768', value: '1024x768'},
    {label: '1280 × 720', value: '1280x720'},
    // {label: '1366 × 768', value: '1366x768'},
    {label: '1440 × 900', value: '1440x900'},
    {label: '1600 × 900', value: '1600x900'},
    {label: '1920 × 1080', value: '1920x1080'},
    // {label: '2560 × 1440', value: '2560x1440'},
    {label: '适应窗口大小', value: 'auto'}
])

// 当前选中的分辨率
const selectedResolution = ref('auto')

// 在页面顶部添加鼠标处理器存储
const mouseHandlers = ref<Array<{ element: HTMLElement, eventType: string, listener: any }>>([])

// 在script开头添加调试标志
const showDebugInfo = shallowRef(false)

// 添加头部控制栏展开/收起状态变量
const headerCollapsed = ref(true)
const isToolbarFixed = ref(false) // 新增：用于标记工具栏是否固定展开

// 添加定时器变量，用于防抖处理
let mouseEnterToolbarTimer: ReturnType<typeof setTimeout> | null = null;
let mouseLeaveToolbarTimer: ReturnType<typeof setTimeout> | null = null;

// 设置延迟时间（毫秒）
const expandDelay = 200; // 鼠标移入后多少毫秒展开
const collapseDelay = 300; // 鼠标移出后多少毫秒收起

// 处理鼠标进入工具栏
const handleMouseEnterToolbar = () => {
    if (isToolbarFixed.value) return; // 如果已固定，则不处理悬停

    // 清除可能存在的离开定时器
    if (mouseLeaveToolbarTimer) {
        clearTimeout(mouseLeaveToolbarTimer);
        mouseLeaveToolbarTimer = null;
    }

    // 如果已经展开了，就不用再设置定时器了
    if (!headerCollapsed.value) return;

    // 设置延迟展开定时器
    mouseEnterToolbarTimer = setTimeout(() => {
        headerCollapsed.value = false;
    }, expandDelay);
};

// 处理鼠标离开工具栏
const handleMouseLeaveToolbar = () => {
    if (isToolbarFixed.value) return; // 如果已固定，则不处理悬停

    // 清除可能存在的进入定时器
    if (mouseEnterToolbarTimer) {
        clearTimeout(mouseEnterToolbarTimer);
        mouseEnterToolbarTimer = null;
    }

    // 如果已经收起了，就不用再设置定时器了
    if (headerCollapsed.value) return;

    // 设置延迟收起定时器
    mouseLeaveToolbarTimer = setTimeout(() => {
        headerCollapsed.value = true;
    }, collapseDelay);
};

// 切换头部折叠状态
const toggleHeader = () => {
    // 切换固定状态
    isToolbarFixed.value = !isToolbarFixed.value;

    // 清除可能存在的悬停定时器，避免冲突
    if (mouseEnterToolbarTimer) {
        clearTimeout(mouseEnterToolbarTimer);
        mouseEnterToolbarTimer = null;
    }
    if (mouseLeaveToolbarTimer) {
        clearTimeout(mouseLeaveToolbarTimer);
        mouseLeaveToolbarTimer = null;
    }

    headerCollapsed.value = !isToolbarFixed.value;
}

// 获取连接信息并连接
const fetchConnectionInfo = async (flag: boolean = false) => {
    if (!workspaceId.value) {
        errorMessage.value = '缺少实例ID，无法连接'
        return
    }

    if (connected.value) await disconnect(flag);

    connecting.value = true
    errorMessage.value = '' // 清除错误信息

    let optimalScreenWidth = window.innerWidth;
    let optimalScreenHeight = window.innerHeight;
    if(selectedResolution.value != 'auto'){
        let resolution = selectedResolution.value.split('x');
        optimalScreenWidth = Number(resolution[0]);
        optimalScreenHeight = Number(resolution[1]);
    }

    try {
         // 调用实际API获取连接信息 - 调整为符合新API的请求参数
         const res = await connectInstanceSession({
                    workspaceId: Number(workspaceId.value),
                    terminalType: terminalType.value || "WEB",
                    usiId: connetId.value,
                    clientInfo: JSON.stringify({
                      userAgent: navigator.userAgent,
                      platform: navigator.platform,
                      screenWidth: optimalScreenWidth,
                      screenHeight: optimalScreenHeight,
                    })
                })

                // 如果API调用成功，使用返回的连接信息 - 调整字段名称
                if (res.success && res.payload) {
                    connectInfo.value = { ...res.payload };
                    const { tunnelUrl, ip, port, sessionId } = res.payload
                    console.log('成功获取连接信息', res.payload)
                    let connectWsUrl = `wss://${ip}${tunnelUrl}`;
                    await initGuacamoleClient(connectWsUrl, sessionId, optimalScreenWidth, optimalScreenHeight)
                    return
                } else {
                    throw new Error(res.message || '获取连接信息失败')
                }
    } catch (error: any) {
        console.error('获取连接信息失败', error)
        message.error(`获取连接信息失败: ${error.message || '未知错误'}`)
        errorMessage.value = `获取连接信息失败，请稍后重试: ${error.message || '未知错误'}`
        connecting.value = false
    }
}

// 添加支持的音频、视频和图片MIME类型变量
const audioMimeTypes = ref<string[]>([])
const videoMimeTypes = ref<string[]>([])
const imageMimeTypes = ref<string[]>([])
const timezone = ref<string>('')

// 检测设备支持的媒体MIME类型
const detectSupportedMediaTypes = () => {
    try {
        // 使用Guacamole API获取支持的音频类型
        if (window.Guacamole && window.Guacamole.AudioPlayer) {
            try {
                // 尝试使用Guacamole的API获取支持的音频类型
                // @ts-ignore - Guacamole.AudioPlayer.getSupportedTypes 存在于运行时
                audioMimeTypes.value = window.Guacamole?.AudioPlayer?.getSupportedTypes();
                if(audioMimeTypes.value.length <= 0){
                    audioMimeTypes.value = ['audio/L8'];
                }
                console.log('Guacamole支持的音频类型:', audioMimeTypes.value);
            } catch (e) {
                console.warn('无法获取Guacamole支持的音频类型:', e);
                // 回退到常见类型检测
                audioMimeTypes.value = ['audio/L8'];
            }
        }

        // 使用Guacamole API获取支持的视频类型
        if (window.Guacamole && window.Guacamole.VideoPlayer) {
            try {
                // 尝试使用Guacamole的API获取支持的视频类型
                // @ts-ignore - Guacamole.VideoPlayer.getSupportedTypes 存在于运行时
                videoMimeTypes.value = window.Guacamole.VideoPlayer.getSupportedTypes();
                if(videoMimeTypes.value.length <= 0){
                    videoMimeTypes.value = ['video/VP8'];
                }
                console.log('Guacamole支持的视频类型:', videoMimeTypes.value);
            } catch (e) {
                console.warn('无法获取Guacamole支持的视频类型:', e);
                // 回退到常见类型检测
                const videoTypes = [
                    'video/webm', 'video/webm;codecs=vp8', 'video/webm;codecs=vp9',
                    'video/mp4', 'video/h264', 'video/ogg', 'video/x-matroska'
                ];
                videoMimeTypes.value = videoTypes.filter(type => {
                    try {
                        return MediaRecorder.isTypeSupported(type);
                    } catch (e) {
                        return false;
                    }
                });
            }
        }

        // 检测图片支持类型 - 常见支持格式
        imageMimeTypes.value = [
            'image/jpeg', 'image/png'
        ];

        // 获取当前时区
        timezone.value = Intl.DateTimeFormat().resolvedOptions().timeZone;

        console.log('检测到支持的媒体类型:', {
            audio: audioMimeTypes.value,
            video: videoMimeTypes.value,
            image: imageMimeTypes.value,
            timezone: timezone.value
        });
    } catch (error) {
        console.error('检测媒体类型失败:', error);
        // 确保至少有一些默认值
        audioMimeTypes.value = ['audio/L16'];
        videoMimeTypes.value = ['video/VP8'];
        imageMimeTypes.value = ['image/jpeg'];
    }
}

// 检测是否为移动设备 - 使用更严谨的方法
const isMobileDevice = () => {
    // 使用多种方法综合判断
    // 1. 检查用户代理字符串
    const userAgentCheck = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|Tablet/i.test(navigator.userAgent);

    // 2. 检查媒体查询 - 更可靠的方法
    const mediaQueryCheck = window.matchMedia('(max-width: 767px), (pointer: coarse)').matches;

    // 3. 检查触摸点数量 - 触摸设备特有
    const touchCheck = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // 4. 检查屏幕尺寸
    const screenCheck = window.innerWidth <= 768 || window.innerHeight <= 768;

    // 综合判断：至少满足两个条件才认为是移动设备
    const conditions = [userAgentCheck, mediaQueryCheck, touchCheck, screenCheck];
    const trueCount = conditions.filter(Boolean).length;

    return trueCount >= 2;
};

// 初始化 Guacamole 客户端
const initGuacamoleClient = async (wsUrl: string, sessionToken: string, optimalScreenWidth = window.screen.width, optimalScreenHeight = window.screen.height) => {
    // 尝试恢复AudioContext - 修复音频问题
    // try {
    //     // 确保Guacamole已加载
    //     if (window.Guacamole && window.Guacamole.AudioContextFactory) {
    //         // 获取或创建AudioContext
    //         const audioContext = window.Guacamole.AudioContextFactory.getAudioContext()

    //         // 如果AudioContext处于暂停状态，尝试恢复它
    //         if (audioContext && audioContext.state === 'suspended') {
    //             console.log('初始化时恢复 AudioContext')
    //             audioContext.resume().then(() => {
    //                 console.log('AudioContext 已成功恢复:', audioContext.state)
    //             }).catch((err: Error) => {
    //                 console.error('恢复 AudioContext 失败:', err)
    //             })
    //         }
    //     }
    // } catch (e) {
    //     console.error('初始化 AudioContext 出错:', e)
    // }

    // 构建连接参数 - 使用Guacamole支持的参数格式
    let connectionString = `session=${sessionToken}`;

    if(optimalScreenWidth) {
        connectionString += `&optimalScreenWidth=${optimalScreenWidth}`;
    }
    if(optimalScreenHeight) {
        connectionString += `&optimalScreenHeight=${optimalScreenHeight}`;
    }

    if(optimalResolution.value){
        connectionString += `&optimalResolution=${optimalResolution.value}`
    }

    const token = useAuthorization();
    if(token) {
       connectionString += `&token=${token.value}`
    }
    // 添加音频MIME类型 - 使用Guacamole支持的参数名称 audio_mimetypes
    if (audioMimeTypes.value.length > 0) {
        connectionString += `&audioMimetypes=${encodeURIComponent(audioMimeTypes.value.join(','))}`
    }

    // 添加视频MIME类型 - 使用Guacamole支持的参数名称 video_mimetypes
    if (videoMimeTypes.value.length > 0) {
        connectionString += `&videoMimetypes=${encodeURIComponent(videoMimeTypes.value.join(','))}`
    }

    // 添加图片MIME类型 - 使用Guacamole支持的参数名称 image_mimetypes
    if (imageMimeTypes.value.length > 0) {
        connectionString += `&imageMimetypes=${encodeURIComponent(imageMimeTypes.value.join(','))}`
    }

    // 添加时区信息 - 使用Guacamole支持的参数名称 timezone
    if (timezone.value) {
        connectionString += `&timezone=${encodeURIComponent(timezone.value)}`
    }

    // 如果支持GPU加速且用户启用了该功能，添加相关参数
    // if (gpuAccelerationAvailable.value && gpuAcceleration.value) {
    //     connectionString += `&render-mode=${renderMode.value}`
    // }

    console.log('连接参数:', connectionString)

    try {
        // 创建 WebSocket 隧道
        const tunnel = new window.Guacamole.WebSocketTunnel(wsUrl)

        // 添加 tunnel 的错误处理
        tunnel.onerror = function (status: any) {
            console.error("WebSocket tunnel 错误:", status)
            if(status?.code == 3410){
                errorMessage.value = `连接已断开【当前云电脑正在被其他用户使用，请稍后再试】`
            } else if(status?.code == 3411) {
                errorMessage.value = `连接已断开【远程电脑长时间未操作，系统已自动断开连接】`
            } else {
                errorMessage.value = `连接错误 (${status.code}): 【${status.message || '未知错误，请稍后重试'}】`;
            }
            connecting.value = false
            connected.value = false
        }

        // 创建 Guacamole 客户端
        guacClient.value = new window.Guacamole.Client(tunnel)

        // 配置GPU加速选项 - 使用try/catch避免类型错误
        try {
            // @ts-ignore - Guacamole.Display可能有静态方法setUseWebGL
            if (window.Guacamole && window.Guacamole.Display && typeof window.Guacamole.Display.setUseWebGL === 'function') {
                // @ts-ignore - 忽略类型检查
                window.Guacamole.Display.setUseWebGL(gpuAcceleration.value)
                console.log(`WebGL渲染已${gpuAcceleration.value ? '启用' : '禁用'}`)
            }
        } catch (e) {
            console.warn('配置WebGL加速时出错:', e)
        }

        // 获取显示元素
        guacDisplay.value = guacClient.value.getDisplay();

        guacDisplayElement.value = guacDisplay.value.getElement()
        console.log('guacDisplayElement.value', guacDisplay.value);
        console.log('guacDisplayElement.value', guacDisplayElement.value);
        // 尝试配置渲染模式 - 使用try/catch避免类型错误


        // 将显示元素添加到容器中
        if (guacDisplayRefs.value) {
            // 确保容器是空的
            while (guacDisplayRefs.value.firstChild) {
                guacDisplayRefs.value.removeChild(guacDisplayRefs.value.firstChild)
            }
            // 添加新的显示元素
            guacDisplayRefs.value.appendChild(guacDisplayElement.value)
        } else {
            console.warn('显示容器引用不存在')
        }
    } catch (error: any) {
        console.error('初始化 Guacamole 客户端失败', error)
        message.error(`初始化远程连接客户端失败: ${error.message || '未知错误'}`)
        errorMessage.value = `初始化远程连接客户端失败，请确保已加载 Guacamole 客户端库: ${error.message || '未知错误'}`
        connecting.value = false
    }

    // 绑定事件处理器 - 先绑定事件再连接
    bindGuacamoleEvents()

    console.log('准备连接 Guacamole 客户端', {sessionToken, gpuAcceleration: gpuAcceleration.value})

    // 连接 - 确保这里传递的参数格式正确
    guacClient.value.connect(connectionString)
}

// 自定义鼠标事件处理 - 应用坐标映射
const handleMouseEvent = (mouseState: any) => {
  if (!guacClient.value || !connected.value) return
  // 标记为内部坐标，来自Guacamole的Mouse对象
  mouseState._isRelative = true
  // 应用坐标映射计算实际位置
  const mappedState = applyCoordinateMapping(mouseState)
  // 发送映射后的鼠标状态到远程桌面
  guacClient.value.sendMouseState(mappedState)
}

// 绑定 Guacamole 事件
const bindGuacamoleEvents = () => {
    if (!guacClient.value) return

    //🚀 初始化鼠标控制 - 直接绑定到Guacamole元素上
    if (isMobile.value || isMobileDevice()) {
      mouseControl.value = new window.Guacamole.Mouse.Touchscreen(guacClient.value.getDisplay().getElement());
    } else {
      mouseControl.value = new window.Guacamole.Mouse(guacClient.value.getDisplay().getElement())
    }
      // 先清除可能存在的事件处理器
      mouseControl.value.onmousedown = null
      mouseControl.value.onmousemove = null
      mouseControl.value.onmouseup = null
      // 为所有鼠标事件绑定同一个处理函数
      mouseControl.value.onmousedown = handleMouseEvent
      mouseControl.value.onmousemove = handleMouseEvent
      mouseControl.value.onmouseup = handleMouseEvent
      // 获取显示元素并绑定鼠标进入离开事件
      guacDisplayElement.value = guacClient.value.getDisplay().getElement()
      // 绑定鼠标进入离开监听器
      bindMouseListeners()

    // 初始化键盘控制
    keyboardControl.value = new window.Guacamole.Keyboard(document)

    keyboardControl.value.onkeydown = (keysym: any) => {
        if(keysym) {
            // 处理键盘事件
            console.log('键盘按下:', keysym)
            guacClient.value.sendKeyEvent(1, keysym)
        }
    }

    keyboardControl.value.onkeyup = (keysym: any) => {
        if(keysym) {
            // 处理键盘事件
            console.log('键盘释放:', keysym)
            guacClient.value.sendKeyEvent(0, keysym)
        }
    }

    // 添加音频支持
    guacClient.value.onaudio = (stream: any, mimetype: string) => {
        console.log('添加音频支持', stream, mimetype)
        if (!audioEnabled.value) return null

        // 尝试恢复AudioContext - 修复音频问题
        try {
            const audioContext = window.Guacamole.AudioContextFactory.singleton
            if (audioContext && audioContext.state === 'suspended') {
                console.log('正在恢复 AudioContext')
                audioContext.resume().then(() => {
                    console.log('AudioContext 已恢复:', audioContext.state)
                }).catch((err: Error) => {
                    console.error('恢复 AudioContext 失败:', err)
                })
            }
        } catch (e) {
            console.error('访问 AudioContext 出错:', e)
        }

        // 尝试获取合适的音频播放器
        const audioPlayer = window.Guacamole.AudioPlayer.getInstance(stream, mimetype)
        console.log('audioPlayer', audioPlayer);
        if (audioPlayer) {
            audioPlayers.value.push(audioPlayer)

            // 播放音频
            audioPlayer.sync()

            // 返回播放器实例
            return audioPlayer
        } else {
            console.warn('不支持的音频格式:', mimetype)
            return null
        }
    }

    // 添加剪贴板支持
    guacClient.value.onclipboard = (stream: any, mimetype: string) => {
        if (mimetype === 'text/plain') {
            // 创建字符串读取器
            const reader = new window.Guacamole.StringReader(stream)

            // 接收文本内容
            let content = ''

            // 当收到数据时添加到内容中
            reader.ontext = (text: string) => {
                content += text
            }

            // 当流结束时处理完整的剪贴板内容
            reader.onend = () => {
                console.log('剪贴板内容', content)
                if (content) {
                    // 更新本地剪贴板内容
                    clipboardContent.value = content

                    // 显示通知
                    showClipboardNotification.value = true
                    setTimeout(() => {
                        showClipboardNotification.value = false
                    }, 3000)

                    if(toggleCopyToRemoteStatus.value) {
                        // 可选：自动复制到本地剪贴板
                        copyToLocalClipboard(content)
                    }
                }
            }
        }
    }

    // 帧率计算 - 使用MutationObserver监测DOM变化来计算帧率
    let observer = new MutationObserver(() => {
        frameCount.value++
        totalFramesReceived.value++
        const now = performance.now() // 使用performance.now()获取更高精度的时间戳

        // 记录每一帧的时间
        frameTimes.value.push(now)

        // 计算当前帧间隔
        if (frameTimes.value.length > 1) {
            const lastIndex = frameTimes.value.length - 1
            currentFrameInterval.value = frameTimes.value[lastIndex] - frameTimes.value[lastIndex - 1]
        }

        // 只保留最近的帧时间记录，实现滑动窗口
        if (frameTimes.value.length > maxFrameTimesSaved) {
            frameTimes.value.shift()
        }

        // 每秒更新一次帧率
        if (now - lastFrameUpdate.value >= 1000) {
            // 1. 简单计算: 每秒收到的帧数
            const elapsedSeconds = (now - lastFrameUpdate.value) / 1000
            const simpleFrameRate = Math.round(frameCount.value / elapsedSeconds)

            // 2. 计算平均帧间隔时间来获得更精确的帧率
            let smoothFrameRate = simpleFrameRate
            let finalFrameRate = simpleFrameRate

            if (frameTimes.value.length > 1) {
                // 计算帧间隔时间（毫秒）
                const frameIntervals = []
                for (let i = 1; i < frameTimes.value.length; i++) {
                    frameIntervals.push(frameTimes.value[i] - frameTimes.value[i - 1])
                }

                // 过滤异常值（使用四分位距IQR方法检测异常值）
                const sortedIntervals = [...frameIntervals].sort((a, b) => a - b)
                const q1Index = Math.floor(sortedIntervals.length * 0.25)
                const q3Index = Math.floor(sortedIntervals.length * 0.75)
                const q1 = sortedIntervals[q1Index]
                const q3 = sortedIntervals[q3Index]
                const iqr = q3 - q1
                const lowerBound = q1 - 1.5 * iqr
                const upperBound = q3 + 1.5 * iqr

                const filteredIntervals = frameIntervals.filter(
                    interval => interval >= lowerBound && interval <= upperBound && interval < 1000
                )

                if (filteredIntervals.length > 0) {
                    // 计算平均间隔时间
                    const avgInterval = filteredIntervals.reduce((sum, interval) => sum + interval, 0) / filteredIntervals.length

                    // 计算帧时间方差 - 评估帧率稳定性
                    const variance = filteredIntervals.reduce((sum, interval) => {
                        const diff = interval - avgInterval
                        return sum + (diff * diff)
                    }, 0) / filteredIntervals.length
                    frameTimeVariance.value = Math.round(variance)

                    // 估算网络延迟（基于帧间隔和方差）
                    const estimatedNetworkDelay = avgInterval * (0.4 + (Math.min(variance, 10000) / 20000))
                    networkLatency.value = Math.round(estimatedNetworkDelay)

                    // 通过平均间隔时间计算帧率
                    smoothFrameRate = Math.round(1000 / avgInterval)

                    // 根据选择的计算方法确定最终帧率
                    switch (frameRateMethod.value) {
                        case 'simple':
                            finalFrameRate = simpleFrameRate
                            break
                        case 'average':
                            finalFrameRate = smoothFrameRate
                            break
                        case 'weighted':
                            // 加权平均：结合简单帧率和平滑帧率，权重根据方差动态调整
                            const varianceFactor = Math.min(1, variance / 10000)
                            const simpleWeight = 0.3 + (varianceFactor * 0.4) // 方差大时增加简单计算权重
                            const smoothWeight = 1 - simpleWeight
                            finalFrameRate = Math.round(simpleFrameRate * simpleWeight + smoothFrameRate * smoothWeight)
                            break
                    }
                }
            }

            // 更新最小/最大帧率记录
            if (finalFrameRate < minFrameRate.value && finalFrameRate > 0) {
                minFrameRate.value = finalFrameRate
            }
            if (finalFrameRate > maxFrameRate.value) {
                maxFrameRate.value = finalFrameRate
            }

            // 将当前帧率添加到历史记录
            frameRateHistory.value.push(finalFrameRate)

            // 只保留最近的帧率历史
            if (frameRateHistory.value.length > maxFrameRateHistorySaved) {
                frameRateHistory.value.shift()
            }

            // 应用指数加权移动平均(EWMA)获得更平滑的帧率显示
            if (frameRateHistory.value.length > 0) {
                // 计算加权系数 - 最近的帧率权重更高
                const alpha = 0.3 // 平滑因子，值越小结果越平滑
                let ewma = frameRateHistory.value[0]

                for (let i = 1; i < frameRateHistory.value.length; i++) {
                    ewma = alpha * frameRateHistory.value[i] + (1 - alpha) * ewma
                }

                frameRate.value = Math.round(ewma)
            } else {
                frameRate.value = finalFrameRate
            }

            // 重置计数和时间
            frameCount.value = 0
            lastFrameUpdate.value = now

            // 输出调试信息
            if (showDebugInfo.value) {
                console.debug('帧率计算:', {
                    method: frameRateMethod.value,
                    fps: frameRate.value,
                    variance: frameTimeVariance.value,
                    latency: networkLatency.value,
                    interval: currentFrameInterval.value
                })
            }
        }
    })

    // 观察DOM变化来计算帧率
    observer.observe(guacDisplayElement.value, {
        childList: true,
        subtree: true,
        attributes: true
    })

    // 状态变化事件
    guacClient.value.onstatechange = (state: any) => {
        console.log('Guacamole 连接状态变更:', state)
        switch (state) {
            case 0: // 空闲
            case 1: // 断开
                console.log('连接状态: 空闲')
                connecting.value = false
                connected.value = false
                break
            case 2: // 连接中
                console.log('连接状态: 连接中')
                connecting.value = true
                connected.value = false
                break
            case 3: // 已连接
                console.log('连接状态: 已连接')
                connecting.value = false
                connected.value = true
                // 清除错误信息
                errorMessage.value = ''
                // 初始化自适应显示大小
                handleConnectSuccess()
                break
            case 4: // 断开连接
            case 5: // 断开连接
                connecting.value = false
                connected.value = false
                break
            default:
                break
        }
    }

    // 错误事件
    guacClient.value.onerror = (error: any) => {
        console.error('Guacamole 错误:', error)
        message.error(`远程连接错误: ${error.message}`)
        if(error.code == 3410){
            errorMessage.value = `连接失败【其他用户正在远程，请稍后重试】`
        } else if(error.code == 519) {
            errorMessage.value = `连接失败: ${error.message || '未知错误'}`
        }

        connecting.value = false
        connected.value = false
    }
}

/**
 * 应用坐标映射：将显示坐标转换为远程桌面坐标
 * 考虑缩放比例、偏移量及容器边界
 */
const applyCoordinateMapping = (mouseState: any) => {
    if (!guacDisplayElement.value || !connected.value) return mouseState

    const originalX = mouseState.x
    const originalY = mouseState.y

    try {
        // 获取当前缩放比例
        const currentScale = scale.value

        // 获取精确的容器和显示元素位置信息 - 使用 DOMRect 获取更准确的位置
        const displayRect = guacDisplayElement.value.getBoundingClientRect()
        const containerRect = guacDisplayRefs.value.getBoundingClientRect()

        // 获取当前显示偏移量
        const currentOffsetX = displayOffsetX.value || 0
        const currentOffsetY = displayOffsetY.value || 0

        // 获取远程桌面原始尺寸
        const remoteWidth = nativeWidth.value
        const remoteHeight = nativeHeight.value

        // 计算有效显示区域的边界（考虑缩放和偏移）
        const scaledWidth = remoteWidth * currentScale
        const scaledHeight = remoteHeight * currentScale

        // 获取元素相对于视口的绝对位置
        const elementLeft = displayRect.left
        const elementTop = displayRect.top
        const containerLeft = containerRect.left
        const containerTop = containerRect.top

        // 计算鼠标相对于显示元素左上角的精确位置
        let relativeX, relativeY

        // 关键修复：检查当前鼠标位置是否已经是相对于显示元素的坐标
        // 如果使用的是Mouse对象内部坐标，则需要考虑滚动和页面定位
        const isInternalCoordinate = mouseState._isRelative

        if (isInternalCoordinate) {
            // 已经是相对坐标，不需要进一步调整
            relativeX = originalX
            relativeY = originalY
        } else {
            // 使用精确的客户端坐标计算相对于显示元素的位置
            // 注意：这里需要考虑显示元素的绝对位置和偏移
            if (isMobile.value || isMobileDevice()) {
              relativeX = originalX
              relativeY = originalY
            } else {
              relativeX = originalX - (elementLeft - containerLeft)
              relativeY = originalY - (elementTop - containerTop)
            }
        }

        // 检查鼠标是否在有效区域内
        if (relativeX < 0 || relativeX > scaledWidth ||
            relativeY < 0 || relativeY > scaledHeight) {
            // 如果鼠标超出有效区域，进行边界限制
            const boundedX = Math.max(0, Math.min(relativeX, scaledWidth))
            const boundedY = Math.max(0, Math.min(relativeY, scaledHeight))

            // 记录调试信息
            console.debug(`鼠标超出显示区域，应用边界限制: (${relativeX}, ${relativeY}) -> (${boundedX}, ${boundedY})`)

            // 更新相对坐标值
            relativeX = boundedX
            relativeY = boundedY
        }

        // 计算实际远程坐标（除以缩放比例）
        const mappedX = relativeX / currentScale
        const mappedY = relativeY / currentScale

        // 确保坐标在远程桌面范围内
        const finalX = Math.max(0, Math.min(remoteWidth, mappedX))
        const finalY = Math.max(0, Math.min(remoteHeight, mappedY))

        // 创建新的鼠标状态
        const newState = {...mouseState}
        newState.x = Math.floor(finalX)
        newState.y = Math.floor(finalY)
        // 标记为已处理的相对坐标
        newState._isRelative = true

        // // 添加详细的坐标映射日志（调试用）
        // if (mouseState.left || mouseState.middle || mouseState.right) {
        //     console.debug(
        //         `鼠标映射: [${isInternalCoordinate ? '内部' : '外部'}] (${originalX}, ${originalY}) -> ` +
        //         `(${finalX.toFixed(1)}, ${finalY.toFixed(1)}) ` +
        //         `[元素位置: ${elementLeft.toFixed(0)}x${elementTop.toFixed(0)}, ` +
        //         `偏移: ${currentOffsetX}x${currentOffsetY}, ` +
        //         `缩放: ${currentScale.toFixed(2)}, ` +
        //         `分辨率: ${remoteWidth}x${remoteHeight}]`
        //     )
        // }

        return newState
    } catch (error) {
        console.error('鼠标坐标映射失败:', error)
        return mouseState // 出错时返回原始状态
    }
}

/**
 * 添加触摸支持，处理触摸事件并转换为鼠标事件
 * 使用精确的位置计算，包含边界处理和偏移计算
 */
const setupTouchHandlers = () => {
  if (!guacDisplayElement.value || !mouseControl.value) return

  // 清除之前的处理器
  clearTouchHandlers()

  const element = guacDisplayElement.value
  const container = guacDisplayRefs.value
  if (!container) return

  // 触摸状态变量
  const touchState = {
    lastTouchX: 0,
    lastTouchY: 0,
    lastX: 0,
    lastY: 0,
    isTouching: false,
    verticalAccumulator: 0,
    horizontalAccumulator: 0
  }

  // 滚动配置
  const scrollConfig = {
    threshold: 30,           // 滚动阈值
    speedFactor: 0.8,        // 速度因子
    smoothingFactor: 0.6,    // 平滑因子
    maxSteps: 3,             // 最大滚动步数
    maxHistoryLength: 5      // 历史记录长度
  }

  // 滚动历史记录
  const scrollHistory = {
    vertical: [] as number[],
    horizontal: [] as number[]
  }

  // 计算相对坐标
  const calculateRelativeCoordinates = (touch: Touch) => {
    const containerRect = container.getBoundingClientRect()
    const containerRelativeX = touch.clientX - containerRect.left
    const containerRelativeY = touch.clientY - containerRect.top

    return {
      x: containerRelativeX - displayOffsetX.value,
      y: containerRelativeY - displayOffsetY.value
    }
  }

  // 发送鼠标状态
  const sendMouseState = (state: any) => {
    if (!guacClient.value || !connected.value) return

    const mappedState = applyCoordinateMapping(state)
    guacClient.value.sendMouseState(mappedState)
    return mappedState
  }

  // 优化：添加平滑滚动函数
  const smoothScrollValue = (newValue: number, history: number[]) => {
    // 添加新值到历史
    history.push(newValue)

    // 保持历史记录在限定长度内
    if (history.length > scrollConfig.maxHistoryLength) {
      history.shift()
    }

    // 如果历史记录为空，直接返回新值
    if (history.length === 0) return newValue

    // 计算加权平均值，最新的值权重最高
    let totalWeight = 0
    let weightedSum = 0

    for (let i = 0; i < history.length; i++) {
      // 指数加权，越新的值权重越高
      const weight = Math.pow(scrollConfig.smoothingFactor, history.length - i - 1)
      weightedSum += history[i] * weight
      totalWeight += weight
    }

    return weightedSum / totalWeight
  }

  // 处理滚动
  const handleScroll = (relativeCoords: {x: number, y: number}, isVertical: boolean, isPositive: boolean) => {
    const history = isVertical ? scrollHistory.vertical : scrollHistory.horizontal
    const accumulator = isVertical ?
      touchState.verticalAccumulator :
      touchState.horizontalAccumulator

    // 平滑处理累加器值
    const smoothedValue = smoothScrollValue(Math.abs(accumulator), history)

    // 计算滚动步数
    const scrollSteps = Math.floor(smoothedValue / scrollConfig.threshold)
    const effectiveSteps = Math.min(scrollSteps, scrollConfig.maxSteps)

    // 如果是水平滚动，需要按下Shift键
    if (!isVertical && guacClient.value) {
      guacClient.value.sendKeyEvent(1, 0xFFE1) // 按下Shift键
    }

    // 执行滚动
    for (let i = 0; i < effectiveSteps; i++) {
      const scrollState = {
        x: relativeCoords.x,
        y: relativeCoords.y,
        left: false,
        middle: false,
        right: false,
        up: isVertical ? !isPositive : !isPositive,
        down: isVertical ? isPositive : isPositive,
        _isRelative: false
      }

      // 发送鼠标按下事件
      const mappedState = sendMouseState(scrollState)

      // 立即发送鼠标抬起事件
      if (mappedState && guacClient.value) {
        mappedState.up = false
        mappedState.down = false
        guacClient.value.sendMouseState(mappedState)
      }
    }

    // 如果是水平滚动，需要释放Shift键
    if (!isVertical && guacClient.value) {
      setTimeout(() => {
        guacClient.value.sendKeyEvent(0, 0xFFE1) // 释放Shift键
      }, 10)
    }

    // 返回余数，保留平滑滚动
    return accumulator % scrollConfig.threshold
  }

  // 触摸开始处理
  const touchStartHandler = (e: TouchEvent) => {
    e.preventDefault()
    if (e.touches.length === 1) {
      const touch = e.touches[0]
      const relativeCoords = calculateRelativeCoordinates(touch)

      // 更新状态
      touchState.lastTouchX = relativeCoords.x
      touchState.lastTouchY = relativeCoords.y
      touchState.lastX = touch.clientX
      touchState.lastY = touch.clientY
      touchState.isTouching = true
      touchState.verticalAccumulator = 0
      touchState.horizontalAccumulator = 0

      // 重置滚动历史
      scrollHistory.vertical = []
      scrollHistory.horizontal = []

      // 发送鼠标按下事件
      sendMouseState({
        x: relativeCoords.x,
        y: relativeCoords.y,
        left: false,
        middle: false,
        right: false,
        up: false,
        down: false,
        _isRelative: false
      })
    }
  }

  // 触摸移动处理
  const touchMoveHandler = (e: TouchEvent) => {
    e.preventDefault()
    if (e.touches.length === 1 && touchState.isTouching) {
      const touch = e.touches[0]
      const relativeCoords = calculateRelativeCoordinates(touch)

      // 计算移动距离
      const deltaX = touch.clientX - touchState.lastX
      const deltaY = touch.clientY - touchState.lastY

      // 更新位置
      touchState.lastX = touch.clientX
      touchState.lastY = touch.clientY

      // 应用速度因子
      const scaledDeltaX = deltaX * scrollConfig.speedFactor
      const scaledDeltaY = deltaY * scrollConfig.speedFactor

      // 累加滚动距离
      touchState.horizontalAccumulator += scaledDeltaX
      touchState.verticalAccumulator += scaledDeltaY

      // 处理垂直滚动
      if (Math.abs(touchState.verticalAccumulator) >= scrollConfig.threshold) {
        const isScrollingDown = touchState.verticalAccumulator < 0
        touchState.verticalAccumulator = handleScroll(
          relativeCoords,
          true,
          isScrollingDown
        )
      }

      // 处理水平滚动
      if (Math.abs(touchState.horizontalAccumulator) >= scrollConfig.threshold) {
        const isScrollingRight = touchState.horizontalAccumulator < 0
        touchState.horizontalAccumulator = handleScroll(
          relativeCoords,
          false,
          isScrollingRight
        )
      }

      // 更新触摸位置
      touchState.lastTouchX = relativeCoords.x
      touchState.lastTouchY = relativeCoords.y

      // 发送鼠标移动事件
      sendMouseState({
        x: relativeCoords.x,
        y: relativeCoords.y,
        left: false,
        middle: false,
        right: false,
        up: false,
        down: false,
        _isRelative: false
      })
    }
  }

  // 触摸结束处理
  const touchEndHandler = (e: TouchEvent) => {
    e.preventDefault()
    if (touchState.isTouching) {
      // 发送鼠标抬起事件
      sendMouseState({
        x: touchState.lastTouchX,
        y: touchState.lastTouchY,
        left: false,
        middle: false,
        right: false,
        up: false,
        down: false,
        _isRelative: false
      })

      // 重置状态
      touchState.isTouching = false
      touchState.verticalAccumulator = 0
      touchState.horizontalAccumulator = 0
      scrollHistory.vertical = []
      scrollHistory.horizontal = []
    }
  }

  // 绑定事件处理器
  element.addEventListener('touchstart', touchStartHandler, {passive: false})
  element.addEventListener('touchmove', touchMoveHandler, {passive: false})
  element.addEventListener('touchend', touchEndHandler, {passive: false})

  // 记录处理器以便后续清理
  mouseHandlers.value = [
    {element, eventType: 'touchstart', listener: touchStartHandler},
    {element, eventType: 'touchmove', listener: touchMoveHandler},
    {element, eventType: 'touchend', listener: touchEndHandler},
  ]

  console.log('触摸事件处理器设置完成')
}

/**
 * 清除触摸事件处理器
 */
const clearTouchHandlers = () => {
    if (mouseHandlers.value.length > 0) {
        mouseHandlers.value.forEach((handler: any) => {
            if (handler.element && handler.eventType && handler.listener) {
                handler.element.removeEventListener(handler.eventType, handler.listener)
            }
        })
        mouseHandlers.value = []
    }
}

// 切换全屏
const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
        const displayContainer = document.body;
        if (displayContainer) {
            displayContainer.requestFullscreen().catch(err => {
                message.error(`全屏请求被拒绝: ${err.message}`)
            })
            // 切换到全屏时启用自动适应
            fitToScreen.value = true
        }
        fullscreen.value = true
    } else {
        document.exitFullscreen()
        fullscreen.value = false
    }

    // 全屏状态变更后需要重新调整大小
    setTimeout(resizeDisplay, 300)
}

// 复制到本地剪贴板
const copyToLocalClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text)
        // message.success('远程内容已复制到本地剪贴板')
    } catch (error) {
        console.error('无法复制到剪贴板:', error)
        message.warning('无法自动复制到剪贴板，请手动复制')
    }
}

// 发送文本到远程剪贴板
const sendToRemoteClipboard = (text: string) => {
    if (!guacClient || !connected.value) return
    console.log('发送文本到远程剪贴板:', guacClient.value, text);
    try {
        const stream = guacClient.value.createClipboardStream('text/plain')
        const writer = new window.Guacamole.StringWriter(stream)

        // 写入文本内容
        writer.sendText(text)

        // 结束流
        writer.sendEnd()

        // message.success('文本已发送到远程剪贴板')
    } catch (error) {
        console.error('发送到远程剪贴板失败:', error)
        // message.error('发送到远程剪贴板失败')
    }
}

// 从本地剪贴板粘贴到远程，不允许自动关闭菜单
const pasteFromLocal = async (e?: Event) => {
    // 如果有事件对象，阻止冒泡以防止菜单自动关闭
    // if (e) {
    //     e.stopPropagation()
    // }

    try {
        const text = await navigator.clipboard.readText()
        console.log('pasteFromLocal', text);
        sendToRemoteClipboard(text)
        // message.success('已从本地粘贴到远程')

        // 不自动关闭下拉菜单
        clipboardDropdownVisible.value = true
    } catch (error) {
        console.error('无法读取本地剪贴板:', error)
        message.warning('无法读取本地剪贴板，请在左上角检查权限')
    }
}

// 发送文本到远程剪贴板，不自动关闭菜单
const sendCustomTextToRemote = (text: string, e?: Event) => {
    // 阻止冒泡以防止菜单自动关闭
    if (e) {
        e.stopPropagation()
    }

    if (text) {
        sendToRemoteClipboard(text)
        // 不自动关闭下拉菜单
        clipboardDropdownVisible.value = true
    }
}

// 控制剪贴板下拉菜单可见性
const handleClipboardDropdownVisibleChange = (visible: boolean) => {
    clipboardDropdownVisible.value = visible
}

// 调整显示大小
const resizeDisplay = () => {
    if (!guacClient.value || !connected.value) return

    const display = guacClient.value.getDisplay()
    if (!display) return

    // 获取容器和原始显示尺寸
    const container = guacDisplayRefs.value
    if (!container) return

    const containerWidth = container.clientWidth
    const containerHeight = container.clientHeight

    // 获取原始显示尺寸
    const displayElement = display.getElement()
    const currentNativeWidth = display.getWidth()
    const currentNativeHeight = display.getHeight()

    displayElement.style.cursor = 'none';

    // 如果原始尺寸为0，则无需计算
    if (currentNativeWidth === 0 || currentNativeHeight === 0) return

    // 更新组件中的原始尺寸引用
    nativeWidth.value = currentNativeWidth
    nativeHeight.value = currentNativeHeight

    // 根据当前缩放模式计算缩放比例
    let scaleX, scaleY, finalScale

    if (fitToScreen.value) {
        // 适应屏幕模式：根据容器尺寸计算最佳缩放比例
        scaleX = containerWidth / currentNativeWidth
        scaleY = containerHeight / currentNativeHeight
        finalScale = Math.min(scaleX, scaleY)
    } else {
        // 应用自定义缩放比例
        finalScale = Math.max(0.1, Math.min(2.0, scale.value))
    }

    // 保存当前使用的缩放比例
    scale.value = finalScale

    // 计算居中偏移
    const scaledWidth = Math.round(currentNativeWidth * finalScale)
    const scaledHeight = Math.round(currentNativeHeight * finalScale)

    // 移动设备时，水平居中但垂直从顶部开始
    let offsetX = Math.max(0, Math.floor((containerWidth - scaledWidth) / 2));
    let offsetY = 0;

    if (!(isMobile.value || isMobileDevice())) {
      // 非移动设备才垂直居中
      offsetY = Math.max(0, Math.floor((containerHeight - scaledHeight) / 2));
    }

    // 保存当前偏移量，用于坐标映射
    displayOffsetX.value = offsetX
    displayOffsetY.value = offsetY

    // 设置CSS样式
    displayElement.style.transform = `scale(${finalScale})`
    displayElement.style.transformOrigin = '0 0'
    displayElement.style.position = 'absolute'
    displayElement.style.left = `${offsetX}px`
    displayElement.style.top = `${offsetY}px`

    // 记录日志，方便调试
    console.log('Display resized:', {
        containerWidth,
        containerHeight,
        nativeWidth: currentNativeWidth,
        nativeHeight: currentNativeHeight,
        scale: finalScale,
        offsetX,
        offsetY
    })

    // 缩放变更后，重新绑定鼠标进入离开监听器
    setTimeout(() => {
        bindMouseListeners()
        setupTouchHandlers()
    }, 100)
}

// 添加缩放控制功能
const zoomIn = () => {
    scale.value = Math.min(scale.value + 0.1, 2.0)
    fitToScreen.value = false
    applyScale()
}

const zoomOut = () => {
    scale.value = Math.max(scale.value - 0.1, 0.5)
    fitToScreen.value = false
    applyScale()
}

const resetZoom = () => {
    scale.value = 1.0
    fitToScreen.value = false
    applyScale()
}

const toggleFitScreen = () => {
    fitToScreen.value = !fitToScreen.value
    if (fitToScreen.value) {
        resizeDisplay()
    } else {
        applyScale()
    }
    message.success(fitToScreen.value ? '已启用自动适应屏幕' : '已关闭自动适应')
}

// 应用当前缩放比例
const applyScale = () => {
    if (!guacDisplay.value) return
    guacDisplay.value.scale(scale.value)

    // 重置位置
    if (guacDisplayElement.value) {
        guacDisplayElement.value.style.left = '0'
        guacDisplayElement.value.style.top = '0'
    }
}

// 在连接成功后获取并设置原始分辨率
const handleConnectSuccess = () => {
    if (!guacDisplay.value) return

    console.log('连接成功，准备初始化显示设置')

    // 移动端隐藏远程桌面的鼠标指针，因为我们使用触摸
    if (isMobile.value || isMobileDevice()) {
      guacClient.value.getDisplay().showCursor(false)
    }

    // 重置性能统计
    // resetPerformanceStats()

    const defaultLayer = guacDisplay.value.getDefaultLayer()
    if (defaultLayer) {
        nativeWidth.value = defaultLayer.width
        nativeHeight.value = defaultLayer.height
        console.log('远程桌面原始分辨率:', nativeWidth.value, 'x', nativeHeight.value)

        // 尝试匹配当前分辨率到预设选项
        // const currentResStr = `${nativeWidth.value}x${nativeHeight.value}`
        // const matchedOption = resolutionOptions.value.find((opt: any) => opt.value === currentResStr)
        // if (matchedOption) {
        //     // 设置当前选中的分辨率
        //     selectedResolution.value = matchedOption.value
        //     console.log('匹配到预设分辨率:', matchedOption.label)
        // } else {
        //     // 未匹配到预设值，保持为自适应
        //     selectedResolution.value = 'auto'
        //     console.log('未匹配到预设分辨率，使用自适应模式')
        // }
    } else {
        console.warn('无法获取默认图层，使用默认分辨率:', nativeWidth.value, 'x', nativeHeight.value)
        // selectedResolution.value = 'auto'
    }

    // 首次连接时自动适应屏幕
    setTimeout(() => {
        console.log('首次自动调整显示大小...')
        if (selectedResolution.value === 'auto') {
            // 如果是自适应模式，适应当前窗口大小
            resizeDisplay()
        } else {
            // 否则保持远程原始分辨率
            guacDisplay.value.scale(1)

            // 居中显示
            if (guacDisplayElement.value && guacDisplayRefs.value) {
                const containerWidth = guacDisplayRefs.value.clientWidth
                const containerHeight = guacDisplayRefs.value.clientHeight

                // 计算居中偏移
                const offsetX = Math.max(0, (containerWidth - nativeWidth.value) / 2)
                const offsetY = Math.max(0, (containerHeight - nativeHeight.value) / 2)

                // 应用偏移
                guacDisplayElement.value.style.position = 'absolute'
                guacDisplayElement.value.style.left = `${offsetX}px`
                guacDisplayElement.value.style.top = `${offsetY}px`
            }
            resizeDisplay()
        }
    }, 500)

    // 如果选择了自适应模式，尝试请求调整服务器端分辨率
    // if (selectedResolution.value === 'auto') {
    //     setTimeout(() => {
    //         console.log('请求调整远程分辨率...')
    //         requestHigherResolution()
    //     }, 1000)
    // }
}

// 修改动态请求更高分辨率方法
const requestHigherResolution = async () => {
    if (!connected.value || !guacClient.value) {
        console.log('无法请求调整分辨率：客户端未连接')
        return
    }

    // 获取容器尺寸
    const containerWidth = guacDisplayRefs.value?.clientWidth || 1024
    const containerHeight = guacDisplayRefs.value?.clientHeight || 768

    console.log('请求调整显示 - 容器尺寸:', containerWidth, 'x', containerHeight)

    // 直接调用setRemoteResolution方法来应用分辨率
    setRemoteResolution('auto')
}

// 发送特殊键组合
const sendKeyCombination = (combination: string) => {
    if (!guacClient || !keyboardControl.value || !connected.value) return;

    // 按下的键组合
    let keysToPress: number[] = [];

    switch (combination) {
        case 'ctrl+alt+del':
            keysToPress = [
                0xFFE3, // Ctrl
                0xFFE9, // Alt
                0xFFFF, // Delete
            ];
            break;
        case 'alt+tab':
            keysToPress = [
                0xFFE9, // Alt
                0xFF09, // Tab
            ];
            break;
        case 'ctrl+alt+f4':
            keysToPress = [
                0xFFE3, // Ctrl
                0xFFE9, // Alt
                0xFFC1, // F4
            ];
            break;
        case 'windows':
            keysToPress = [0xFFEB]; // Windows/Super key
            break;
        default:
            message.warning('未知的键盘组合');
            return;
    }

    // 先按下所有键
    keysToPress.forEach(key => {
        guacClient.value.sendKeyEvent(1, key);
    });

    // 延迟后释放所有键（反向顺序）
    setTimeout(() => {
        keysToPress.reverse().forEach(key => {
            guacClient.value.sendKeyEvent(0, key);
        });
        message.success(`已发送 ${combination} 组合键`);
    }, 100);
};

// 切换音频状态
const toggleAudio = () => {
    audioEnabled.value = !audioEnabled.value;

    if (audioEnabled.value) {
        // 恢复所有音频播放
        audioPlayers.value.forEach((player: Guacamole.AudioPlayer) => {
            if (player && player.play) {
                player.play();
            }
        });
        message.success('已开启音频');
    } else {
        // 暂停所有音频播放
        audioPlayers.value.forEach((player: Guacamole.AudioPlayer) => {
            if (player && player.pause) {
                player.pause();
            }
        });
        message.success('已静音');
    }
};

const doDisconnectSession = async () => {
    if (connectInfo.value?.sessionId) {
        try {
           let res = await disconnectSession(connectInfo.value.sessionId);
           if(res.success) {
               // message.success('已成功断开链接');
           } else {
               message.error(res.message || '断开连接命令失败');
           }
        } catch (e) {
            console.error('断开连接失败:', e);

        }
    }
}

// 断开连接
const disconnect = async (flag: boolean = false) => {
    if (guacClient.value) {
        guacClient.value.disconnect();

        await doDisconnectSession();

        // 释放资源
        if (keyboardControl.value) {
            keyboardControl.value.onkeydown = null;
            keyboardControl.value.onkeyup = null;
            keyboardControl.value.reset();
        }

        if (mouseControl.value) {
            mouseControl.value.onmousedown = null;
            mouseControl.value.onmouseup = null;
            mouseControl.value.onmousemove = null;
        }

        // 清理鼠标进入离开事件监听器
        cleanupMouseListeners()

        // 停止所有音频播放
        audioPlayers.value.forEach((player: Guacamole.AudioPlayer) => {
            if (player && player.pause) {
                player.pause();
            }
        });

        guacClient.value = null;
        message.success(flag ? '正在调整分辨率...' : '已断开远程连接')
        connected.value = false
    }
}

// 清理鼠标进入离开事件监听器
const cleanupMouseListeners = () => {
    if (guacDisplayElement.value) {
        try {
            const element = guacDisplayElement.value;

            // 移除鼠标进入和离开事件
            element.removeEventListener('mouseenter', handleMouseEnter);
            element.removeEventListener('mouseleave', handleMouseLeave);

            console.debug('已清理鼠标进入离开事件监听器');
        } catch (error) {
            console.error('清理鼠标事件监听器失败:', error);
        }
    }

    // 清理触摸事件处理器
    clearTouchHandlers();
}

// 鼠标进入事件处理函数
const handleMouseEnter = (e: MouseEvent) => {
    if (!connected.value || !guacClient.value) return;

    console.debug('鼠标进入显示区域');

    // 获取显示元素和容器的精确位置
    const containerRect = guacDisplayRefs.value.getBoundingClientRect();

    // 在鼠标进入时，立即计算正确的坐标
    // 使用相对于容器的坐标计算，而不是相对于显示元素
    const containerRelativeX = e.clientX - containerRect.left;
    const containerRelativeY = e.clientY - containerRect.top;

    // 计算相对于显示元素的实际位置（考虑偏移）
    const x = containerRelativeX - displayOffsetX.value;
    const y = containerRelativeY - displayOffsetY.value;

    // 创建一个初始的鼠标移动事件，确保坐标正确
    const initialState = {
        x,
        y,
        left: false,
        middle: false,
        right: false,
        up: false,
        down: false,
        // 标记为外部坐标，需要进行完整映射
        _isRelative: false
    };

    // 应用坐标映射并发送
    const mappedState = applyCoordinateMapping(initialState);
    guacClient.value.sendMouseState(mappedState);
};

// 鼠标离开事件处理函数
const handleMouseLeave = (e: MouseEvent) => {
    if (!connected.value || !guacClient.value) return;

    console.debug('鼠标离开显示区域');

    // 释放所有鼠标按键
    const releaseState = {
        // 使用最后的有效位置，而不是0，0
        // 这可以避免鼠标离开时光标跳到远程桌面左上角
        x: Math.floor(nativeWidth.value / 2),  // 使用远程桌面中心点
        y: Math.floor(nativeHeight.value / 2),
        left: false,
        middle: false,
        right: false,
        up: false,
        down: false,
        _isRelative: true  // 标记为已经是相对坐标
    };

    guacClient.value.sendMouseState(releaseState);
};

// 绑定鼠标进入离开监听器
const bindMouseListeners = () => {
  if (guacDisplayElement.value) {
      const element = guacDisplayElement.value;
      // 先移除可能存在的事件监听器，避免重复添加
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);

      // 添加新的事件监听器
      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      console.debug('已绑定鼠标进入离开事件监听器');
  }
};

// 返回列表页
const backToList = () => {
  if (window.webviewApi) {
    const id = route.query.id
    window.webviewApi.invoke('close-webview', id)
    return
  }
  disconnect()
  router.push('/cloudDesktop')
}

// 处理页面焦点恢复事件
const handleWindowFocus = () => {
    if (connected.value && toggleCopyToRemoteStatus.value) {
        console.log('页面焦点恢复，尝试从本地剪贴板粘贴到远程');
        navigator.permissions.query({
            name: 'clipboard-read'
        }).then(permissionStatus => {
            // permissionStatus.state 的值是 'granted'、'denied'、'prompt':
            console.log(permissionStatus.state);
            if(permissionStatus?.state == 'granted') {
                pasteFromLocal();
            } else if(permissionStatus?.state == 'prompt') {
                message.warning('当前未允许读取剪切板，请在左上角允许，如没有弹窗请手动开启');
            } else {
                console.log('当前没有剪切板权限,已被禁止');
            }
        });
    }
};

// 初始化剪贴板监听
const initClipboardListener = () => {
    // 监听剪贴板粘贴事件
    document.addEventListener('paste', (e: ClipboardEvent) => {
        if (connected.value) {
            const text = e.clipboardData?.getData('text');
            if (text) {
                // 将本地剪贴板内容发送到远程
                sendToRemoteClipboard(text);
            }
        }
    });

    // 监听页面焦点恢复事件，当用户切换回页面时自动调用pasteFromLocal
    window.addEventListener('focus', handleWindowFocus);

    // 监听键盘事件，处理Command+V (Mac) 和 Ctrl+V (Windows/Linux)
    // document.addEventListener('keydown', async (e: KeyboardEvent) => {
    //     // 检查是否按下了Command+V (Mac) 或 Ctrl+V (Windows/Linux)
    //     if (connected.value && ((e.metaKey && e.key === 'v') || (e.ctrlKey && e.key === 'v'))) {
    //         // e.stopPropagation(); // 阻止事件冒泡

    //         try {
    //             // 从本地剪贴板读取文本
    //             const text = await navigator.clipboard.readText();
    //             if (text) {
    //                 // 将本地剪贴板内容发送到远程
    //                 sendToRemoteClipboard(text);
    //                 console.log('快捷键触发：已发送本地剪贴板内容到远程');
    //             }
    //         } catch (error) {
    //             console.error('无法读取本地剪贴板:', error);
    //             message.warning('无法读取本地剪贴板，请检查权限或手动粘贴');
    //         }
    //     }
    // });
};

const customVisible = shallowRef(false);
const showCustomServe = () => {
    customVisible.value = true;
}

// 初始化音频恢复
const initAudioResume = () => {
    // 监听点击事件
    document.addEventListener('click', () => {
        if (!isInitAudioResume.value) {
            isInitAudioResume.value = true
            try {
                // 确保Guacamole已加载并且AudioContextFactory可用
                if (window.Guacamole && window.Guacamole.AudioContextFactory) {
                    const audioContext = window.Guacamole.AudioContextFactory.getAudioContext()
                    if (audioContext && audioContext.state === 'suspended') {
                        console.log('第一次用户交互时恢复 AudioContext')
                        audioContext.resume().then(() => {
                            console.log('AudioContext 已通过用户交互恢复:', audioContext.state)
                        }).catch((err: Error) => {
                            console.error('恢复 AudioContext 失败:', err)
                        })
                    }
                }
            } catch (e) {
                console.error('尝试在用户交互时恢复 AudioContext 出错:', e)
            }
        }
    });
}

// 向guacamole后端发送调整分辨率请求
const setRemoteResolution = async (resolutionValue: string) => {

    if (!connected.value || !guacClient.value) {
        message.warning('未连接到远程桌面');
        return;
    }

    // 自适应窗口大小
    if (resolutionValue === 'auto') {
        // 获取容器尺寸
        const containerWidth = guacDisplayRefs.value?.clientWidth;
        const containerHeight = guacDisplayRefs.value?.clientHeight;

        // 设置为窗口大小
        applyRemoteResolution(containerWidth, containerHeight);
        return;
    }

    // 解析分辨率值
    const [width, height] = resolutionValue.split('x').map(Number);
    if (!width || !height) {
        message.error('无效的分辨率值');
        return;
    }

    // 应用分辨率
    applyRemoteResolution(width, height);
};

// 应用分辨率到远程会话
const applyRemoteResolution = async (width: number, height: number) => {
    console.log('正在调整远程分辨率:', width, 'x', height)
    message.loading({content: `正在调整远程分辨率: ${width} × ${height}`, key: 'resChange'})

    try {
        if (!guacClient.value || !guacClient.value.sendSize) {
            message.error({content: '当前连接不支持调整分辨率', key: 'resChange'});
            return;
        }

        // 构建命令，这里假设后端支持 "size" 指令来调整分辨率
        // 实际命令格式需要根据你的 Guacamole 服务端实现进行调整
        // guacClient.value.sendSize(width, height);
        await fetchConnectionInfo(true)

        // 设置一个超时检查是否成功应用
        setTimeout(() => {
            // 读取当前分辨率
            const defaultLayer = guacDisplay.value?.getDefaultLayer()
            if (defaultLayer) {
                const currentWidth = defaultLayer.width
                const currentHeight = defaultLayer.height

                console.log('调整后的远程分辨率:', currentWidth, 'x', currentHeight)

                if (Math.abs(currentWidth - width) < 10 && Math.abs(currentHeight - height) < 10) {
                    message.success({
                        content: `已调整远程分辨率为 ${currentWidth} × ${currentHeight}`,
                        key: 'resChange'
                    })

                    // 更新本地记录的分辨率
                    nativeWidth.value = currentWidth
                    nativeHeight.value = currentHeight

                    // 更新UI显示
                    resizeDisplay()
                } else {
                    message.warning({
                        content: `分辨率可能未成功调整，当前: ${currentWidth} × ${currentHeight}`,
                        key: 'resChange'
                    })
                }
            } else {
                message.info({content: '已发送分辨率调整请求', key: 'resChange'})
            }
        }, 3 * 1000) // 等待3秒检查
    } catch (error) {
        console.error('调整远程分辨率失败:', error)
        message.error({content: '调整远程分辨率失败', key: 'resChange'})
    }
}

// 监听分辨率选择变化
watch(selectedResolution, (newValue: any) => {
    if (newValue && connected.value) {
        setRemoteResolution(newValue);
    }
});

// 添加窗口自适应按钮处理函数
const adaptToWindow = () => {
    selectedResolution.value = 'auto';
    setRemoteResolution('auto');
};

/**
 * 处理窗口大小变化事件
 */
const handleWindowResize = lodash.debounce(async () => {
  resizeDisplay()
  setupTouchHandlers()
  await fetchConnectionInfo(true)
},750)

// 初始化Guacamole AudioContext
const initGuacamoleAudioContext = () => {
    try {
        // 检查Guacamole库是否加载
        if (!window.Guacamole) {
            console.warn('无法初始化AudioContext：Guacamole库未加载')
            return
        }

        // 创建AudioContext单例
        const audioContext = window.Guacamole.AudioContextFactory.getAudioContext()

        if (!audioContext) {
            console.warn('无法创建AudioContext：浏览器可能不支持Web Audio API')
            return
        }

        // 添加自定义sync方法的补丁 - 使用类型断言
        if (window.Guacamole.RawAudioPlayer && window.Guacamole.RawAudioPlayer.prototype) {
            // 保存原始的sync方法
            const originalSync = window.Guacamole.RawAudioPlayer.prototype.sync
            if (originalSync && typeof originalSync === 'function') {
                // 重写sync方法 - 使用类型断言
                // @ts-ignore - 忽略this上下文类型检查
                (window.Guacamole.RawAudioPlayer.prototype as any).sync = function () {
                    // 调用原始sync方法
                    originalSync.apply(this, arguments)

                    // 尝试恢复AudioContext如果它是暂停状态
                    if (audioContext && audioContext.state === 'suspended') {
                        try {
                            console.log('Guacamole sync: 尝试恢复AudioContext')
                            audioContext.resume()
                        } catch (e) {
                            console.error('Guacamole sync: 恢复AudioContext失败:', e)
                        }
                    }
                }
            }
        }

        console.log('已初始化Guacamole AudioContext，当前状态:', audioContext.state)

        // 如果AudioContext处于暂停状态，尝试立即恢复
        if (audioContext.state === 'suspended') {
            audioContext.resume().then(() => {
                console.log('成功恢复AudioContext，当前状态:', audioContext.state)
            }).catch((err) => {
                console.error('恢复AudioContext失败:', err)
            })
        }
    } catch (e) {
        console.error('初始化Guacamole AudioContext时出错:', e)
    }
}

// 在页面挂载时初始化事件监听
onMounted(() => {
    if (isMobile.value || isMobileDevice()) {
      selectedResolution.value = '768x1330';
    }
    // 检测支持的媒体类型
    detectSupportedMediaTypes()

    // 初始化Guacamole AudioContext
    initGuacamoleAudioContext()

    // 检测GPU加速支持
    checkGpuAccelerationSupport()

    // 尝试恢复AudioContext
    resumeAudioContext()

    // 尝试连接
    fetchConnectionInfo()

    // 窗口大小变化时调整显示大小
    window.addEventListener('resize', handleWindowResize)

    // 初始化剪贴板监听
    initClipboardListener()

    // 初始化音频恢复
    initAudioResume()

    // 添加全屏变化监听
    document.addEventListener('fullscreenchange', () => {
        fullscreen.value = !!document.fullscreenElement
        // 全屏变化后调整显示大小
        setTimeout(resizeDisplay, 100)
    })

    document.title = `远程连接-${instanceName.value}`
})

// 页面卸载时断开连接
onBeforeUnmount(() => {
    disconnect()

    // 移除事件监听器
    window.removeEventListener('resize', handleWindowResize)
    document.removeEventListener('fullscreenchange', () => {
    })

    // 移除页面焦点恢复事件监听器
    window.removeEventListener('focus', handleWindowFocus)

    // 清理鼠标事件监听器
    cleanupMouseListeners()
})

// 切换性能面板显示
const togglePerformancePanel = () => {
    showPerformancePanel.value = !showPerformancePanel.value
    // 如果开启性能面板，重置统计数据
    if (showPerformancePanel.value) {
        resetPerformanceStats()
    }
}

// 计算会话时长
const getSessionDuration = () => {
    const now = Date.now()
    const duration = now - renderStartTime.value
    const seconds = Math.floor((duration / 1000) % 60)
    const minutes = Math.floor((duration / (1000 * 60)) % 60)
    const hours = Math.floor(duration / (1000 * 60 * 60))

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 计算平均帧率
const getAverageFrameRate = () => {
    const now = performance.now()
    const duration = (now - renderStartTime.value) / 1000 // 转换为秒
    if (duration <= 0) return 0

    return Math.round(totalFramesReceived.value / duration)
}

// 重置性能统计
const resetPerformanceStats = () => {
    // 重置帧率相关统计
    frameCount.value = 0
    totalFramesReceived.value = 0
    renderStartTime.value = performance.now()
    lastFrameUpdate.value = performance.now()

    // 重置帧率记录
    minFrameRate.value = 9999
    maxFrameRate.value = 0
    frameRate.value = 0

    // 清空历史数据
    frameTimes.value = []
    frameRateHistory.value = []

    // 重置其他指标
    frameTimeVariance.value = 0
    networkLatency.value = 0
    currentFrameInterval.value = 0

    message.success('性能统计数据已重置')
}

// 检测GPU加速支持情况
const checkGpuAccelerationSupport = () => {
    // 检查WebGL支持
    const canvas = document.createElement('canvas')
    let gl: WebGLRenderingContext | null = null

    try {
        // 明确指定返回类型为WebGLRenderingContext
        gl = canvas.getContext('webgl2') || canvas.getContext('webgl') || canvas.getContext('experimental-webgl') as WebGLRenderingContext | null
    } catch (e) {
        console.warn('WebGL检测出错:', e)
    }

    if (gl) {
        gpuAccelerationAvailable.value = true

        // 获取GPU信息
        try {
            // 明确指定扩展类型
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
            if (debugInfo) {
                const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL)
                const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
                gpuInfo.value = `${vendor} - ${renderer}`
            } else {
                gpuInfo.value = '已启用WebGL加速，但无法获取GPU详情'
            }
        } catch (e) {
            gpuInfo.value = '已启用WebGL加速，但获取GPU信息时出错'
            console.warn('获取GPU信息时出错:', e)
        }

        console.log('支持GPU加速渲染:', gpuInfo.value)
    } else {
        gpuAccelerationAvailable.value = false
        gpuInfo.value = '不支持WebGL渲染'
        console.warn('不支持GPU加速渲染')
    }
}

// 切换GPU加速
const toggleGpuAcceleration = () => {
    if (!gpuAccelerationAvailable.value) {
        message.warning('您的设备不支持GPU加速')
        return
    }

    gpuAcceleration.value = !gpuAcceleration.value

    // 如果已连接，需要重新连接以应用新设置
    if (connected.value) {
        message.info({
            content: `正在${gpuAcceleration.value ? '启用' : '禁用'}GPU加速，需要重新连接...`,
            duration: 3
        })

        // 短暂延迟后重新连接
        setTimeout(() => {
            fetchConnectionInfo()
        }, 1000)
    } else {
        message.success(`已${gpuAcceleration.value ? '启用' : '禁用'}GPU加速，将在下次连接时生效`)
    }
}

// 选择渲染模式
const selectRenderMode = (mode: string) => {
    renderMode.value = mode

    // 如果已连接，需要重新连接以应用新设置
    if (connected.value) {
        message.info({
            content: `正在切换到${getRenderModeName(mode)}渲染模式，需要重新连接...`,
            duration: 3
        })

        // 短暂延迟后重新连接
        setTimeout(() => {
            fetchConnectionInfo()
        }, 1000)
    } else {
        message.success(`已切换到${getRenderModeName(mode)}渲染模式，将在下次连接时生效`)
    }
}

// 获取渲染模式名称
const getRenderModeName = (mode: string) => {
    switch (mode) {
        case 'auto':
            return '自动'
        case 'gpu':
            return 'GPU'
        case 'cpu':
            return 'CPU'
        default:
            return mode
    }
}

// 辅助函数：尝试恢复AudioContext
const resumeAudioContext = () => {
    try {
        // 确保Guacamole已加载
        if (window.Guacamole && window.Guacamole.AudioContextFactory) {
            // 获取或创建AudioContext
            const audioContext = window.Guacamole.AudioContextFactory.getAudioContext()

            // 如果AudioContext处于暂停状态，尝试恢复它
            if (audioContext && audioContext.state === 'suspended') {
                console.log('用户交互后恢复 AudioContext')
                audioContext.resume().then(() => {
                    console.log('AudioContext 已成功恢复:', audioContext.state)
                }).catch((err: Error) => {
                    console.error('恢复 AudioContext 失败:', err)
                })
            }
        }
    } catch (e) {
        console.error('恢复 AudioContext 出错:', e)
    }
}

// 连接到远程桌面
const connectToCloudDesktop = () => {
    // 确保在用户交互时就尝试恢复AudioContext
    resumeAudioContext()

    // 调用实际的连接函数
    fetchConnectionInfo()
}


(window as any).changeResolution = (data: string)=>{
  selectedResolution.value = data;
};

(window as any).changeScale = (data:number)=>{
  scale.value = Number(data);
  applyScale()
};

(window as any).getClient = ()=>{
  return guacClient.value;
};

const mobileInputText = ref('')
const virtualKeyboardInput = ref(null)

const toggleVirtualKeyboard = () => {
  nextTick(() => {
    virtualKeyboardInput.value?.focus()
  })
}

// 处理输入事件
const handleVirtualKeyInput = (e:any) => {
  mobileInputText.value = e.target.value
}

// 添加浮动面板位置控制
const floatingPanelPosition = ref({ left: '10px', top: '10px' })
const isDragging = ref(false)
const dragStartPos = ref({ x: 0, y: 0 })
const panelStartPos = ref({ x: 0, y: 0 })

// 开始拖拽
const startDrag = (e: TouchEvent) => {
  isDragging.value = true
  const touch = e.touches[0]
  dragStartPos.value = { x: touch.clientX, y: touch.clientY }

  // 获取当前面板位置
  const currentLeft = parseInt(floatingPanelPosition.value.left) || 10
  const currentTop = parseInt(floatingPanelPosition.value.top) || 10
  panelStartPos.value = { x: currentLeft, y: currentTop }
}

// 拖拽中
const onDrag = (e: TouchEvent) => {
  if (!isDragging.value) return

  e.preventDefault() // 防止页面滚动
  const touch = e.touches[0]

  // 计算移动距离
  const deltaX = touch.clientX - dragStartPos.value.x
  const deltaY = touch.clientY - dragStartPos.value.y

  // 更新面板位置
  const newLeft = Math.max(0, panelStartPos.value.x + deltaX)
  const newTop = Math.max(0, panelStartPos.value.y + deltaY)

  // 限制不超出屏幕边界
  const maxLeft = window.innerWidth - 60 // 面板宽度约60px
  const maxTop = window.innerHeight - 140 // 面板高度约120px

  floatingPanelPosition.value = {
    left: `${Math.min(newLeft, maxLeft)}px`,
    top: `${Math.min(newTop, maxTop)}px`
  }
}

const handleMobileMenuClick = (item:any)=>{
  switch (item.key){
    case 'clipboard':
      pasteFromLocal();
      break;
    case 'resolution':
      break;
    case 'key-combination':
      break;
    case 'disconnect':
      disconnect();
      break;
  }
}

// 结束拖拽
const endDrag = () => {
  isDragging.value = false
}

</script>

<template>
    <RemoteLayout>
        <div class="cloud-desktop-connect-container" id="guac-display-container">
            <!-- 连接信息头部 -->
            <div v-if="!isMobile" class="connect-header-container" :class="{ 'collapsed': headerCollapsed }" @mouseenter="handleMouseEnterToolbar" @mouseleave="handleMouseLeaveToolbar">
                <div class="connect-header text-12px">
                    <div class="left-info flex items-center">
                        <!-- <a-button type="link" @click="backToList" class="back-btn mr-2">
                            <template #icon>
                                <left-outlined/>
                            </template>
                            返回列表
                        </a-button> -->
<!--                        <a-divider type="vertical" class="divider"/>-->
                        <a-tag v-if="connected" color="success" class="ml-4 text-12px">已连接</a-tag>
                        <a-tag v-else-if="connecting" color="processing" class="ml-4 text-12px">连接中...</a-tag>
                        <a-tag v-else color="error" class="ml-4 text-12px">未连接</a-tag>
                        <span class="text-12px font-medium desktop-name">{{ instanceName || '远程桌面' }}</span>
                        <a-divider type="vertical" class="divider"/>
                        <span class="text-12px font-medium desktop-name">{{ connectInfo?.workspaceIp || '' }}</span>

                        <!-- 显示帧率 -->
                        <span v-if="connected" class="ml-4 text-12px text-white cursor-pointer"
                        >
                            帧率：{{ frameRate }} FPS
<!--                            <a-tooltip title="预估的网络延迟时间">-->
<!--                                <span class="ml-2">(延迟: {{ networkLatency }}ms)</span>-->
<!--                            </a-tooltip>-->
<!--                            <eye-outlined v-if="!showPerformancePanel" class="ml-2"/>-->
<!--                            <eye-invisible-outlined v-else class="ml-2"/>-->
                        </span>
                    </div>
                    <div class="right-actions">
                        <a-space>
                            <!-- 剪贴板操作 -->
                          <a-tooltip title="剪贴板">
                            <a-dropdown :trigger="['click']" placement="bottomRight" :disabled="!connected">
                                <a-button shape="circle" :disabled="!connected">
                                    <template #icon>
                                        <copy-outlined/>
                                    </template>
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item key="share-copy">
                                            共享剪贴板
                                            <a-switch v-model:checked="toggleCopyToRemoteStatus" :default-value="toggleCopyToRemoteStatus" @change="handleWindowFocus"></a-switch>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                          </a-tooltip>

                            <!-- 特殊键组合 -->
                          <a-tooltip title="组合键">
                            <a-dropdown :trigger="['click']" placement="bottomRight" :disabled="!connected">
                                <a-button shape="circle" :disabled="!connected">
                                    <template #icon>
                                        <KeyOutlinedIcon class="w-18px h-18px mt-2px" />
                                    </template>
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item key="ctrl-alt-del" @click="sendKeyCombination('ctrl+alt+del')">
                                            <interaction-outlined/>
                                            Ctrl+Alt+Del
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                          </a-tooltip>

                            <!-- 分辨率调整 -->
                          <a-tooltip title="分辨率">
                            <a-dropdown :trigger="['click']" placement="bottomRight" :disabled="!connected">
                                <a-button shape="circle" :disabled="!connected">
                                    <template #icon>
                                        <setting-outlined/>
                                    </template>
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item key="resolution-title" class="menu-title" disabled>
                                            <setting-outlined/>
                                            调整远程分辨率
                                        </a-menu-item>
                                        <a-menu-divider/>
                                        <a-menu-item
                                                v-for="option in resolutionOptions"
                                                :key="option.value"
                                                @click="selectedResolution = option.value"
                                                :class="{ 'selected-item': selectedResolution === option.value }"
                                        >
                                            <check-outlined v-if="selectedResolution === option.value"/>
                                            <span :style="{ marginLeft: selectedResolution === option.value ? '0' : '24px' }">
                                                {{ option.label }}
                                            </span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                          </a-tooltip>


                            <!-- 音频控制 -->
<!--                            <a-tooltip :title="audioEnabled ? '静音' : '开启音频'" >-->
<!--                                <a-button shape="circle" @click="toggleAudio" :disabled="!connected">-->
<!--                                    <template #icon>-->
<!--                                        <sound-outlined v-if="audioEnabled"/>-->
<!--                                        <sound-outlined v-else class="muted-icon"/>-->
<!--                                    </template>-->
<!--                                </a-button>-->
<!--                            </a-tooltip>-->

                            <!-- 全屏 -->
                            <a-tooltip title="全屏显示 (F11)">
                                <a-button shape="circle" @click="toggleFullscreen">
                                    <template #icon>
                                        <fullscreen-outlined v-if="!fullscreen"/>
                                        <fullscreen-exit-outlined v-else/>
                                    </template>
                                </a-button>
                            </a-tooltip>

                            <!-- 重新连接 -->
                            <a-tooltip title="重试连接">
                                <a-button shape="circle" @click="fetchConnectionInfo()">
                                    <template #icon>
                                        <reload-outlined/>
                                    </template>
                                </a-button>
                            </a-tooltip>

                            <!-- 断开连接 -->
                            <a-tooltip title="断开连接">
                                <a-button shape="circle" type="primary" danger @click="disconnect">
                                    <template #icon>
                                        <disconnect-outlined/>
                                    </template>
                                </a-button>
                            </a-tooltip>

                            <!-- 在右侧操作区域添加GPU加速按钮 -->
                            <!-- <a-dropdown :trigger="['click']" placement="bottomRight" :disabled="!gpuAccelerationAvailable">
                                <a-button shape="circle" :disabled="!gpuAccelerationAvailable">
                                    <template #icon>
                                        <thunderbolt-outlined :class="{'text-blue-500': gpuAcceleration && gpuAccelerationAvailable}" />
                                    </template>
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item key="gpu-title" class="menu-title" disabled>
                                            <thunderbolt-outlined />
                                            GPU加速渲染设置
                                        </a-menu-item>
                                        <a-menu-divider />
                                        <a-menu-item key="toggle-gpu" @click="toggleGpuAcceleration">
                                            <check-outlined v-if="gpuAcceleration" />
                                            <span :style="{ marginLeft: gpuAcceleration ? '0' : '24px' }">
                                                {{ gpuAcceleration ? '已启用GPU加速' : '启用GPU加速' }}
                                            </span>
                                        </a-menu-item>
                                        <a-menu-divider />
                                        <a-menu-item key="render-mode-title" disabled>
                                            <setting-outlined />
                                            渲染模式
                                        </a-menu-item>
                                        <a-menu-item
                                            v-for="mode in ['auto', 'gpu', 'cpu']"
                                            :key="mode"
                                            @click="selectRenderMode(mode)"
                                            :class="{ 'selected-item': renderMode === mode }"
                                        >
                                            <check-outlined v-if="renderMode === mode" />
                                            <span :style="{ marginLeft: renderMode === mode ? '0' : '24px' }">
                                                {{ getRenderModeName(mode) }}
                                            </span>
                                        </a-menu-item>
                                        <a-menu-divider />
                                        <a-menu-item key="gpu-info" disabled>
                                            <span class="text-xs opacity-80 break-all">{{ gpuInfo || '正在检测GPU信息...' }}</span>
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown> -->
                        </a-space>
                    </div>

                </div>
                <!-- 折叠/展开控制按钮 -->
                <div class="header-toggle" @click="toggleHeader" :class="{ 'fixed': isToolbarFixed }">
                    <down-outlined v-if="headerCollapsed"/>
                    <up-outlined v-else/>
                </div>
            </div>

            <div v-if="connected && isMobile" class="pos-absolute z-999"
                 :style="floatingPanelPosition"   id="mobile-ball-container"
                 @touchstart="startDrag"
                 @touchmove="onDrag"
                 @touchend="endDrag">
              <a-dropdown :trigger="['click']" overlayStyle="width:100px" @click.prevent
                          :getPopupContainer="triggerNode => triggerNode.parentNode">
                <div class="w-54px h-54px rounded-50% bg-#D5D5D5 flex-center flex-col">
                  <img src="@/assets/images/desktop/add.webp" class="w-36px h-36px" alt="">
                </div>
                <template #overlay>
                  <a-menu @click="handleMobileMenuClick">
                    <a-menu-item key="clipboard">
                      <div>剪贴板</div>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="key-combination">
                      <div>组合键</div>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="resolution">
                      <div>重新连接</div>
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="disconnect">
                      <div>断开连接</div>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <div class="w-54px h-54px rounded-50% bg-#D5D5D5 flex-center mt-5px" @click="toggleVirtualKeyboard">
                <img src="@/assets/images/desktop/keyboard.webp" class="w-36px h-36px" alt="">
              </div>
              <input
                  type="text"
                  ref="virtualKeyboardInput"
                  class="virtual-keyboard-input"
                  @input="handleVirtualKeyInput"
                  placeholder="点击此处输入文本..."
              />
            </div>

            <!-- Guacamole 显示区域 -->
            <div class="guac-display-container relative flex-1">
                <div v-if="errorMessage"
                     class="relative error-message flex flex-col items-center justify-center h-full z-2">
                    <warning-outlined class="text-36px text-red-500 mb-4"/>
                    <p class="text-16px mb-2">{{ errorMessage }}</p>
                    <div class="flex items-center justify-center mt-5px">
                      <a-button @click="backToList" class="mr-4">
                        返回列表
                      </a-button>
                      <a-button @click="showCustomServe" class="mr-4" :loading="connecting">
                        联系客服
                      </a-button>
                      <a-button type="primary" @click="connectToCloudDesktop"  :loading="connecting">
                        重试连接
                      </a-button>
                    </div>
                </div>
                <div v-else-if="connecting" class="connecting-message flex flex-col items-center justify-center h-full">
                    <a-spin tip="正在连接远程桌面，请稍候..." size="large"/>
                </div>
                <div v-show="connected" ref="guacDisplayRefs" class="guac-display w-full h-full"></div>

                <div v-if="!errorMessage && !connected && !connecting"
                     class="relative error-message flex flex-col items-center justify-center h-full z-2">
                    <p class="text-16px mb-2">连接已断开</p>
                    <div class="flex items-center justify-center mt-5px">
                        <a-button @click="backToList" class="mr-4">
                            返回列表
                        </a-button>
                        <a-button @click="showCustomServe" class="mr-4" :loading="connecting">
                            联系客服
                        </a-button>
                        <a-button type="primary" @click="connectToCloudDesktop"  :loading="connecting">
                            重试连接
                        </a-button>
                    </div>
                </div>
                <!-- 剪贴板内容通知 -->
<!--                <div v-if="showClipboardNotification" class="clipboard-notification">-->
<!--                    <check-circle-outlined/>-->
<!--                    远程内容已复制到本地剪贴板-->
<!--                </div>-->

                <!-- 当前分辨率显示 -->
                <div v-if="connected && !fullscreen" class="resolution-badge">
                    {{ nativeWidth }} × {{ nativeHeight }}
                </div>

                <!-- 在guac-display-container中添加性能监控面板 -->
                <div v-if="connected && showPerformancePanel" class="performance-panel">
                    <div class="panel-header">
                        <span>性能监控</span>
                        <a-button type="text" size="small" @click="resetPerformanceStats">
                            <reload-outlined/>
                            重置
                        </a-button>
                    </div>
                    <div class="panel-content">
                        <div class="stat-row">
                            <span class="stat-label">当前帧率:</span>
                            <span class="stat-value" :class="{'text-green-500': frameRate > 25, 'text-yellow-500': frameRate <= 25 && frameRate >= 15, 'text-red-500': frameRate < 15}">{{ frameRate }} FPS</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">计算方法:</span>
                            <span class="stat-value">
                                <a-radio-group v-model:value="frameRateMethod" size="small" button-style="solid">
                                    <a-radio-button value="simple">简单</a-radio-button>
                                    <a-radio-button value="average">平均</a-radio-button>
                                    <a-radio-button value="weighted">加权</a-radio-button>
                                </a-radio-group>
                            </span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">平均帧率:</span>
                            <span class="stat-value">{{ getAverageFrameRate() }} FPS</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">最小帧率:</span>
                            <span class="stat-value">{{ minFrameRate === 9999 ? '-' : minFrameRate }} FPS</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">最大帧率:</span>
                            <span class="stat-value">{{ maxFrameRate }} FPS</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">帧间隔:</span>
                            <span class="stat-value">{{ currentFrameInterval.toFixed(2) }} ms</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">帧时间方差:</span>
                            <span class="stat-value" :class="{'text-green-500': frameTimeVariance < 1000, 'text-yellow-500': frameTimeVariance >= 1000 && frameTimeVariance < 5000, 'text-red-500': frameTimeVariance >= 5000}">{{ frameTimeVariance.toFixed(2) }}</span>
                            <a-tooltip>
                                <template #title>方差越小表示帧率越稳定</template>
                                <question-circle-outlined class="ml-1 text-gray-400" />
                            </a-tooltip>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">网络延迟:</span>
                            <span class="stat-value" :class="{'text-green-500': networkLatency < 50, 'text-yellow-500': networkLatency >= 50 && networkLatency < 100, 'text-red-500': networkLatency >= 100}">{{ networkLatency }} ms</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">总接收帧数:</span>
                            <span class="stat-value">{{ totalFramesReceived }}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">会话时长:</span>
                            <span class="stat-value">{{ getSessionDuration() }}</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">渲染模式:</span>
                            <span class="stat-value">
                                {{ gpuAcceleration ? 'GPU' : 'CPU' }}
                                <span v-if="gpuAcceleration && gpuAccelerationAvailable" class="text-blue-500">
                                    <thunderbolt-outlined class="ml-1"/>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部信息 -->
            <!-- <div class="connect-footer bg-#f0f2f5 p-2 text-center text-12px text-#666" v-if="connected && !fullscreen">
                <p>按 F11 全屏/退出全屏 | 使用 Ctrl+Alt+Delete 发送组合键到远程系统 | <a href="#"
                                                                                         @click.prevent="pasteFromLocal">粘贴到远程</a>
                    | <a href="#" @click.prevent="adaptToWindow">适应窗口大小</a></p>
            </div> -->
            <a-modal
                    v-model:visible="customVisible"
                    :width="260"
                    title="联系客服"
                    centered
                    :footer="null"
                    :body-style="{ padding: 0 }">
                <custorm-sever
                        :width="260"
                        :status="undefined"/>
            </a-modal>
        </div>
    </RemoteLayout>
</template>

<style lang="less" scoped>
.cloud-desktop-connect-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;

  .connect-header-container {

    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    width: 100%;
    pointer-events: none;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;

    &.collapsed {
      transform: translateY(-100%);
    }

    .connect-header {
      -webkit-app-region: drag;
      app-region: drag;

      min-width: max-content;
      pointer-events: initial;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 20px;
      background-color: rgba(3, 89, 72, 0.6);
      backdrop-filter: blur(8px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      width: 60%;
      border-radius: 0 0 6px 6px;
      transform: translateY(0);
      transition: all 0.3s ease;
      color: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      .left-info {
        .back-btn {
          color: #fff;
          opacity: 0.85;

          &:hover {
            opacity: 1;
            color: #1890ff;
          }
        }

        .divider {
          background-color: rgba(255, 255, 255, 0.2);
        }

        .desktop-name {
          color: #fff;
          font-weight: 500;
          max-width: 220px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .ant-tag {
          border: none;
        }
      }

      .right-actions {
        -webkit-app-region: no-drag;
        app-region: no-drag;
        .ant-btn {
          color: white;
          border-color: rgba(255, 255, 255, 0.25);
          background-color: rgba(0, 0, 0, 0.3);
          transition: all 0.3s;

          &:hover {
            //background-color: rgba(8, 193, 138, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .header-toggle {
      pointer-events: initial;
      position: absolute;
      right: 50%;
      transform: translateX(50%);
      bottom: -18px;
      width: 48px;
      height: 18px;
      background-color: rgba(0, 0, 0, 0.75);
      border-radius: 0 0 24px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-top: none;
      z-index: 1000;
      transition: all 0.3s ease;

      &.collapsed {
        bottom: 0;
      }

      .anticon {
        font-size: 14px;
        transition: all 0.3s;
      }

      &:hover {
        background-color: var(--primary-color);

        .anticon {
          transform: scale(1.2);
          color: #fff;
        }
      }
    }
  }

  .guac-display-container {
    width: 100%;
    height: 100%;
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #121212;
    transition: transform 0.3s ease;
    transform: translateZ(0);

    .error-message,
    .connecting-message {
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 20px;
      border-radius: 4px;
    }

    .guac-display {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      //cursor: none;
    }

    .clipboard-notification {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 1000;
      animation: fadeInOut 3s ease-in-out;
    }

    .resolution-badge {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10;
    }

  }
}


.menu-title {
  font-weight: bold;
  color: var(--primary-color) !important;
}

.selected-item {
  background-color: #e6f7ff;
  color: var(--primary-color);
}

.muted-icon {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 18px;
    background-color: #f5222d;
    transform: rotate(45deg);
    left: 50%;
    top: 50%;
    margin-top: -9px;
    margin-left: -1px;
  }
}

:deep(.guac-keyboard) {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.7);
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 添加媒体查询和响应式样式 */
@media (max-width: 768px) {
  .connect-header-container {
    .connect-header {
      padding: 8px 12px;

      .left-info {
        .desktop-name {
          max-width: 120px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .right-actions {
        .ant-space {
          gap: 4px !important;
        }

        .ant-btn {
          width: 32px;
          height: 32px;
          font-size: 12px;
        }
      }
    }

    .header-toggle {
      width: 42px;
      height: 16px;

      .anticon {
        font-size: 12px;
      }
    }
  }
}

/* 添加工具提示样式 */
:deep(.ant-tooltip-inner) {
  background-color: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(8px);
}

.performance-panel {
  position: absolute;
  top: 60px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 220px;
  z-index: 10;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
    font-size: 14px;

    .ant-btn {
      color: rgba(255, 255, 255, 0.7);
      padding: 0 4px;

      &:hover {
        color: white;
      }
    }
  }

  .panel-content {
    padding: 8px 12px;

    .stat-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;
      font-size: 12px;
      line-height: 1.5;

      .stat-label {
        color: rgba(255, 255, 255, 0.7);
      }

      .stat-value {
        font-weight: 500;
        font-family: monospace;
      }
    }
  }
}

.virtual-keyboard-input {
  position: fixed;
  left: -99999999999px;
  top: -99999999999px;
  font-size: 16px;
  transform: translateZ(0);
}

</style>
