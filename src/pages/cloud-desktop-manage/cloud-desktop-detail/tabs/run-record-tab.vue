<script setup lang="ts">
import type {TableColumnsType} from 'ant-design-vue'
import { message } from 'ant-design-vue'
import type {CloudDesktopRunRecord} from '~/types/cloud-desktop';
import { getUserInstanceRunRecords } from '@/api/common/cloud-desktop';

// 定义组件props
interface RunRecordTabProps {
    desktopId: string
}

const props = defineProps<RunRecordTabProps>()

// 表格状态
const loading = ref(false)
const dataSource = ref<CloudDesktopRunRecord[]>([])
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total: number) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50'],
})

// 表格列定义
const columns: TableColumnsType = [
    {
        title: '开机时间',
        dataIndex: 'onTime',
        key: 'onTime',
    },
    {
        title: '关机时间',
        dataIndex: 'offTime',
        key: 'offTime',
    },
    {
        title: '持续时长',
        dataIndex: 'timeConsume',
        key: 'timeConsume',
    }
]

// 获取运行记录数据
const fetchRunRecords = async () => {
    if (!props.desktopId) return

    loading.value = true
    try {
        // 这里应该调用API获取运行记录数据
        const response = await getUserInstanceRunRecords({
            id: props.desktopId,
            pageNum: pagination.current,
            pageSize: pagination.pageSize
        });

        dataSource.value = [...response.payload] as CloudDesktopRunRecord[];
        pagination.total = response.pageInfo.total
    } catch (error) {
        console.error('获取运行记录失败', error)
        message.error('获取运行记录失败')
    } finally {
        loading.value = false
    }
}

// 表格分页变化
const handleTableChange = (pag: any) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchRunRecords()
}

// 监听desktopId变化
watch(() => props.desktopId, (newVal) => {
    if (newVal) {
        fetchRunRecords()
    }
})

// 组件挂载时获取数据
onMounted(() => {
    if (props.desktopId) {
        fetchRunRecords()
    }
})
</script>

<template>
    <div class="run-record-tab">
        <a-table
                row-key="id"
                :loading="loading"
                :columns="columns"
                :data-source="dataSource"
                :pagination="pagination"
                @change="handleTableChange"
        />
    </div>
</template> 