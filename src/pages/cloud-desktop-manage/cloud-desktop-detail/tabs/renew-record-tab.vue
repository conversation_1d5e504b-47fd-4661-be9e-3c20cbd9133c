<script setup lang="tsx">
import type {TableColumnsType} from 'ant-design-vue'
import type {CloudDesktopEffectRecord} from '~/types/cloud-desktop'
import { EipTypeMapping, EipTypeTextEnum } from '~#/constant.ts'

const props = withDefaults(defineProps<{
    dataSource: CloudDesktopEffectRecord[]
}>(), {
    dataSource: () => []
})

// 表格状态
const loading = ref(false)

const columns: TableColumnsType = [
    {
        title: '续费时间',
        dataIndex: 'renewalTime',
        key: 'renewalTime',
        width: 120,
    },
    {
        title: '网络地址',
        dataIndex: 'networkAddress',
        key: 'networkAddress',
        width: 120,
    },
    {
        title: '规格',
        dataIndex: 'effectContent',
        key: 'effectContent',
        width: 150,
        customRender: ({text, record}: { text: string, record: any }) => (<p>
            地区: {record.zoneName || '-'}<br/>
            配置：CPU: {record.vcpu || '-'}核 内存: {record.memory || '-'}G <br/>
            操作系统: {record.systemName || '-'}<br/>
            套餐: {record.stMenu || '-'}<br/>
        </p>)
    },
    {
        title: '生效类型',
        dataIndex: 'type',
        key: 'type',
        width: 120,
        customRender: ({ text }: { text: any }) => EipTypeTextEnum[text] || '/'
    },
]

const dataArr = computed(() => {
    return props.dataSource?.filter(item => {
       return item.type === EipTypeMapping.RENEWAL
    }) || []
})
</script>

<template>
    <div class="effect-record-tab">
        <t-table
                :scroll="{ x: 'max-content' }"
                row-key="id"
                :loading="loading"
                :columns="columns"
                :data-source="dataArr"
        />
    </div>
</template>