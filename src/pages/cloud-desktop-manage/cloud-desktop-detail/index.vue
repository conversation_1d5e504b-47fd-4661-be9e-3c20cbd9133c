<script setup lang="ts">
import { CopyOutlined } from '@ant-design/icons-vue'
import {message} from 'ant-design-vue'

// 引入Tab组件
import RunRecordTab from './tabs/run-record-tab.vue'
import EffectRecordTab from './tabs/effect-record-tab.vue'
import RenewRecordTab from './tabs/renew-record-tab.vue'

import type {
    CloudDesktopDetail,
    CloudDesktopRunRecord,
    CloudDesktopEffectRecord,
    CloudDesktopRenewRecord
} from '~/types/cloud-desktop'
import { getUserInstanceDetail } from '~/api/common/cloud-desktop'

import BlueDesktop from '@/assets/images/common/desktop-blue.png'

// 定义组件属性
interface DesktopDetailProps {
    visible: boolean
    desktopId?: string
}

// 定义组件事件
const emit = defineEmits<{
    (e: 'update:visible', visible: boolean): void
    (e: 'close'): void
}>()

const props = withDefaults(defineProps<DesktopDetailProps>(), {
    visible: false,
    desktopId: ''
})

// 抽屉状态
const loading = ref(false)

const setActiveKeyMap = {
    run: 'run',
    effect: 'effect',
    renew: 'renew'
}
const activeTabKey = ref(setActiveKeyMap.run)
const setActiveKey = (key: string) => {
    activeTabKey.value = key
}
// 云电脑详情信息
const desktopDetail = reactive<CloudDesktopDetail>({})

// 复制IP地址
const copyIpAddress = (ip: string) => {
    navigator.clipboard.writeText(ip)
        .then(() => {
            message.success('复制成功')
        })
        .catch(() => {
            message.error('复制失败，请手动复制')
        })
}

// 关闭抽屉
const closeDrawer = () => {
    emit('update:visible', false)
    emit('close')
}

// 获取详情数据
const fetchDesktopDetail = async () => {
    if (!props.desktopId) return

    loading.value = true
    try {
        const response = await getUserInstanceDetail(Number(props.desktopId))
        if (response.success && response.payload) {
            Object.assign(desktopDetail, response.payload)
        } else {
            Object.assign(desktopDetail, {})
            message.error('获取云电脑详情失败')
        }
    } catch (error) {
        console.error('获取云电脑详情失败', error)
        message.error('获取云电脑详情失败')
    } finally {
        loading.value = false
    }
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
    if (newVal && props.desktopId) {
        fetchDesktopDetail()
    }
})

// 监听desktopId变化
watch(() => props.desktopId, (newVal) => {
    if (props.visible && newVal) {
        fetchDesktopDetail()
    }
})
</script>

<template>
    <a-drawer
            :visible="visible"
            :width="590"
            :title="null"
            placement="right"
            :closable="false"
            @close="closeDrawer"
            :body-style="{ paddingBottom: '80px', paddingLeft: '20px', paddingRight: '20px', background: 'linear-gradient( 180deg, #EDF2FF 0%, #FFFFFF 7%, #FFFFFF 100%)' }"
    >
        <a-spin :spinning="loading">
            <div class="flex justify-between items-center">
                <div class="text-16px font-medium">云电脑详情</div>
            </div>
            <a-divider class="my-15px"/>
            <!-- 云电脑基本信息 -->
            <div class="desktop-info">
                <div class="flex items-center px-15px py-10px bg-#F4F5FA border-color-#E7E7E7 border-solid border-1px line-height-28px">
                    <t-image :src="BlueDesktop" class="w-24px h-24px mr-6px line-height-24px"></t-image>
                    {{ desktopDetail.desktopName }}
                </div>
                <h3 class="text-16px font-medium mb-4 mt-15px">远程信息</h3>

                <div class="info-card">
                    <div class="info-item">
                        <div class="label w-140px">网络地址</div>
                        <div class="value flex items-center">
                            {{ desktopDetail.networkAddress }}
                            <a-button
                                    type="link"
                                    class="ml-1 p-0"
                                    @click="() => copyIpAddress(desktopDetail.networkAddress)"
                            >
                                <CopyOutlined/>
                            </a-button>
                        </div>
                    </div>
                    <!-- <div class="info-item">
                        <div class="label w-140px">远程连接地址</div>
                        <div class="value flex items-center">
                            {{ desktopDetail.networkAddress }}:{{ desktopDetail.networkPort }}
                            <a-button
                                    type="link"
                                    class="ml-1 p-0"
                                    @click="() => copyIpAddress(`${desktopDetail.networkAddress}:${desktopDetail.networkPort}`)"
                            >
                                <CopyOutlined/>
                            </a-button>
                        </div>
                    </div> -->
                    <!-- <div class="info-item" v-if="desktopDetail.networkPort">
                        <div class="label w-140px">网络端口</div>
                        <div class="value">{{ desktopDetail.networkPort }}</div>
                    </div> -->
                </div>

                <h3 class="text-16px font-medium mb-4 mt-15px">云电脑规格</h3>
                <div class="info-card grid grid-cols-2">
                    <div class="info-item">
                        <div class="label">地区</div>
                        <div class="value">{{ desktopDetail.zoneName || '-' }}</div>
                    </div>
                    <div class="info-item">
                        <div class="label">套餐</div>
                        <div class="value">{{ desktopDetail.stMenu || '-' }}</div>
                    </div>
                    <div class="info-item col-span-2">
                        <div class="label">操作系统</div>
                        <div class="value">{{ desktopDetail.systemName || '-' }}</div>
                    </div>
                    <!-- <div class="info-item" v-if="desktopDetail.vCpu">
                        <div class="label">CPU</div>
                        <div class="value">{{ desktopDetail.vCpu }}</div>
                    </div> -->
                    <!-- <div class="info-item" v-if="desktopDetail.memory">
                        <div class="label">内存</div>
                        <div class="value">{{ desktopDetail.memory }}</div>
                    </div> -->
                    <!-- <div class="info-item" v-if="desktopDetail.systemDisk">
                        <div class="label">系统盘</div>
                        <div class="value">{{ desktopDetail.systemDisk }}</div>
                    </div> -->
                    <div class="info-item col-span-2">
                        <div class="label">配置</div>
                        <div class="value">
                            CPU: {{ desktopDetail.vcpu || '-' }}核
                            内存: {{ desktopDetail.memory || '-' }}G
                            系统盘: {{ desktopDetail.systemDisk || '-' }}G
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab切换 -->
            <div class="flex justify-start items-center py-20px">
                <a-button :type="activeTabKey == setActiveKeyMap.run ? 'primary':'info'" :class="activeTabKey == setActiveKeyMap.run ? 'primary-bg-btn':'info-btn'"  shape="round" ghost class="w-88px mr-12px" @click="setActiveKey(setActiveKeyMap.run)">运行记录</a-button>
                <a-button :type="activeTabKey == setActiveKeyMap.effect ? 'primary':'info'" :class="activeTabKey == setActiveKeyMap.effect ? 'primary-bg-btn':'info-btn'" shape="round" ghost class="w-88px mr-12px" @click="setActiveKey(setActiveKeyMap.effect)">生效记录</a-button>
                <a-button :type="activeTabKey == setActiveKeyMap.renew ? 'primary':'info'" :class="activeTabKey == setActiveKeyMap.renew ? 'primary-bg-btn':'info-btn'" shape="round" ghost class="w-88px" @click="setActiveKey(setActiveKeyMap.renew)">续费记录</a-button>
            </div>

            <template v-if="activeTabKey === setActiveKeyMap.run">
                <RunRecordTab :desktop-id="desktopId"/>
            </template>
            <template v-else-if="activeTabKey === setActiveKeyMap.effect">
                <EffectRecordTab :dataSource="desktopDetail.eipRecords" />
            </template>
            <template v-else-if="activeTabKey === setActiveKeyMap.renew">
                <RenewRecordTab :dataSource="desktopDetail.eipRecords" />
            </template>
        </a-spin>
    </a-drawer>
</template>

<style scoped lang="less">
.desktop-info {
  .info-card {
    border-left: 1px solid #E5E6EB;
    border-right: 1px solid #E5E6EB;
    border-radius: 4px;
  }

  .info-item {
    display: flex;
    align-items: center;
    border-top: 1px solid #E5E6EB;

    .label {
      font-size: 16px;
      min-width: 108px;
      padding: 10px 20px;
      color: #86909C;
      background: #F9F9F9;
      flex-shrink: 0;

    }

    .value {
      font-size: 16px;
      line-height: 16px;
      font-weight: 400;
      color: #3D3D3D;
      padding: 0 14px;
    }

    &:last-child {
      border-bottom: 1px solid #E5E6EB;
    }
  }
}
</style>