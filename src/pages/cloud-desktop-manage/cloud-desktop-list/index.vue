<script setup lang="tsx">
import {useRouter, useRoute} from 'vue-router'

import {ReloadOutlined, SearchOutlined, FormOutlined, CheckCircleOutlined, CloseCircleOutlined} from '@ant-design/icons-vue';
import type {TableColumnsType} from 'ant-design-vue'
import {message} from 'ant-design-vue'
import type {
    CloudDesktopRecord,
    CloudDesktopOperationType,
    CloudDesktopFilterForm
} from '~/types/cloud-desktop';


import {getUserInstanceList, startInstance, stopInstance, updateInstanceRemark} from '~/api/common/cloud-desktop'

// 使用组件
import PageContainer from '~/components/page-container/index.vue'
import StatusTag from '~/components/status-tag/index.vue'
import DesktopOperations from './components/desktop-operations/index.vue'
import DesktopDetail from '../cloud-desktop-detail/index.vue'
import DesktopEditModal from './components/desktop-edit-modal/index.vue'
import dayjs from "dayjs";
import {useAuthentication} from "~/hooks/useAuthentication.ts";

import {useCheckDueInstance} from "~/hooks/useCheckDueInstance.ts";

import { useDownload } from '@/composables/use-download'

 const { open, close } = useDownload()
const router = useRouter()
const route = useRoute()
// 列表状态
const loading = ref(false)
const selectedRowKeys = ref<number[]>([])
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total: number) => `共 ${total} 条`,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50'],
});
const params = reactive({
    desktopName: '',
    expreTime:'',
    label:'',
});
const userStore = useUserStore()
const {userInfo} = storeToRefs(userStore)

const FilterFormStatus = {
    running: 'running',
    expired: 'expired',
    all: 'all'
}

// 详情抽屉状态
const detailDrawerVisible = ref(false)
const currentDesktopId = ref('')

const currentEditDesktopId = ref('')
const currentEditDesktopName = ref('')

// 添加编辑弹窗状态
const editModalVisible = ref(false)
const desktopEditModalRef = ref(null)
const currentEditDesktop = ref({
    id: 0,
    desktopName: '',
    remark: '',
    labelList:[]
})

// 过滤条件
const filterForm = reactive<CloudDesktopFilterForm>({
    status: 'all',
    // name: '',
})

// 表格列定义
const columns: TableColumnsType = [
    {
        title: '云电脑名称',
        dataIndex: 'desktopName',
        key: 'desktopName',
        width: 240,
    },
    {
        title: '网络地址',
        dataIndex: 'networkAddress',
        key: 'networkAddress',
        width: 150,
        customRender: ({text}) => text || '-',
    },
    {
        title: '设备区域',
        dataIndex: 'zoneName',
        key: 'zoneName',
        width: 150,
    },
    {
        title: '到期时间',
        dataIndex: 'expreTime',
        key: 'expreTime',
        width: 150,
        customRender: ({text, record}) => {
            if (text) {
                if (dayjs(text).isBefore(dayjs()) || ['CAN_BE_RENEWED', 'CLEAR'].includes(record.expreState)) {
                    return (<p>
                        {dayjs(text).format('YYYY-MM-DD')}
                        <br/>
                        <p class="color-#F53F3F cursor-pointer">
                            (<span>已过期</span>
                            <span
                                v-if="record.expreState === 'CAN_BE_RENEWED'">，去续费&gt;</span>)

                        </p>
                    </p>)
                } else {
                    return (<p>
                        {dayjs(text).format('YYYY-MM-DD')}
                        <br/>
                        <span class="color-#86909C">(剩余{dayjs(text).diff(dayjs(), 'day')}天)</span>
                    </p>)
                }
            }
            return '-'
        }
    },
    {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        customRender: ({text}: { text: string | null }) => {
            return text || '-'
        },
        width: 150,
    },
    {
        title: '标签',
        dataIndex: 'label',
        key: 'label',
        width: 200,
    },
    {
        title: '设备状态',
        dataIndex: 'deviceState',
        key: 'deviceState',
        width: 100,
    },
    {
        title: '远程状态',
        dataIndex: 'remoteState',
        key: 'remoteState',
        width: 100,
    },
    {
        title: '操作',
        key: 'operation',
        fixed: 'right',
        width: 200,
    },
]

// 数据源
const dataSource = ref<CloudDesktopRecord[]>([])

const expreStateEnumMap: any = {
    'all': '',
    'running': 'DUE',
    'expired': 'CAN_BE_RENEWED,CLEAR',
}

// 获取列表数据
const fetchData = async () => {
    loading.value = true
    try {
        // 调用API接口获取数据，调整参数名称
        const response = await getUserInstanceList({
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            expreState: expreStateEnumMap[filterForm.status] || '', // 过期状态
            desktopName: params.desktopName || '', // 云电脑名称
            expreTime: params.expreTime || '', // 到期时间
            label: params.label || '', // 标签
            // deviceState: '', // 设备状态，可根据需要添加到筛选条件
            // remoteState: '' // 远程状态，可根据需要添加到筛选条件
        })

        if (response.success && response.payload) {
            // 调整获取数据的方式，适应新的API返回格式
            dataSource.value = [...response.payload];
            // 更新分页信息
            pagination.total = response.pageInfo.total || 0
        } else {
            message.error(response.message || '获取云电脑列表失败')
            dataSource.value = []
            pagination.total = 0
        }
    } catch (error) {
        console.error('获取云电脑列表失败', error)
        message.error('获取云电脑列表失败')
        dataSource.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格行选择变化
const onSelectChange = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys
}

// 表格分页变化
const handleTableChange = (pag: any) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

const { showAuthenticationModal } = useAuthentication()

const doStartInstance: (ids?: number[]) => Promise<boolean> = async (ids: number[] = []) => {
    if(!userInfo.value?.isRealName) {
        showAuthenticationModal();
        return false
    }
    if(!ids || !ids.length) {
        message.error('当前未选中操作设备');
        return false;
    };
    message.loading('正在开机...')
    // 批量启动云电脑，实际项目中应该使用批量API
    let res: any = {};
    try {
        res = await startInstance(ids, "WEB");
    } catch (e) {

    }
    message.destroy();
    if(res?.success) {
        message.success('已发送开机指令，请稍等')
        return true;
    } else {
        message.error(res.message || '开机失败');
        return false;
    }
}

const doStopInstance: (ids?: number[]) => Promise<boolean> = async (ids: number[] = []) => {
    if(!ids || !ids.length) {
        message.error('当前未选中操作设备');
        return false;
    };
    message.loading('正在关机中...')
    let res: any = {};
    try {
        res = await stopInstance(ids, "WEB");
    } catch (e) {

    }
    message.destroy();
    if(res?.success) {
        message.success('已发送关机指令，请稍等')
        return true;
    } else {
        message.error(res.message || '关机失败');
        return false;
    }
}

// 批量操作方法
const handleBatchOperation = useThrottleFn(async (type: string) => {
    if (type === 'group') {
        message.warning('群控功能开发中')
        return;
    }
    if (selectedRowKeys.value.length === 0) {
        message.warning('请先选择操作的云电脑')
        return
    }

    switch (type) {
        case 'start':
            try {
                let startStatus = await doStartInstance([...selectedRowKeys.value.map(id => parseInt(id))]);
                if(startStatus) {
                    fetchData() // 刷新列表
                }
            } catch (error) {
                console.error('批量开机失败', error)
                message.error('批量开机失败')
            }
            break
        case 'stop':
            try {

                // 批量关闭云电脑，实际项目中应该使用批量API
                let stopRes = await doStopInstance([...selectedRowKeys.value.map(id => parseInt(id))]);
                if(stopRes) {
                    fetchData() // 刷新列表
                }
            } catch (error) {
                console.error('批量关机失败', error)
                message.error('批量关机失败')
            }
            break
        case 'renew':
            const logicIds = dataSource.value.filter((item)=>selectedRowKeys.value.includes(item.id) && item.logicId).map((item)=>item.logicId);
            router.push(`/product/pay?logicIds=${logicIds}`)
            break
        case 'group':
            message.warning('群控功能开发中')
            break
        default:
            break
    }
}, 1000);

// 处理单个云电脑操作
const handleDesktopOperation = async (type: CloudDesktopOperationType, record: CloudDesktopRecord, isFocus:Boolean = false) => {
    switch (type) {
        case 'start':
            try {
                let startRes = await doStartInstance([record.id]);
                if(startRes) {
                    setTimeout(() => {
                        fetchData() // 刷新列表
                    }, 2000)
                }
            } catch (error) {
                console.error('开机失败', error)
                message.error(`开机失败: ${record.desktopName}`)
            }
            break
        case 'stop':
            try {
                let stopRes = await doStopInstance([Number(record.id)]);
                if(stopRes) {
                    setTimeout(() => {
                        fetchData() // 刷新列表
                    }, 2000)
                }
            } catch (error) {
                console.error('关机失败', error)
                message.error(`关机失败: ${record.desktopName}`)
            }
            break
        case 'edit':
            // 显示编辑弹窗
            currentEditDesktop.value = {
                id: record.id,
                desktopName: record.desktopName || '',
                remark: record.remark || '',
                labelList: record.labelList || [],
            }
            editModalVisible.value = true
            if(isFocus) {
                nextTick(() => {
                    desktopEditModalRef.value?.focus()
                })
            }
            
            
            break;
        case 'localRemote':
            // 弹出下载弹窗
            open()
            break;
        case 'networkRemote':
            // 跳转到远程连接页面
            // 这里使用了 Vue Router 的 resolve 方法来生成链接，确保路由正确解析
            let url = router.resolve({
                path: '/cloud-desktop-connect',
                query: {
                    workspaceId: record.workspaceId,
                    id: record.id,
                    terminalType: "WEB",
                    desktopName: record.desktopName,
                    connectType: 'network',
                    instanceId: record.instanceId
                }
            }).href;
            window.open(url, '_blank');
            break
        case 'renew':
            router.push(`/product/pay?logicIds=${[record.logicId]}`)
            break
        case 'detail':
            showDesktopDetail(record.id)
            break
        default:
            break
    }
}

// 编辑成功后刷新列表
const handleEditSuccess = () => {
    fetchData()
}

// 显示云电脑详情
const showDesktopDetail = (desktopId: any) => {
    currentDesktopId.value = desktopId
    detailDrawerVisible.value = true
}

// 关闭云电脑详情抽屉
const closeDesktopDetail = () => {
    detailDrawerVisible.value = false
}

// 重置筛选
const resetFilter = () => {
    params.desktopName = ''
    params.expreTime = ''
    params.label = ''
    clearEditData()
    fetchData()
}

const handleSearch = () => {
    clearEditData()
    fetchData()
}

// 编辑云电脑
const handleEditDesktopName = (record: CloudDesktopRecord) => {
    console.log('编辑云电脑', record)
    currentEditDesktopId.value = record.id
    currentEditDesktopName.value = record.desktopName
}

const clearEditData = () => {
    currentEditDesktopId.value = ''
    currentEditDesktopName.value = ''
}

// 确认编辑云电脑名称
const handleConfirmEdit = async (record: CloudDesktopRecord) => {
    // 电脑名称未修改
    if(record.desktopName == currentEditDesktopName.value) {
        clearEditData()
        return
    }
    if (currentEditDesktopName.value.trim() === '') {
        message.error('云电脑名称不能为空')
        return
    }
    const params = {
        id: currentEditDesktopId.value,
        desktopName: currentEditDesktopName.value,
    }

    const res = await updateInstanceRemark(params)
    if (res.success) {
      message.success('更新云电脑信息成功')
      fetchData()
      clearEditData()
    } else {
      message.error(res.message || '更新云电脑信息失败')
    }
}

onMounted(() => {
    if(route.query.status){
        filterForm.status = route.query.status
    }
    fetchData()
    useCheckDueInstance()
})
</script>

<template>
    <PageContainer>
        <div class="desktop-manage-container">
            <div class="flex items-center">
                <div class="indicator w-6px h-15px bg-green-500 border-rounded-6px mr-2"></div>
                <div class="text-20px font-500 line-height-28px">云电脑管理</div>
            </div>
            <!-- 操作按钮 -->
            <TCard class="py-16px px-20px mt-20px border-rounded-12px">
                <div class="desktop-manage-operate">
                    <!-- 标签页 -->
                    <div class="tabs-container">
                        <a-tabs class="tabs-container-style" v-model:activeKey="filterForm.status" @change="fetchData">
                            <a-tab-pane :key="FilterFormStatus.all" tab="全部云电脑"/>
                            <a-tab-pane :key="FilterFormStatus.running" tab="即将过期云电脑"/>
                            <a-tab-pane :key="FilterFormStatus.expired" tab="已过期云电脑"/>
                        </a-tabs>
                    </div>
                    <div class="operation-container flex justify-between items-center mb-4">
                        <div class="left-buttons">
                            <a-button type="primary" shape="round" ghost class="primary-bg-btn mr-2"
                                    @click="() => router.push('/product')">
                                购买云电脑
                            </a-button>
                            <template v-if="filterForm.status != FilterFormStatus.expired">
                                <a-button class="info-btn w-102px mr-2" shape="round"
                                        @click="() => handleBatchOperation('start')">
                                    开机
                                </a-button>
                                <a-button class="info-btn w-102px mr-2" shape="round"
                                        @click="() => handleBatchOperation('stop')">
                                    关机
                                </a-button>
                            </template>
                            <a-button class="info-btn w-102px mr-2" shape="round"
                                    @click="() => handleBatchOperation('renew')">
                                续费
                            </a-button>
                            <div relative class="w-[max-content] inline">
                                <a-button class="info-btn w-102px" shape="round"
                                        @click="() => handleBatchOperation('group')">
                                    桌面群控
                                </a-button>
                                <div class="dev-tag">
                                    <span class="text">开发中</span>
                                </div>
                            </div>
                        </div>
                        
                    </div>

                    <div class="flex items-center mb-4">
                        <div class="flex items-center mr-20px">
                            <div class="mr-12px">云电脑名称</div>
                            <a-input v-model:value="params.desktopName" placeholder="请输入云电脑名称" class="w-180px" />
                        </div>
                        <div class="flex items-center mr-20px">
                            <div class="mr-12px">标签</div>
                            <a-input v-model:value="params.label" placeholder="请输入云电脑名称" class="w-180px" />
                        </div>
                        <div class="flex items-center mr-20px">
                            <div class="mr-12px">到期时间</div>
                            <a-date-picker v-model:value="params.expreTime" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"/>
                        </div>
                        <div class="flex items-center mr-20px">
                            <a-button type="primary" class="mr-12px" @click="handleSearch">
                                <template #icon>
                                <SearchOutlined/>
                                </template>
                                查询
                            </a-button>
                            <a-button @click="resetFilter">
                                <template #icon>
                                <ReloadOutlined/>
                                </template>
                                重置
                            </a-button>
                        </div>
                    </div>


                    <!-- 警告提示 -->
                    <div class="warning-tips bg-#FFF7EF rounded-1 mb-4 flex items-center text-#595654">
                        <WaringTips :hideIcon="false" no-bg hideUpdateBtn
                                    tip="提醒：到期7天后仍未续费，云电脑将被系统自动释放，数据不可找回。请及时续费。"/>
                    </div>
                </div>
                <!-- 表格 -->
                <TTable
                        row-key="id"
                        :loading="loading"
                        :columns="columns"
                        :data-source="dataSource"
                        :pagination="pagination"
                        :scroll="{ x: 'max-content' }"
                        :row-selection="{ columnWidth:'46px', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                        @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'desktopName'">
                            <div class="flex items-center">
                                <a-input style="width:172px" placeholder="请输入云电脑名称" v-if="currentEditDesktopId == record.id"  v-model:value="currentEditDesktopName"/>
                                <template v-else>
                                    <a-tooltip>
                                        <template #title>{{record.desktopName}}</template>
                                        <span class="ellipsis max-w-190px">{{ record.desktopName }}</span>
                                    </a-tooltip>
                                </template>
                                <template v-if="currentEditDesktopId == record.id">
                                    <CheckCircleOutlined class="ml-10px cursor-pointer color-[#08C18A]"  @click="handleConfirmEdit(record)"/>
                                    <CloseCircleOutlined class="ml-4px cursor-pointer" @click="clearEditData"/>
                                </template>
                                <FormOutlined @click="handleEditDesktopName(record)" class="ml-10px cursor-pointer color-[#08C18A]" v-else/>
                            </div>
                        </template>
                        <!-- 标签 -->
                        <template v-if="column.key === 'label'">
                            <template v-if="record.labelList && record.labelList.length">
                                <div class="flex items-center">
                                    <a-tooltip placement="top">
                                        <template #title>
                                            <span v-for="(item,index) in record.labelList" :key="index" class="mr-8px">{{item.label}}</span>
                                        </template>
                                        <div style="width:max-content;max-width:168px">{{record.labelList.map((item)=>item.label).join('、')}}</div>
                                    </a-tooltip>
                                    <FormOutlined @click="handleDesktopOperation('edit',record,true)" class="ml-10px cursor-pointer color-[#08C18A]" />
                                </div>
                               
                            </template>
                            <template v-else>
                                <div style="width:max-content;max-width:168px">/</div> 
                            </template>   
                        </template>

                        <!-- 设备状态 -->
                        <template v-if="column.key === 'deviceState'">
                            <StatusTag :status="record.deviceState" type="device"/>
                        </template>

                        <!-- 远程状态 -->
                        <template v-if="column.key === 'remoteState'">
                            <StatusTag :status="record.remoteState" type="remote"/>
                        </template>

                        <!-- 操作 -->
                        <template v-if="column.key === 'operation'">
                            <DesktopOperations
                                    v-if="record"
                                    :record="record"
                                    @operation="handleDesktopOperation"
                            />
                        </template>
                    </template>
                </TTable>
            </TCard>
        </div>
        <!-- 云电脑详情抽屉 -->
        <DesktopDetail
                v-model:visible="detailDrawerVisible"
                :desktop-id="currentDesktopId"
                @close="closeDesktopDetail"
        />

        <!-- 添加云电脑编辑弹窗 -->
        <DesktopEditModal
                ref="desktopEditModalRef"
                v-model:visible="editModalVisible"
                :desktop-info="currentEditDesktop"
                @success="handleEditSuccess"
        />
    </PageContainer>
</template>

<style scoped lang="less">
.dev-tag {
  position: absolute;
  right: -20px;
  top: -22px;
  padding: 0;
  height: 28px;
  font-size: 16px;
  width: 64px;
  background: #FFE6D0;
  line-height: 28px;
  color: #FF8503;
  zoom: 0.5;
  border-radius: 12px 0 12px 0;
  text-align: center;
  font-style: normal;
  text-transform: none;
  .text {
    background: linear-gradient(19.64deg, #FF8503 0%, #FF4A03 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.tabs-container {
  :deep(.ant-tabs-nav-list) {
    .ant-tabs-tab {
      padding: 12px 16px;
    }
    .ant-tabs-ink-bar {
      height: 3px;
      border-radius: 3px;
    }
  }
}
.desktop-manage-container {
  width: 100%;
  padding: 20px;

  .operation-container {
    padding: 8px 0;
  }

  :deep(.ant-pagination) {
    margin-top: 16px;
    text-align: right;
  }
  .desktop-manage-operate{
    :deep(.ant-input) {
    background: #f7f8fa;
    border: none;
  }
    :deep(.ant-select-selector) {
        background: #f7f8fa;
        border: none;
    }
    :deep(.ant-picker) {
        background: #f7f8fa;
        border: none;
    }
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
   }
}
</style>
