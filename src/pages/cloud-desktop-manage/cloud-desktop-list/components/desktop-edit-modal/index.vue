<script setup lang="ts">
import {ref, reactive} from 'vue'
import {Form, message} from 'ant-design-vue'
import {updateInstanceRemark} from '~/api/common/cloud-desktop'

const useForm = Form.useForm

// 定义组件属性
interface DesktopEditModalProps {
    visible: boolean
    desktopInfo: {
        id: number
        desktopName?: string
        remark?: string
    }
}

const labelSelectRef = ref(null)

// 定义组件事件
const emit = defineEmits<{
    (e: 'update:visible', visible: boolean): void
    (e: 'success'): void
}>()

const props = withDefaults(defineProps<DesktopEditModalProps>(), {
    visible: false,
    desktopInfo: () => ({id: 0, desktopName: '', remark: ''})
})

// 表单状态
const formState = reactive({
    desktopName: '',
    remark: '',
    label: [],
})

// 表单规则
const rules = {
    desktopName: [
        {required: true, message: '请输入云电脑名称', trigger: 'blur'},
        {max: 30, message: '名称不能超过30个字符', trigger: 'blur'}
    ],
    remark: [
        {max: 100, message: '备注不能超过100个字符', trigger: 'blur'}
    ],
}

const {resetFields, validate, validateInfos} = useForm(formState, rules)

// 保存状态
const saving = ref(false)

// 监听visible变化
watch(() => props.visible, (newVal) => {
    if (newVal) {
        // 打开弹窗时，初始化表单数据
        formState.desktopName = props.desktopInfo.desktopName || ''
        formState.remark = props.desktopInfo.remark || ''
        formState.label = props.desktopInfo.labelList ? props.desktopInfo.labelList.map(item => item.label) : []
    } else {
        // 关闭弹窗时，重置表单数据
        resetFields()
    }
})

// 监听desktopInfo变化
watch(() => props.desktopInfo, (newVal) => {
    if (props.visible && newVal) {
        formState.desktopName = newVal.desktopName || ''
        formState.remark = newVal.remark || ''
        formState.label = newVal.labelList ? newVal.labelList.map(item => item.label) : []
    }
}, {deep: true})

// 取消编辑
const handleCancel = () => {
    emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
    try {
        await validate()

        // 检查是否有ID
        if (!props.desktopInfo.id) {
            message.error('云电脑ID不能为空')
            return
        }

        saving.value = true

        try {
            // 调用API更新云电脑信息
            let labels: { label: string; parentId: number }[] = []
            if(formState.label.length) {
                formState.label.map((item: string) => {
                    labels.push({
                        label: item,
                        parentId: props.desktopInfo.id
                    })
                })
            }

            
            const res = await updateInstanceRemark({
                id: props.desktopInfo.id,
                desktopName: formState.desktopName,
                remark: formState.remark,
                labels: labels || undefined
            })

            if (res.success) {
                message.success('更新云电脑信息成功')
                emit('success')
                emit('update:visible', false)
            } else {
                message.error(res.message || '更新云电脑信息失败')
            }
        } catch (error: any) {
            console.error('更新云电脑信息失败', error)
            message.error(`更新云电脑信息失败: ${error.message || '未知错误'}`)
        } finally {
            saving.value = false
        }
    } catch (error) {
        console.log('表单验证失败', error)
    }
}

const handleTagChange = (val, record) => {
  // 每个标签最多20个字符
  let uniqueTags = Array.from(new Set(val.map(tag => tag.length > 10 ? tag.slice(0, 10) : tag)));
  // 限制最多5个标签
  if (uniqueTags.length > 5) {
    uniqueTags = uniqueTags.slice(0, 5);
    message.warning('最多只能输入5个标签');
  }
  // 检查是否有被截断的标签
  if (val.some(tag => tag.length > 10)) {
    message.warning('每个标签最多10个字符，已自动截断');
  }
  formState.label = uniqueTags;
}

const focus = () => {
    labelSelectRef.value?.focus?.()
}

defineExpose({
    focus
})
</script>

<template>
    <a-modal
            :visible="visible"
            title="编辑云电脑"
            :width="507"
            :maskClosable="false"
            :footer="null"
            @cancel="handleCancel"
            class="desktop-edit-modal"
            :destroyOnClose="true"
    >
        <a-spin :spinning="saving">
            <a-form
                    layout="horizontal"
                    :label-col="{ span: 5 }"
                    :model="formState"
            >
                <a-form-item
                        label="云电脑名称"
                        v-bind="validateInfos.desktopName"
                >
                    <a-input
                            v-model:value="formState.desktopName"
                            placeholder="请输入云电脑名称"
                            :maxlength="30"
                            allow-clear
                            class="w-full"
                    />
                    <!-- <div class="text-[#86909C] text-xs mt-1">名称最长支持30个字符</div> -->
                </a-form-item>

                <a-form-item
                        label="标签"
                        v-bind="validateInfos.label"
                >
                    <a-select
                        mode="tags"
                        style="width: 100%"
                        placeholder="标签"
                        :maxTagCount="5"
                        :value="formState.label"
                        ref="labelSelectRef"
                        @change="val => handleTagChange(val, formState.label)"
                    ></a-select>
                </a-form-item>

                <a-form-item
                        label="备注"
                        v-bind="validateInfos.remark"
                >
                    <a-textarea
                            v-model:value="formState.remark"
                            placeholder="请输入备注信息"
                            :maxlength="100"
                            :auto-size="{ minRows: 3, maxRows: 5 }"
                            allow-clear
                            class="w-full"
                    />
                    <!-- <div class="text-[#86909C] text-xs mt-1">备注最长支持100个字符</div> -->
                </a-form-item>
            </a-form>

            <div class="modal-footer flex justify-center mt-4">
                <a-button class="w-101px mr-3 info-btn" size="large" @click="handleCancel">取消</a-button>
                <a-button class="w-101px" size="large" type="primary" :loading="saving" @click="handleSubmit">提交
                </a-button>
            </div>
        </a-spin>
    </a-modal>
</template>

<style scoped lang="less">
.desktop-edit-modal {
  :deep(.ant-modal-body) {
    padding: 24px 32px;
  }

  :deep(.ant-form-item-label) {
    label {
      font-size: 14px;
      font-weight: 500;
      color: #212121;
    }
  }

  :deep(.ant-input), :deep(.ant-input-affix-wrapper) {
    border-radius: 4px;
  }

  .modal-footer {
    margin-top: 24px;
  }
}
</style> 