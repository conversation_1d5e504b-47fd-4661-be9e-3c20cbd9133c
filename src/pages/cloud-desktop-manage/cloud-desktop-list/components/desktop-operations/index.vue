<script setup lang="ts">
import type {CloudDesktopRecord, CloudDesktopOperationType} from '~/types/cloud-desktop'
import { AllStatusMapping, InstanceExpiredMapping } from '~#/constant.ts';

import desktopIcon from '@/Icons/monitor.svg';
import globeIcon from '@/Icons/globe.svg';
import walletIcon from '@/Icons/wallet.svg';
import dotMoreIcon from '@/Icons/dots-horizontal.svg';
interface DesktopOperationsProps {
    record: CloudDesktopRecord
}

const props = defineProps<DesktopOperationsProps>()

const emit = defineEmits<{
    (e: 'operation', type: CloudDesktopOperationType, record: CloudDesktopRecord): void
}>()

// 处理操作事件
const handleOperation = (type: CloudDesktopOperationType) => {
    emit('operation', type, props.record)
}

const isRunning = computed(() => [AllStatusMapping.RUNNING, AllStatusMapping.STARTING].includes(props.record.deviceState));
const isStopped = computed(() => [AllStatusMapping.STOPPING, AllStatusMapping.STOPPED].includes(props.record.deviceState));
const isRenewing = computed(() => [InstanceExpiredMapping.CAN_BE_RENEWED, InstanceExpiredMapping.CLEAR].includes(props.record.expreState));
const isRenewed = computed(() => props.record.expreState === InstanceExpiredMapping.CLEAR);

</script>

<template>
    <div class="desktop-operations">
        <a-button
                class="flex items-center"
                type="link"
                size="small"
                @click="() => handleOperation('localRemote')"
        >
           <desktopIcon class="w-16px h-16px mr-1px" />
            本地远程
        </a-button>
        <a-divider type="vertical"/>
        <a-button
                class="flex items-center"
                type="link"
                size="small"
                :disabled="isStopped || isRenewing"
                @click="() => handleOperation('networkRemote')"
        >
            <globeIcon class="w-16px h-16px mr-1px" />
            网络远程
        </a-button>
        <a-button class="flex items-center"
                  type="link"
                  size="small" @click="() => handleOperation('renew')">
            <walletIcon class="w-16px h-16px mr-1px" />
            续费
        </a-button>
        <a-divider type="vertical"/>
        <a-dropdown>
            <a-button class="flex items-center" type="link" size="small">
                <dotMoreIcon class="w-16px h-16px mr-1px" />
                更多
            </a-button>
            <template #overlay>
                <a-menu>
                    <a-menu-item
                            key="start"
                            :disabled="isRunning || isRenewing"
                            @click="() => handleOperation('start')"
                    >
                        开机
                    </a-menu-item>
                    <a-menu-item
                            key="stop"
                            :disabled="isStopped || isRenewing"
                            @click="() => handleOperation('stop')"
                    >
                        关机
                    </a-menu-item>
                    <a-menu-item
                            key="edit"
                            @click="() => handleOperation('edit')">
                        编辑
                    </a-menu-item>
                    <a-menu-item key="detail" @click="() => handleOperation('detail')">
                        详情
                    </a-menu-item>
                </a-menu>
            </template>
        </a-dropdown>
    </div>
</template>

<style scoped lang="less">
.desktop-operations {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 1fr 1fr;
}
</style> 