<template>
  <div class="login-log">
    <CommonPage title="登录日志">
      <div class="log-content">
        <div class="log-header-form ml-16px">
          <div class="from-item">
            <div class="label">云电脑名称</div>
            <a-input v-model:value="params.desktop_name" placeholder="请输入云电脑名称" class="w-180px" />
          </div>
          <div class="from-item">
            <div class="label">终端类型</div>
            <a-select v-model:value="params.terminalType"  @change="handleSelectChange" class="w-150px" placeholder="请选择终端类型">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="WEB">Web</a-select-option>
              <a-select-option value="windows">Windows</a-select-option>
              <a-select-option value="macOS">Mac</a-select-option>
            </a-select>
          </div>
          <div class="from-item" style="margin-right: 16px;">
            <div class="label">登录日期</div>
            <a-range-picker format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" @change="handleRangePicker"/>
          </div>
          <div class="from-item">
            <a-space>  
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <SearchOutlined/>
                </template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined/>
                </template>
                重置
              </a-button>
            </a-space>
          </div>
        </div>
        <div class="mt-30px">
          <a-table
            :columns="columns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :row-class-name="
              (_record, index) => (index % 2 === 1 ? 'table-striped' : null)
            "
            @change="handleTableChange"
          >
          </a-table>
        </div>
      </div>
    </CommonPage>
  </div>
</template>

<script setup>
import CommonPage from "@/components/commom-page/index.vue";
import { reactive, ref, onMounted } from "vue";
import { getLoginRecordsListApi } from "@/api/common/list.ts";
import {ReloadOutlined, SearchOutlined} from '@ant-design/icons-vue';
const columns = [
  {
    title: "登录账号",
    dataIndex: "phone",
    key: "phone",
  },
  {
    title: "云电脑名称",
    dataIndex: "desktopName",
    key: "desktopName",
  },
  {
    title: "设备区域",
    dataIndex: "region",
    key: "region",
  },
  {
    title: "终端类型",
    key: "terminalType",
    dataIndex: "terminalType",
   
  },
  {
    title: "IP地址",
    key: "clientIp",
    dataIndex: "clientIp",
  },
  {
    title: "打开云电脑时间",
    key: "onTime",
    dataIndex: "onTime",
  },
  {
    title: "地区",
    key: "clientRegion",
    dataIndex: "clientRegion",
  },
];

const tableData = ref([]);
const tableLoading = ref(false);
const params = reactive({
  desktop_name: undefined,
  terminalType: "",
  startDateTime: undefined,
  endDateTime: undefined,
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50"],
});
const getList = async () => {
  let param = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    ...params
  };
  if(!param.terminalType){
    delete param.terminalType
  }
  tableLoading.value = true;
  let res = await getLoginRecordsListApi(param);
  tableLoading.value = false;
  if (res.code) {
    console.log(res.data);
    tableData.value = res.payload;
    pagination.total = res.pageInfo.total;
  }
};

// 表格分页变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    getList()
}

const handleSearch = () => {
  getList();
};

const handleReset = () => {
  params.desktop_name = undefined;
  params.terminalType = '';
  params.startDateTime = undefined;
  params.endDateTime = undefined;
  getList();
};


const handleSelectChange = () => {
  getList();
};

const handleRangePicker = (e) =>{
  console.log('handleRangePicker',e)
    if(!e) {
        params.startDateTime = undefined;
        params.endDateTime = undefined;
        getList();
        return;
    }
  try {
     params.startDateTime = e[0] + ' 00:00:00'
    params.endDateTime = e[1] + ' 23:59:59'
    getList();
  } catch(e){
    console.log(e)
  }
 
}

onMounted(() => {
  getList();
});
</script>

<style scoped lang='less'>
.login-log {
  padding: 20px;
  .log-content {
    padding: 20px;
    width: 100%;
    .log-header-form {
      width: 100%;
      display: flex;
      .from-item {
        display: flex;
        align-items: center;
        margin-right: 32px;
        .label {
          margin-right: 16px;
        }
        :deep(.ant-input) {
          background: #f7f8fa;
          border: none;
        }
        :deep(.ant-select-selector) {
          background: #f7f8fa;
          border: none;
        }
        :deep(.ant-picker) {
          background: #f7f8fa;
          border: none;
        }
      }
    }
  }
}

:deep(.table-striped) td {
  background-color: #fafafa;
}
:deep(.ant-table-tbody) {
  td {
    border-top: none !important;
    border-bottom: none !important;
  }
}
:deep(.ant-table-thead) {
  th {
    background: #f4f5fa !important;
  }
}
</style>
