<template>
  <div class="switch-details">
    <CommonPage title="开关机明细">
      <div class="details-content">
        <div class="details-header-form ml-16px">
          <div class="from-item">
            <div class="label">云电脑名称</div>
            <a-input v-model:value="params.desktop_name" @blur="handleInputBlur"  placeholder="请输入云电脑名称" class="w-220px" />
          </div>
          <div class="from-item" style="margin-right: 16px;">
            <div class="label">终端类型</div>
            <a-select v-model:value="params.terminalType" @change="handleSelectChange" class="w-220px" placeholder="请选择终端类型">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="WEB">Web</a-select-option>
              <a-select-option value="windows">Windows</a-select-option>
              <a-select-option value="macOS">Mac</a-select-option>
            </a-select>
          </div>
          <!-- <div class="from-item">
            <div class="label">登录日期</div>
            <a-range-picker />
          </div> -->
          <div class="from-item">
            <a-space>  
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <SearchOutlined/>
                </template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined/>
                </template>
                重置
              </a-button>
            </a-space>
          </div>
        </div>
        <div class="mt-30px">
          <a-table
            :columns="columns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :row-class-name="
              (_record, index) => (index % 2 === 1 ? 'table-striped' : null)
            "
            @change="handleTableChange"
          >
          </a-table>
        </div>
      </div>
    </CommonPage>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import CommonPage from "@/components/commom-page/index.vue";
import { getRunRecordsListApi } from "@/api/common/list.ts";
const columns = [
  {
    title: "登录账号",
    dataIndex: "phone",
    key: "phone",
  },
  {
    title: "云电脑名称",
    dataIndex: "desktopName",
    key: "desktopName",
  },
  {
      title: "终端类型",
      key: "terminalType",
      dataIndex: "terminalType",
      customRender: ({ text }) => {
          return text || "WEB";
      },
  },
  {
    title: "开机时间",
    dataIndex: "onTime",
    key: "onTime",
  },
  {
    title: "关机时间",
    key: "offTime",
    dataIndex: "offTime",
    customRender: ({ text }) => {
      return text ? text : "/";
    },
  },
  {
    title: "使用时长",
    key: "timeConsume",
    dataIndex: "timeConsume",
  },
];

const tableData = ref([]);
const tableLoading = ref(false);
const params = reactive({
  desktop_name: undefined,
  terminalType: "",
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ["10", "20", "50"],
});
const getList = async () => {
  let param = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    ...params
  };
  tableLoading.value = true;
  let res = await getRunRecordsListApi(param);
  tableLoading.value = false;
  if (res.code) {
    console.log(res.data);
    tableData.value = res.payload;
    pagination.total = res.pageInfo.total;
  }
};

// 表格分页变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    getList()
}

const handleSearch = () => {
  getList();
};

const handleReset = () => {
  params.desktop_name = undefined;
  params.terminalType = '';
  params.startDateTime = undefined;
  params.endDateTime = undefined;
  getList();
};


const handleSelectChange = () => {
  getList();
};



onMounted(() => {
  getList();
});
</script>

<style scoped lang='less'>
.switch-details {
  padding: 20px;
  .details-content {
    padding: 20px;
    width: 100%;
    .details-header-form {
      width: 100%;
      display: flex;
      .from-item {
        display: flex;
        align-items: center;
        margin-right: 32px;
        .label {
          margin-right: 16px;
        }
        :deep(.ant-input) {
          background: #f7f8fa;
          border: none;
        }
        :deep(.ant-select-selector) {
          background: #f7f8fa;
          border: none;
        }
        :deep(.ant-picker) {
          background: #f7f8fa;
          border: none;
        }
      }
    }
  }
}

:deep(.table-striped) td {
  background-color: #fafafa;
}
:deep(.ant-table-tbody) {
  td {
    border-top: none !important;
    border-bottom: none !important;
  }
}
:deep(.ant-table-thead) {
  th {
    background: #f4f5fa !important;
  }
}
</style>
