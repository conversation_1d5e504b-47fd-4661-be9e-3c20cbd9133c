<script setup lang="ts">
import {ROOT_ROUTE_REDIRECT_PATH} from '~/router/constant.ts'

const router = useRouter()

function back() {
    router.replace({
        path: ROOT_ROUTE_REDIRECT_PATH,
    })
}
</script>

<template>
    <a-result status="404" title="404" sub-title="Sorry, the page you visited does not exist.">
        <template #extra>
            <a-button type="primary" @click="back">
                Back Home
            </a-button>
        </template>
    </a-result>
</template>
