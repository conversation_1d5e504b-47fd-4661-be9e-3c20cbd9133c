<script setup lang="ts">
// @ts-ignore 以下导入在模板中使用，不需要标记未使用
import {MobileOutlined} from "@ant-design/icons-vue";
import {delayTimer} from "@v-c/utils";
import {AxiosError} from "axios";
import {SMS_CHANNEL} from "@/api/common/auth";
import {getQueryParam, setCookie} from "@/utils/tools";
import type {LoginVerifyCodeParams} from "@/api/common/login";
import {loginApi} from "@/api/common/login";
import {handleOperateElectron, phoneRegExp} from "@/utils/common-utils";

const userStore = useUserStore();

const notification = useNotification();
const message = useMessage();
const router = useRouter();
const route = useRoute();
const loginModel = reactive({
    phone: undefined,
    code: undefined,
    type: "mobile", // 默认为验证码登录
    remember: false,
});

const isNestedElectron = route.query.isNestedElectron;
const urlCode = route.query.urlCode;
const urlCode1 = localStorage.getItem('urlCode')

// 登录方式切换
const loginType = ref("account"); // account: 账号密码登录，wechat: 微信扫码登录

const handleLogoClick = () => {
  window.open('https://www.iqinghu.com', '_self')
}

const formRef = shallowRef();
const resetCounter = 60;
const submitLoading = shallowRef(false);
const errorAlert = shallowRef(false);
const {pause} = useInterval(1000, {
    controls: true,
    immediate: false,
    callback(count: number) {
        if (count) {
            if (count === resetCounter) pause();
        }
    },
});

// 修改账号类型选择相关状态
const showAccountTypeSelection = ref(false);
// 移除 selectedAccountType，默认固定为个人账号
// const selectedAccountType = ref('personal');

// 添加一个标记，跟踪验证码是否已经发送
const verifyCodeSent = ref(false);

// 登录方式切换
function toggleLoginType() {
    loginType.value = loginType.value === "account" ? "wechat" : "account";
}

// 尝试不同的事件处理函数名
function handleVerifyCodeSent() {
    console.log("验证码发送成功");
    verifyCodeSent.value = true;
}

async function submit() {
    // 检查是否同意协议
    if (!loginModel.remember) {
        message.error("请阅读并同意用户协议");
        return;
    }

    // 简化验证码检查，只检查是否输入了验证码
    // 由于事件监听可能有问题，暂时只检查验证码是否输入
    if (!loginModel.code) {
        message.error("请输入验证码");
        return;
    }

    submitLoading.value = true;
    try {
        await formRef.value?.validate();

        // 使用新的登录接口参数
        const params: LoginVerifyCodeParams = {
            signInType: "VERIFY_CODE",
            username: loginModel.phone,
            verifyCode: loginModel.code,
            urlCode: urlCode || urlCode1,
            inviteCode: ""
        };

        // 调用登录API
        const response = await loginApi(params);

        submitLoading.value = false;

        if (!response.success) {
            notification.error({
                message: "登录失败",
                description: response.message || "登录失败，请稍后再试",
                duration: 3,
            });
            return;
        }

        // 如果是新注册用户，触发百度统计转化
        if (response.payload?.isFirstRegister) {
            window._agl && window._agl.push(['track', ['success', {t: 3}]]);
        }

        // 登录成功，设置token
        await userStore?.setAccessToken(response.payload?.accessToken);

        // 保存登录接口返回的用户信息
        await userStore?.saveLoginUserInfo(response.payload);

        // 获取用户详情信息
        let userInfo = await userStore?.getUserInfo();
        console.log("userInfo", userInfo);

        // 如果是首次注册用户，显示账号类型选择
        if (userInfo?.isFirstRegister) {
            showAccountTypeSelection.value = true;
            return; // 不跳转页面，等待用户选择账号类型
        }
        if (isNestedElectron) {
          if (window?.electron) {
            window.electron.ipcRenderer.invoke("auth:login-success",response.payload)
          }
          return
        }

        notification.success({
            message: "登录成功",
            description: `欢迎回来，${response.payload?.nickname || '青虎云用户'}！`,
            duration: 3,
        });

        // 获取当前是否存在重定向的链接，如果存在就走重定向的地址
        const redirect = getQueryParam("redirect", "/");
        router.push(redirect);
    } catch (e) {
        if (e instanceof AxiosError) errorAlert.value = true;
        submitLoading.value = false;
    }
}

async function validPhone() {
  try {
    await formRef.value.validate(["phone"]);
    return true
  } catch (error) {
    return false
  }
}

// 初始化渠道码
function initRegCode() {
    const query = route.query;
    if (query.code) {
        const exp = new Date();
        exp.setTime(exp.getTime() + 7 * 24 * 60 * 60 * 1000 * 100000);
        setCookie("regCode", query.code, exp);
    }
}

// 账号类型提交处理函数
async function submitAccountType() {
    submitLoading.value = true;
    try {
        // TODO: 调用更新用户账号类型的接口
        // const response = await updateUserAccountTypeApi({
        //     accountType: 'personal' // 固定为个人账号
        // });

        // 接口调用成功后，更新本地用户信息
        if (userStore.userInfo) {
            userStore.userInfo.isFirstRegister = false;
            // 更新存储
            await userStore.getUserInfo(); // 重新获取用户信息并更新存储
        }

        // 隐藏账号类型选择
        showAccountTypeSelection.value = false;

        // 获取当前是否存在重定向的链接，如果存在就走重定向的地址
        const redirect = getQueryParam("redirect", "/");
        router.push(redirect);

        submitLoading.value = false;
    } catch (error) {
        submitLoading.value = false;
        notification.error({
            message: "设置账号类型失败",
            description: "请稍后重试",
            duration: 3,
        });
    }
}

onMounted(async () => {
    await delayTimer(300);
    initRegCode();
});

const isShow = ref(false);

</script>

<template>
    <div class="qinghu-login-page">

      <div v-if="isNestedElectron" class="w-full h-15 flex justify-end items-center px-12 pos-absolute top-0 left-0 bg-transparent z-3 app-region-drag">
        <div class="flex items-center gap-2 app-region-no-drag" @mouseleave="isShow = false" @mouseenter="isShow = true">
          <div class="w-14px h-14px flex-center bg-[#F4BE4F] border-1px border-solid border-#C3983F rounded-full cursor-pointer"
               @click="handleOperateElectron('login:window-minimize')">
            <img  v-if="isShow" src="@/assets/images/min.webp" class="w-8px h-1px" alt="">
          </div>
          <div class="w-14px h-14px flex-center bg-[#64c956] border-1px border-solid border-#48b33b rounded-full cursor-pointer"
               @click="handleOperateElectron('login:window-maximize')">
            <img  v-if="isShow" src="@/assets/images/max.webp" class="w-7px h-7px" alt="">
          </div>
          <div
              class="w-14px h-14px flex-center bg-[#EC6A5E] border-1px border-solid border-#BD564C rounded-full cursor-pointer"
              @click="handleOperateElectron('login:window-close')">
            <img  v-if="isShow" src="@/assets/images/close.webp" class="w-10px h-10px" alt="">
          </div>
        </div>
      </div>


        <!-- 页面头部 - logo -->
        <div class="login-header">
            <div class="logo-container" @click="handleLogoClick" style="cursor: pointer">
                <img src="@/assets/images/logo.png" alt="青虎云电脑" class="logo-image" />
                <div class="logo-text-container">
                    <span class="logo-text">青虎云电脑</span>
                    <span class="logo-desc">一站式青虎云电脑平台</span>
                </div>
            </div>
        </div>

        <!-- 登录页面主体内容 -->
        <div class="login-container">
            <!-- 左侧产品介绍 -->
            <!-- <div class="product-intro">
                <h1 class="intro-title">
                  <span class="title-part">青虎云</span>
                  <span class="sub-title">电脑</span>
                </h1>
                <p class="intro-subtitle">高性能云电脑，为电商客户提供全场景一体化解决方案</p>

                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">随时随地一键连接，3秒上云</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">多系统支持 Windows、macOS、iOS、Android 等</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">本地电脑无远程便捷管理店铺</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">异地安全办公，数据双重加密防关联</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">电商店铺多开、账号安全运营</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">IP隔离，每一台电脑都有<b class="text-red-500">独立IP</b>，比指纹浏览器更安全</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon check-icon"></div>
                        <span class="feature-text">青虎AI智能体，云端自动化科技</span>
                    </div>
                </div>
            </div> -->
            <div class="login-container-form">

              <div class="login-container-carousel">
                 <a-carousel autoplay>
                  <div><img src="@/assets/images/login-carousel.webp" alt="" class="carousel-img"/></div>
                </a-carousel>
              </div>

              <!-- 右侧登录表单 -->
              <div class="login-form-container">
                  <!-- 登录表单，当显示账号类型选择时隐藏 -->
                  <div class="login-form-box" v-if="loginType === 'account' && !showAccountTypeSelection">
                      <!-- 切换登录方式按钮 -->
                      <!-- <div class="login-type-switch">
                          <div class="wechat-login-btn" @click="toggleLoginType">
                              <img src="@/assets/images/icon-wechat.png" alt="扫码" class="wechat-icon" />
                              <span>微信扫码登录</span>
                              <div class="arrow"></div>
                          </div>
                          <img src="@/assets/images/icon-qr-code.png" alt="扫码" class="qrcode-icon" @click="toggleLoginType" />
                      </div> -->

                      <h2 class="login-title">账号登录</h2>
                      <p class="login-subtitle">未注册手机号登录时，会自动创建账号</p>

                      <!-- 登录表单 -->
                      <a-form ref="formRef" :model="loginModel" class="login-form">
                          <!-- 判断是否存在error -->
                          <a-alert
                              v-if="errorAlert"
                              class="error-alert"
                              :message="'账号或验证码错误'"
                              type="error"
                              show-icon
                          />

                          <!-- 手机号输入框 -->
                          <a-form-item
                              name="phone"
                              :rules="[
                                  { required: true, message: '请输入手机号' },
                                  {
                                      pattern: phoneRegExp,
                                      message: '请输入正确的手机号',
                                  },
                              ]"
                          >
                              <div class="phone-input-container">
                                  <div class="country-code">
                                      <span class="code-text">中国大陆+86</span>
                                      <span class="code-arrow">▼</span>
                                  </div>
                                  <a-input
                                      v-model:value="loginModel.phone"
                                      :maxlength="11"
                                      allow-clear
                                      placeholder="请输入手机号"
                                      size="large"
                                      @press-enter="submit"
                                      class="phone-input"
                                  />
                              </div>
                          </a-form-item>

                          <!-- 验证码输入框 -->
                          <a-form-item name="code" :rules="[{ required: true, message: '验证码必填' }]">
                              <div class="code-input-container">
                                  <a-input
                                      v-model:value="loginModel.code"
                                      :maxlength="6"
                                      autocomplete="off"
                                      class="code-input"
                                      allow-clear
                                      placeholder="请输入验证码"
                                      size="large"
                                      @press-enter="submit"
                                  />
                                  <SmsCode
                                      :phone="loginModel.phone"
                                      autocomplete="off"
                                      :channel-type="SMS_CHANNEL.CHANNEL_LOGIN"
                                      :send-valid-methods="validPhone"
                                      @send="handleVerifyCodeSent"
                                      @send-success="handleVerifyCodeSent"
                                      @success="handleVerifyCodeSent"
                                      @sent="handleVerifyCodeSent"
                                      @code-sent="handleVerifyCodeSent"
                                  />
                              </div>
                          </a-form-item>

                          <!-- 登录按钮 -->
                          <a-button
                              type="primary"
                              block
                              :loading="submitLoading"
                              size="large"
                              @click="submit"
                              class="submit-btn"
                          >
                              登录 / 注册
                            <div class="btn-tag">领取10元无门槛优惠券</div>

                          </a-button>

                          <!-- 底部协议 -->
                          <div class="agreement">
                              <a-checkbox v-model:checked="loginModel.remember">
                                  <span>登录即代表阅读并同意</span>
                                  <a href="/workbench/user-agreement.html" target="_blank" class="agreement-link">《用户协议》</a>
                                  <span>和</span>
                                  <a href="/workbench/privacy-policy-part.html" target="_blank" class="agreement-link">《隐私政策》</a>
                              </a-checkbox>
                          </div>
                      </a-form>
                  </div>

                  <!-- 微信扫码登录，当显示账号类型选择时隐藏 -->
                  <div class="wechat-login-container" v-if="loginType !== 'account' && !showAccountTypeSelection">
                      <!-- 切换登录方式按钮 -->
                      <div class="login-type-switch">
                          <button @click="toggleLoginType" class="account-login-btn">
                              账号密码登录
                          </button>
                      </div>

                      <h2 class="login-title">扫码登录</h2>

                      <div class="qrcode-container">
                          <!-- 二维码图片容器，二维码资源待提供 -->
                          <div class="qrcode-box">
                              <!-- 二维码图片待提供 -->
                              <div class="qrcode-placeholder"></div>
                          </div>
                          <p class="qrcode-tip">请用微信扫码</p>
                      </div>
                  </div>

                  <!-- 新用户账号类型选择界面 -->
                  <div class="login-form-container account-type-container" v-if="showAccountTypeSelection">
                      <div class="login-form-box">
                          <h2 class="login-title">选择账号类型</h2>

                          <div class="account-type-options">
                              <!-- 个人账号选项 -->
                              <div
                                  class="account-type-option selected"
                              >
                                  <div class="option-content">
                                      <div class="option-icon">
                                          <img src="@/assets/images/icon-person.png" alt="个人账号" />
                                      </div>
                                      <div class="option-info">
                                          <div class="option-title">个人账号</div>
                                          <div class="option-desc">享受提升工作的云端服务</div>
                                      </div>
                                  </div>
                                  <div class="check-mark">
                                      <svg viewBox="64 64 896 896" data-icon="check" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                          <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"></path>
                                      </svg>
                                  </div>
                              </div>

                              <!-- 企业账号选项 -->
                              <div
                                  class="account-type-option disabled"
                                  @click="message.info('企业账号功能正在开发中，敬请期待')"
                              >
                                  <div class="option-content">
                                      <div class="option-icon enterprise-icon">
                                          <img src="@/assets/images/icon-company.png" alt="企业账号" />
                                      </div>
                                      <div class="option-info">
                                          <div class="option-title">企业账号</div>
                                          <div class="option-desc">享受提升工作的云端服务</div>
                                      </div>
                                  </div>
                                  <div class="tag">开发中</div>
                              </div>
                          </div>

                          <!-- 提交按钮 -->
                          <a-button
                              type="primary"
                              block
                              :loading="submitLoading"
                              size="large"
                              @click="submitAccountType"
                              class="submit-btn"
                          >
                              提交
                          </a-button>
                      </div>
                  </div>
              </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.qinghu-login-page {
  width: 100vw;
  height: 100vh;
  background-image: url('@/assets/images/bg-login.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.login-header {
  width: 100%;
  height: 80px;
  padding: 0 50px;
  display: flex;
  align-items: center;

  .logo-container {
    display: flex;
    align-items: center;

    .logo-image {
      width: 40px;
      height: auto;
      margin-right: 10px;
    }

    .logo-text-container {
      display: flex;
      flex-direction: column;

      .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        line-height: 1.2;
      }

      .logo-desc {
        font-size: 12px;
        color: #666;
        line-height: 1.2;
      }
    }
  }
}

.login-container {
  
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  // padding: 0 50px;
  .login-container-form{
    width: 920px;
    height: 504px;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: -120px;
  }
}

// 左侧产品介绍
.product-intro {
  flex: 1;
  max-width: 500px;

  .intro-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 15px;
    .title-part {
      background: linear-gradient(to right, #00ABF7, #00D492);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .sub-title{
      color: #000;
    }
  }


  .intro-subtitle {
    font-size: 16px;
    color: #333;
    margin-bottom: 40px;
  }

  .feature-list {
    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      margin-top: 20px;

      .feature-icon {
        width: 16px;
        height: 16px;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAUtJREFUOE+lkztLA1EQhc/cbHazAcFOtLWzjo2RJVtpIQhCsBEllVhqscHuliaiVlpZCHZiUFLYSZb4qAT/gHXUxgcEspvHHdkiUdS8p70z3zDnnEsYsmjIeQwGuM1MYDZdCpb3B3iQUb0cPRSA8BLOWl8A4y47iTryAD/7qrIAW3o9A7RiNq4x8gy8+Coch7350dSu6wlGcWcOii4B+GAV8+3tp5/CtwB6cXeFlFr3NSw3BdLcjB0CrogoooiTVSt9/tu1FsBws0dE2ABQaghaAhqeUOKGgBHFfFpNpFf/s/z7BCmFnjCPBVGKmT0ivAM0DuZXTw9PYWbrrTMgeD1Lhoyx6RwBi83mBiFVs5yTdoH7K2JBRgwyL4honpkffbcSg5Sqd0DQeb9v6rX6NQh7VcvJdYp7exsLB6PAZxm2rA8G6PGXdQ1SN84XdsRqEWXr9eMAAAAASUVORK5CYII=');
        background-size: cover;
        background-repeat: no-repeat;
        margin: 0 10px 0 0;
      }

      .feature-text {
        font-size: 14px;
        color: #555;
        line-height: 1.6;
      }
    }
  }
}
.login-container-carousel{
   width: 460px;
  height: 100%;
  .carousel-img{
    width: 100%;
    height: 100%;
  }
}
// 右侧登录框
.login-form-container {
  width: 460px;
  height: 100%;
  background: #fff;
  // border-radius: 10px;
  // box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  position: relative;
}

.login-form-box {
  padding: 30px;

  .login-type-switch {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;

    .wechat-login-btn {
      position: relative;
      display: flex;
      align-items: center;
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 6px 12px;
      cursor: pointer;
      margin-right: 5px;
      margin-top: -37px;

      .wechat-icon {
        width: 15px;
        height: 15px;
        margin-right: 5px;
      }

      span {
        font-size: 12px;
        color: #333;
      }

      .arrow {
        position: absolute;
        top: 50%;
        right: -6px;
        transform: translateY(-50%) rotate(45deg);
        width: 10px;
        height: 10px;
        background-color: white;
        border-top: 1px solid #ddd;
        border-right: 1px solid #ddd;
        box-shadow: 2px -2px 3px rgba(0, 0, 0, 0.05);
      }

      &:hover {
        opacity: 0.8;
      }
    }
    .qrcode-icon {
      width: 64px;
      height: 64px;
      cursor: pointer;
      margin: -10px -12px 0 0;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .login-title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 30px 0 10px 0;
    text-align: center;
  }

  .login-subtitle {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
    text-align: center;
  }

  .login-form {
    .error-alert {
      margin-bottom: 20px;
    }

    .phone-input-container {
      display: flex;
      align-items: center;

      .country-code {
        min-width: 120px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        border: 1px solid #d9d9d9;
        border-right: none;
        border-radius: 4px 0 0 4px;

        .code-text {
          font-size: 14px;
          color: #999;
        }

        .code-arrow {
          font-size: 12px;
          color: #999;
        }
      }

      .phone-input {
        flex: 1;
        border-radius: 0;
        border-left: 1px solid #ccc;

        :deep(.ant-input) {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          border-color: #d9d9d9;
        }
      }
    }

    .code-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .code-input {
        flex: 1;
        max-width: calc(100% - 130px);

        :deep(.ant-input) {
          border-color: #d9d9d9;
        }
      }
    }

    .forget-password {
      display: flex;
      justify-content: flex-end;
      margin: 10px 0 20px;

      .reset-pwd-link {
        color: #00a7fa;
        font-size: 14px;
      }
    }

    .submit-btn {
      height: 46px;
      font-size: 16px;
      background: linear-gradient(to right, #00ABF7, #00D492);
      border: none;
      margin-bottom: 15px;
      margin-top: 88px;
      position: relative;
      &:hover {
        background: linear-gradient(to right, #0095e6, #00b87d);
        border: none;
      }

      &[disabled] {
        color: #fff !important;
        opacity: 0.7;
        background: linear-gradient(to right, #00ABF7, #00D492);
      }
      .btn-tag{
        width: 171.5px;
        height: 47px;
        background: url("@/assets/images/common/148665.webp") no-repeat;
        background-size: cover;
        position: absolute;
        top: -26px;
        right: -18px;
        font-size: 16px; 
        color: #FFFFFF;
        text-align: center;
        line-height: 34px;
      }
    }

    .agreement {
      text-align: center;
      color: #666;
      font-size: 12px;

      span {
        color: #666;
        font-size: 12px;
      }

      .agreement-link {
        color: #00a7fa;
      }
    }
  }
}

// 微信扫码登录
.wechat-login-container {
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .login-type-switch {
    position: absolute;
    top: 20px;
    right: 20px;

    .account-login-btn {
      background: none;
      border: none;
      cursor: pointer;
      color: #00a7fa;
      font-size: 14px;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .login-title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin: 30px 0 10px 0;
  }

  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .qrcode-box {
      width: 200px;
      height: 200px;
      padding: 10px;
      border: 1px solid #eee;

      .qrcode-placeholder {
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
      }
    }

    .qrcode-tip {
      margin-top: 30px;
      font-size: 16px;
      color: #333;
    }
  }
}

// 媒体查询，确保在不同设备上都能良好展示
@media (max-width: 1200px) {
  .login-container {
    flex-direction: column;
    padding: 0 20px;
  }

  .product-intro {
    max-width: 100%;
    padding-right: 0;
    margin-bottom: 40px;
    text-align: center;

    .feature-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
    }

    .feature-item {
      width: 45%;
      margin: 0 10px 15px;
    }
  }
}

@media (max-width: 768px) {
  .login-header {
    padding: 0 20px;
    height: 60px;
  }

  .product-intro {
    .intro-title {
      font-size: 28px;
    }

    .feature-item {
      width: 100%;
    }
  }

  .login-form-container {
    width: 100%;
    max-width: 400px;
  }
}

// 账号类型选择样式
.account-type-container {
  margin: 0 auto;
}

.account-type-options {
  margin: 30px 0;
}

.account-type-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }

  &.selected {
    border-color: #00ABF7;
    box-shadow: 0 2px 8px rgba(0, 167, 247, 0.2);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.8;
    border-color: #e8e8e8;

    &:hover {
      box-shadow: none;
      border-color: #e8e8e8;
    }
  }

  .option-content {
    display: flex;
    align-items: center;
  }

  .option-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin-right: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .enterprise-icon {
    background-color: #e6f7ff;

    img {
      width: 80%;
      height: 80%;
      object-fit: contain;
    }
  }

  .option-info {
    .option-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 5px;
    }

    .option-desc {
      font-size: 14px;
      color: #999;
    }
  }

  .check-mark {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #00D492;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
  }

  .tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #ff9500;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
  }
}
</style>
