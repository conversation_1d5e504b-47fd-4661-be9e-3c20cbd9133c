<script setup lang="ts">
import {InsuranceOutlined, LockOutlined, MobileOutlined} from '@ant-design/icons-vue'
import {AxiosError} from 'axios'
import SmsCode from '~/components/sms-code/index.vue'
import {doResetPassword, verifyResetPassword} from '@/api/common/login'
import {SMS_CHANNEL, VERIFY_METHOD} from '@/api/common/auth'
import type {LoginMobileParams, LoginParams} from '@/api/common/login'
import LoginLayout from '~/layouts/login-layout/index.vue'
import {passwordRegExp, phoneRegExp} from '@/utils/common-utils';

const message = useMessage();
const notification = useNotification()
const appStore = useAppStore()
const {layoutSetting} = storeToRefs(appStore)
const router = useRouter()
const resetPasswordModel = reactive({
    password: undefined,
    phone: undefined,
    code: undefined,
    type: VERIFY_METHOD.PHONE_PASSCODE,
})
const {t} = useI18nLocale()
const formRef = shallowRef()
const submitLoading = shallowRef(false)
const errorAlert = shallowRef(false)

async function submit() {
    submitLoading.value = true
    try {
        await formRef.value?.validate()
        const params: LoginParams | LoginMobileParams = {
            phoneNumber: resetPasswordModel.phone,
            passCode: resetPasswordModel.code,
        } as unknown as LoginMobileParams
        const {code, data, success} = await verifyResetPassword({
            verifyMethod: VERIFY_METHOD.PHONE_PASSCODE,
            phonePassCodePayload: params,
        })
        if (!success) {
            notification.error({
                message: '手机验证失败',
                description: '请稍后再试',
                duration: 3,
            })
            submitLoading.value = false
            return
        }

        const resetPayload = await doResetPassword({
            passwordResetToken: data.passwordResetToken,
            password: resetPasswordModel.password,
        })
        if (!resetPayload.success) {
            notification.error({
                message: '重置密码失败',
                description: '请稍后再试',
                duration: 3,
            })
            submitLoading.value = false
            return
        }

        // 此处 setToken 动态绑定storage
        notification.success({
            message: '重置成功',
            description: '请先登录',
            duration: 3,
        })
        setTimeout(() => {
            router.push({
                path: '/login',
                replace: true,
            })
        }, 2000)
    } catch (e) {
        if (e instanceof AxiosError)
            errorAlert.value = true

        submitLoading.value = false
    }
}

function goBackLogin() {
    router.push({
        path: '/login',
        replace: true,
    })
}

async function validPhone() {
    await formRef.value.validate(['phone'])
}
</script>

<template>
    <LoginLayout>
        <div class="login-main-box px-5 w-[400px] flex-center flex-col relative z-11">
            <a-form ref="formRef" w-320px :model="resetPasswordModel">
                <a-tabs v-model:activeKey="resetPasswordModel.type" centered
                        @tab-click="(res) => (res === 'goBack' && (goBackLogin()))">
                    <a-tab-pane :key="VERIFY_METHOD.PASSCODE" :tab="t('pages.login.resetPassword')"/>
                    <a-tab-pane key="goBack" :tab="t('pages.login.goBackLogin')"/>
                </a-tabs>
                <!-- 判断是否存在error -->
                <a-alert v-if="errorAlert && resetPasswordModel.type === VERIFY_METHOD.PHONE_PASSCODE" mb-24px
                         :message="t('pages.login.phoneLogin.errorMessage')" type="error" show-icon/>
                <a-form-item
                        name="phone" :rules="[
            { required: true, message: t('pages.login.phoneNumber.required') },
            {
              pattern: phoneRegExp,
              message: t('pages.login.phoneNumber.invalid'),
            },
          ]"
                >
                    <a-input v-model:value="resetPasswordModel.phone" :maxlength="11" allow-clear
                             :placeholder="t('pages.login.phoneNumber.placeholder')" size="large" @press-enter="submit">
                        <template #prefix>
                            <MobileOutlined/>
                        </template>
                    </a-input>
                </a-form-item>
                <a-form-item name="code" :rules="[{ required: true, message: t('pages.login.captcha.required') }]">
                    <div flex items-center>
                        <a-input
                                v-model:value="resetPasswordModel.code"
                                :maxlength="6" style="flex: 1 1 0%; transition: width 0.3s ease 0s; margin-right: 8px;"
                                autocomplete="off"
                                allow-clear :placeholder="t('pages.login.captcha.placeholder')" size="large"
                                @press-enter="submit"
                        >
                            <template #prefix>
                                <InsuranceOutlined/>
                            </template>
                        </a-input>
                        <SmsCode
                                :phone="resetPasswordModel.phone"
                                autocomplete="off" :channel-type="SMS_CHANNEL.CHANNEL_RESET_PASSWORD"
                                :send-valid-methods="validPhone"
                        />
                    </div>
                </a-form-item>
                <a-form-item
                        name="password" :rules="[{ required: true, message: t('pages.login.password.required') }, {
            pattern: passwordRegExp,
            message: '密码设置应为8~18字符的数字＋字母组合，请重新输入',
          }]"
                >
                    <a-input-password v-model:value="resetPasswordModel.password" :maxlength="18" allow-clear
                                      :placeholder="t('pages.login.password.placeholder')" size="large"
                                      @press-enter="submit">
                        <template #prefix>
                            <LockOutlined/>
                        </template>
                    </a-input-password>
                </a-form-item>
                <a-button type="primary" block :loading="submitLoading" size="large" @click="submit">
                    {{ t('pages.login.register') }}
                </a-button>
            </a-form>
        </div>
    </LoginLayout>
</template>

<style lang="less" scoped>
.login-main-box {
  background: #fff;
  border-radius: 8px;
  padding: 30px 0 40px;
  margin-right: 200px;

  .ant-tabs-nav-list {
    margin: 0 auto;
    font-size: 16px;
  }

  .login-form-other {
    line-height: 22px;
    text-align: center
  }

}

.login-form-main {
  box-shadow: var(--c-shadow);
}

.icon {
  margin-left: 8px;
  color: var(--text-color-2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color .3s;

  &:hover {
    color: var(--cloud-color-primary);
  }
}

.login-media(@width:100%) {
  .login-form-main {
    width: @width;
  }
  .login-main-box {
    width: 100%;
    margin: 0 20px;
  }
  .login-form-desc {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
    .login-media(400px)
}

@media screen and (max-width: 767px) {
    .login-media(350px);
}
</style>
