<script setup lang="ts">
import {ParentCompConsumer} from '~/layouts/basic-layout/parent-comp-consumer'

const appStore = useAppStore()
const {layoutSetting} = storeToRefs(appStore)
</script>

<template>
    <ParentCompConsumer>
        <RouterView>
            <template #default="{ Component, route }">
                <Transition appear :name="layoutSetting.animationName" mode="out-in">
                    <div w-full h-full>
                        <component :is="Component" :key="route.fullPath"/>
                    </div>
                </Transition>
            </template>
        </RouterView>
    </ParentCompConsumer>
</template>
