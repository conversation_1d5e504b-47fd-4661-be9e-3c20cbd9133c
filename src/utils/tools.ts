import {get} from 'lodash-es'
import router from '~/router'

export function getQueryParam(param: string | string[], defaultVal = '') {
    const query = router.currentRoute.value?.query ?? {}
    const val = get(query, param) ?? defaultVal
    return decodeURIComponent(val)
}

/**
 * 获取token
 */
export function getCookie(<PERSON>ie) {
    let sty = ''
    const info = document.cookie
    const cookieArr = info.split('; ')
    let valueArr = []
    for (let i = 0; i < cookieArr.length; i++) {
        valueArr = cookieArr[i].split('=')
        if (valueArr.includes(<PERSON>ie))
            sty = valueArr[1]
    }
    return sty
}

export function setCookie(name, value, exp) {
    if (!exp) {
        const exp = new Date()
        exp.setTime(exp.getTime() + 60 * 60 * 1000 * 100000)
    }
    document.cookie
        = `${name
        }=${
            escape(value)
        };expires=${
            exp.toGMTString()
        };path=`
        + `/`
}

/**
 *存储cookie
 */
export function setCookies(name, value, exp) {
    document.cookie = `${name}=${escape(value)};expires=${exp.toGMTString()};path=` + `/`
}

/**
 *删除cookie
 */
export function delCookie(name) {
    const exp = new Date()
    exp.setTime(exp.getTime() - 1)
    const cval = getCookie(name)
    document.cookie = `${name}=${escape(cval)};expires=${exp?.toGMTString()};path=/`
}
