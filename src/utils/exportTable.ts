// 导出表格主入口
export function exportTable({dataSource, header, exportName}) {
    let tableHeaderContent = ''
    const tableHeaderArray: any = []
    for (const item of header)
        tableHeaderArray.push(`<th colspan="2">${item.title}</th>`)

    const tableHeaderStr = tableHeaderArray.join('')
    tableHeaderContent = `<tr style="text-align: center;">${tableHeaderStr}</tr>`
    let index = 0
    for (const item of dataSource) {
        let tableStr = '<tr style="text-align: center;">'
        for (const headerItem of header) {
            const value = (headerItem.exportFormat ? headerItem.exportFormat({
                text: item[headerItem.key],
                record: item,
                index
            }) : item[headerItem.key]).toString() || '-'
            tableStr += `<td colspan="2">${value}</td>`
        }
        tableStr += '</tr>'
        tableHeaderContent += tableStr
        index++
    }
    setTable(tableHeaderContent, exportName)
}

function setTable(tableStr, exportName) {
    const reg = /,/gm
    tableStr = tableStr.replace(reg, '')
    // Worksheet名
    const worksheet = 'Sheet1'
    const uri = 'data:application/vnd.ms-excel;base64,'
    // 真正要导出（下载）的HTML模板
    const exportTemplate = `<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" 
                    xmlns="http://www.w3.org/TR/REC-html40">
                        <head><meta charset='UTF-8'><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
                            <x:Name>${worksheet}</x:Name>
                                <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
                            </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
                        </head>
                        <body>
                            <table syle="table-layout: fixed;word-wrap: break-word; word-break: break-all;">${tableStr}</table>
                        </body>
                    </html>`
    // 下载模板
    const href = uri + base64(exportTemplate)
    blobDown(href, exportName)
}

// 输出base64编码
function base64(s) {
    return window.btoa(unescape(encodeURIComponent(s)))
}

// 转为blob对象并添加下载链接
function blobDown(url, filename = 'excel') {
    // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
    if (window.navigator.msSaveOrOpenBlob) {
        const bstr = atob(url.split(',')[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--)
            u8arr[n] = bstr.charCodeAt(n)

        const blob = new Blob([u8arr])
        window.navigator.msSaveOrOpenBlob(blob, `${filename}.` + `xls`)
    } else {
        // 这里就按照chrome等新版浏览器来处理
        const a = document.createElement('a')
        a.href = url
        a.setAttribute('download', filename)
        a.click()
    }
}
