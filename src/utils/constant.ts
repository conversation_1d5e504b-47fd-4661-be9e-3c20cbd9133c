// 全局配置信息及静态常量

export enum AccessEnum {
    ADMIN = 'ADMIN',
    USER = 'USER',
}

export enum DynamicLoadEnum {
    FRONTEND = 'FRONTEND', // 前端动态加载菜单，使用这种方式将从dynamic-routes.ts中加载菜单和路由信息
    BACKEND = 'BACKEND', // 后端动态加载菜单， 使用这种方式将从后端接口加载菜单和路由信息
}

export enum STATUS {
    OFF = '0',
    RUNNING = '1',
    ONLINE = '2',
    ERROR = '3',
}

// 更新为腾讯云验证码配置
export enum CaptchaInfo { 
    // 滑块验证码配置信息
    AppId = '195574944', // 腾讯云验证码AppId
    AppSecretKey = 'hChUbLp40cpHmTULCpMoqT5N1', // 腾讯云验证码AppSecretKey
}

// 默认情况下我们提供从后端加载的逻辑
export const DYNAMIC_LOAD_WAY = import.meta.env.VITE_APP_LOAD_ROUTE_WAY ?? DynamicLoadEnum.BACKEND
