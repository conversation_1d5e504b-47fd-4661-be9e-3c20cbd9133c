export const phoneRegExp = /^(86)?1([2-9]{1})[0-9]{9}$/
export const passwordRegExp = /^(?![a-zA-Z]+$)(?![0-9]+$)[A-Za-z0-9]{8,18}$/

/**
 * 格式化金额 单位（分）
 * @param amount 金额
 */
export const formatAmount = (amount: number) => amount ? (amount / 100).toFixed(2) : '0.00'

/**
 * 根据名称获取图片路径
 * @param name 名称
 */
export const getAssetsImgByName =(name: string): string => new URL(`../assets/images/product/${name}`, import.meta.url).href
export const getAssetsByName =(name: string): string => new URL(`../assets/images/${name}`, import.meta.url).href

/**
 * 隐藏手机号中间四位
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export const maskPhone = (phone: string): string => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

export const handleOperateElectron = (channel: string) => {
  if (window?.electron) {
    const params = new URL(location.href).searchParams;
    const childId = params.get('childId');
    window?.electron?.ipcRenderer.invoke(channel, childId)
  }
}

/**
 * 获取当前操作系统类型
 * @returns 'Windows' | 'MacOS' | 'Other'
 */
export const getCurrentOS = (): 'Windows' | 'MacOS' | 'Other' => {
  const ua = navigator.userAgent
  if (/Windows/i.test(ua)) return 'Windows'
  if (/Macintosh|Mac OS X/i.test(ua)) return 'MacOS'
  return 'Other'
}


export function handleCopy (value = '', callback: () => void) {
  let textarea = document.createElement("textarea");
  textarea.value = value;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand("Copy");
  document.body.removeChild(textarea);
  if (callback) {
      callback();
  } else {
      const message = useMessage();
      message.success('复制成功！');
  }
}

export function useTablePagination (config: any) {
  const tablePagination = ref({
      total: 0,
      current: 1,
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: total => `共 ${total} 条`,
      pageSizeOptions: ['10', '20', '30', '50', '100'],
      ...config
  });
  return tablePagination;
}

export function getBase64 (file: Blob | File, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(file);
}