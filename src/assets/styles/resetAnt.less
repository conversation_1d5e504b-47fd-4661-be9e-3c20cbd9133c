.white-header-color .ant-modal {
  .ant-modal-close-x {
    color: #FFFFFF;
  }
}

.ant-modal {
  .ant-modal-content {
    padding: 0;
    border-radius: 12px;
  }

  .ant-modal-header {
    padding: 18px 20px 14px;
    min-height: 50px;
    background: #ffffff;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid #F2F3F5;
    margin: 0;
  }

  .ant-modal-close {
    top: 16px;
    right: 20px;
    color: #1D2129;
  }

  .ant-modal-title {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #1D2129;
  }

  .ant-modal-body {
    padding: 32px;
  }

  .ant-modal-footer {
    padding: 0 0 20px;
  }
}

.ant-table {
  .ant-table-thead {
    .ant-table-cell { // 去除表头分割线
      background: #E5E6EB;
      padding: 8px 16px;

      &:before {
        display: none;
      }
    }
  }
}

.info-btn {
  &.ant-btn {
    background: #F2F3F5;
    color: #4E5969;
    border: none;

    &:hover {
      background: #eaebec;
      color: #4E5969;
    }
  }
}

.primary-bg-btn {
  &.ant-btn {
    background: rgba(8, 193, 138, 0.10);

    &.ant-btn-background-ghost {
      &:hover {
        background: rgba(8, 193, 138, 0.13);
      }
    }

    &:hover {
      background: rgba(8, 193, 138, 0.13);
    }
  }
}

.radio-primary-bg .ant-radio-button-wrapper-checked {
  color: #FFFFFF;
  background: var(--primary-color);

  &:hover {
    color: #FFFFFF;
  }
}

.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
  background: transparent;
}

.pay-modal {

  .ant-modal-body {
    padding: 15px;

    .ant-modal-confirm-body {
      justify-content: center;
    }

  }
}

.recharge-modal {
  .ant-modal-body {
    padding: 0;
    background: #f8f9ff;

    .ant-modal-confirm-body {
      justify-content: center;
    }

    .ant-modal-confirm-title{
      padding: 15px;
      background: #FFF;
    }

    .ant-modal-confirm-content {
      padding: 10px 0 20px 0;
    }
  }
}

.app-region-drag {
  -webkit-app-region: drag;
  app-region: drag;
}

.app-region-no-drag {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}
