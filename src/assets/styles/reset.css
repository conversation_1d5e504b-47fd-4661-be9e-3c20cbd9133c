@import "motion.css";

html {
    --primary-color: var(--cloud-color-primary);
    --text-color: #1D2129;
    --text-color-1: #666666;
    --text-color-2: #999999;
    --bg-color: #fff;
    --hover-color: rgba(255, 255, 255, .025);
    --bg-color-container: #f0f2f5;
    --c-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05);
}

html.dark {
    --text-color: #1D2129;
    --text-color-1: rgba(229, 224, 216, 0.45);
    --text-color-2: rgba(229, 224, 216, 0.45);
    --bg-color: rgb(36, 37, 37);
    --hover-color: rgb(42, 44, 55);
    --bg-color-container: rgb(42, 44, 44);
    --c-shadow: rgba(13, 13, 13, 0.65) 0 2px 8px 0;
}

body {
    color: var(--text-color);
    background-color: var(--bg-color);
    text-rendering: optimizeLegibility;
    overflow: hidden;
}

#app, body, html {
    height: 100%;
}

#app {
    overflow-x: hidden;
}

*, :after, :before {
    box-sizing: border-box;
}

.primary-before-dot {
    display: flex;
    align-items: center;

    &:before {
        display: inline-block;
        content: " ";
        width: 4px;
        height: 13px;
        background: #FB7701;
        border-radius: 2px 2px 2px 2px;
        margin-right: 4px;
        vertical-align: middle;
    }
}