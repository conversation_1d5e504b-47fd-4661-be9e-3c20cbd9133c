export const orderStatusEnum = {
    0: '订单生成',
    1: '支付中',
    2: '支付成功',
    3: '支付失败',
    4: '已撤销',
    5: '已退款',
    6: '订单关闭',
    7: '订单关闭',
}

// 所有实例状态
export const AllStatusMapping = {
    'RUNNING': 'RUNNING', // 运行中 远程中
    'STOPPED': 'STOPPED', // 已关机
    'STARTING': 'STARTING',// 开机中
    'STOPPING': 'STOPPING', // 关机中
    'IDLE': 'IDLE', // 空闲
    'DEFAULT': 'DEFAULT',
    'ACTIVE': 'ACTIVE' // 远程中
}

// 实例到期状态
export const InstanceExpiredMapping = {
    'CAN_BE_RENEWED': 'CAN_BE_RENEWED', // 已过期，可续费
    'CLEAR': 'CLEAR', // 已过期，不可续费
    'DUE': 'DUE', // 正常
}


export const EipTypeMapping = {
    'RENEWAL': 'RENEWAL', // 续费
    'NEW': 'NEW', // 新购
}

export const EipTypeTextEnum: any = {
    'RENEWAL': '续费', // 续费
    'NEW': '新购', // 新购
}

export enum appInfoEnum {
    appId = 46,
    tenantId = 46,
    appName = 'qinghuyun'
}




  
// 后台设置的全部达秘套餐 (此处必须与crm后台设置的套餐一致)
export const qinghuSetMealListByCrm = [
    {
      id: '26',
      name: '试用版'
    },
    {
      id: '27',
      name: '入门版月度'
    },
    {
      id: '28',
      name: '入门版年度'
    },
    {
      id: '29',
      name: '运营版年度'
    },
    {
      id: '30',
      name: '专业版年度'
    },
    {
      id: '31',
      name: '至尊版年度'
    }
  ]
  
  // 账户类型 (包含：1：公司，2：个人)
export enum accountTypeEnum {
    COMPANY = 1,
    PERSONAL = 2
  }